#!/usr/bin/env node

/**
 * Email processor script called by Postfix
 * This script reads raw email from stdin and forwards it to the main application
 * 
 * Updated for /opt/eu-email-webhook path standardization
 */

import http from 'http';

// Configuration - use environment variables or defaults
const APP_HOST = process.env.APP_HOST || 'localhost';
const APP_PORT = process.env.APP_PORT || 3000;
const TIMEOUT_MS = parseInt(process.env.WEBHOOK_TIMEOUT_MS) || 30000;

// Read email from stdin (pipe from Postfix)
let rawEmail = '';

process.stdin.setEncoding('utf8');
process.stdin.on('data', (chunk) => {
  rawEmail += chunk;
});

process.stdin.on('end', async () => {
  try {
    console.log(`[${new Date().toISOString()}] Processing email (${rawEmail.length} bytes)`);
    
    // Forward to main application
    const options = {
      hostname: APP_HOST,
      port: APP_PORT,
      path: '/api/email/process',
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Content-Length': Buffer.byteLength(rawEmail),
        'X-Email-Source': 'postfix',
        'X-Processing-Script': '/opt/eu-email-webhook/scripts/production/process-email.js',
      },
      timeout: TIMEOUT_MS,
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`[${new Date().toISOString()}] Email processed successfully (${res.statusCode})`);
          process.exit(0);
        } else {
          console.error(`[${new Date().toISOString()}] Email processing failed: ${res.statusCode} ${responseData}`);
          process.exit(1);
        }
      });
    });

    req.on('timeout', () => {
      console.error(`[${new Date().toISOString()}] Email processing timeout after ${TIMEOUT_MS}ms`);
      req.destroy();
      process.exit(1);
    });

    req.on('error', (error) => {
      console.error(`[${new Date().toISOString()}] Failed to forward email:`, error.message);
      process.exit(1);
    });

    req.write(rawEmail);
    req.end();

  } catch (error) {
    console.error(`[${new Date().toISOString()}] Email processing error:`, error.message);
    process.exit(1);
  }
});

process.stdin.on('error', (error) => {
  console.error(`[${new Date().toISOString()}] Failed to read email from stdin:`, error.message);
  process.exit(1);
});

// Handle process termination gracefully
process.on('SIGTERM', () => {
  console.log(`[${new Date().toISOString()}] Received SIGTERM, shutting down gracefully`);
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log(`[${new Date().toISOString()}] Received SIGINT, shutting down gracefully`);
  process.exit(0);
});
