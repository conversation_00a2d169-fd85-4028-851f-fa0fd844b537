#!/bin/bash

# Production Postfix Manager Rebuild Script
# This script rebuilds and redeploys the postfix-manager service with the latest spam filtering code

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

APP_DIR="/opt/eu-email-webhook"
BACKUP_DIR="/opt/eu-email-webhook/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${GREEN}🔧 Production Postfix Manager Rebuild${NC}"
echo "=================================================="
echo -e "${YELLOW}⚠️  This will rebuild and restart the postfix-manager service${NC}"
echo -e "${YELLOW}⚠️  Brief service interruption expected${NC}"
echo ""

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]]; then
    echo -e "${RED}❌ This script must be run as root or with sudo${NC}"
    exit 1
fi

# Check if we're in the correct directory
if [[ ! -f "$APP_DIR/docker-compose.prod.yml" ]]; then
    echo -e "${RED}❌ Production docker-compose.prod.yml not found at $APP_DIR${NC}"
    echo "Please ensure you're running this on the production server"
    exit 1
fi

cd "$APP_DIR"

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo -e "${YELLOW}1. Checking current service status...${NC}"
docker compose -f docker-compose.prod.yml ps postfix-manager

echo ""
echo -e "${YELLOW}2. Testing current spam filter endpoint...${NC}"
if curl -s --connect-timeout 5 "http://localhost:3001/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Service is reachable${NC}"

    # Test spam filter endpoint
    TEST_RESPONSE=$(curl -s -w "%{http_code}" -X PUT "http://localhost:3001/domains/test.example.com/spam-filter" \
        -H "Content-Type: application/json" \
        -d '{"enabled":true}' 2>/dev/null || echo "000")
    
    HTTP_CODE="${TEST_RESPONSE: -3}"
    
    if [ "$HTTP_CODE" = "404" ]; then
        echo -e "${RED}❌ Spam filter endpoint missing (404) - rebuild needed${NC}"
    elif [ "$HTTP_CODE" = "200" ]; then
        echo -e "${GREEN}✅ Spam filter endpoint working - rebuild may not be needed${NC}"
        echo -e "${YELLOW}💡 Continue anyway? (y/N)${NC}"
        read -r CONTINUE
        if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
            echo "Aborted by user"
            exit 0
        fi
    else
        echo -e "${YELLOW}⚠️  Unexpected response (HTTP $HTTP_CODE) - proceeding with rebuild${NC}"
    fi
else
    echo -e "${RED}❌ Service not reachable - proceeding with rebuild${NC}"
fi

echo ""
echo -e "${YELLOW}3. Creating backup of current container...${NC}"
# Export current container as backup
CONTAINER_ID=$(docker compose -f docker-compose.prod.yml ps -q postfix-manager)
if [[ -n "$CONTAINER_ID" ]]; then
    docker commit "$CONTAINER_ID" "postfix-manager-backup:$TIMESTAMP"
    echo -e "${GREEN}✅ Backup created: postfix-manager-backup:$TIMESTAMP${NC}"
else
    echo -e "${YELLOW}⚠️  No running container found to backup${NC}"
fi

echo ""
echo -e "${YELLOW}4. Pulling latest code...${NC}"
git fetch origin main
git reset --hard origin/main
echo -e "${GREEN}✅ Code updated to latest${NC}"

echo ""
echo -e "${YELLOW}5. Rebuilding postfix-manager service...${NC}"
# Force rebuild without cache to ensure latest code
docker compose -f docker-compose.prod.yml build --no-cache postfix-manager
echo -e "${GREEN}✅ Service rebuilt${NC}"

echo ""
echo -e "${YELLOW}6. Stopping current service...${NC}"
docker compose -f docker-compose.prod.yml stop postfix-manager
echo -e "${GREEN}✅ Service stopped${NC}"

echo ""
echo -e "${YELLOW}7. Starting updated service...${NC}"
docker compose -f docker-compose.prod.yml up -d postfix-manager
echo -e "${GREEN}✅ Service started${NC}"

echo ""
echo -e "${YELLOW}8. Waiting for service to be ready...${NC}"
sleep 10

# Wait for health check
for i in {1..30}; do
    if curl -s --connect-timeout 2 "http://localhost:3001/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Service is healthy${NC}"
        break
    fi
    echo -n "."
    sleep 2
done

echo ""
echo -e "${YELLOW}9. Testing spam filter endpoint...${NC}"
TEST_RESPONSE=$(curl -s -w "%{http_code}" -X PUT "http://localhost:3001/domains/test.example.com/spam-filter" \
    -H "Content-Type: application/json" \
    -d '{"enabled":true}' 2>/dev/null || echo "000")

HTTP_CODE="${TEST_RESPONSE: -3}"
RESPONSE_BODY="${TEST_RESPONSE%???}"

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Spam filter endpoint working!${NC}"
    echo -e "${YELLOW}📊 Response:${NC}"
    echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"
elif [ "$HTTP_CODE" = "404" ]; then
    echo -e "${RED}❌ Spam filter endpoint still missing (404)${NC}"
    echo -e "${YELLOW}💡 The endpoint may not be in the latest code or there's a build issue${NC}"
    echo -e "${YELLOW}💡 Check the main.go file for the spam filter route${NC}"
else
    echo -e "${YELLOW}⚠️  Unexpected response (HTTP $HTTP_CODE)${NC}"
    echo "Response: $RESPONSE_BODY"
fi

echo ""
echo -e "${YELLOW}10. Final service status...${NC}"
docker compose -f docker-compose.prod.yml ps postfix-manager

echo ""
echo -e "${GREEN}🏁 Rebuild completed!${NC}"
echo ""
echo -e "${YELLOW}📊 Service Information:${NC}"
echo "  Health: curl http://postfix-manager:3001/health"
echo "  Status: curl http://postfix-manager:3001/status"
echo "  Logs: docker compose -f docker-compose.prod.yml logs -f postfix-manager"
echo ""
echo -e "${YELLOW}🧪 Test spam filtering:${NC}"
echo "  curl -X PUT 'http://postfix-manager:3001/domains/YOUR_DOMAIN/spam-filter' \\"
echo "       -H 'Content-Type: application/json' \\"
echo "       -d '{\"enabled\":true}'"
echo ""
echo -e "${YELLOW}🔄 Rollback if needed:${NC}"
echo "  docker tag postfix-manager-backup:$TIMESTAMP postfix-manager:latest"
echo "  docker compose -f docker-compose.prod.yml up -d postfix-manager"
