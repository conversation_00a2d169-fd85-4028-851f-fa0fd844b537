#!/bin/bash

# Setup test database for running tests
set -e

echo "🧪 Setting up test database..."

# Load environment variables
if [ -f .env ]; then
    set -a
    source .env
    set +a
fi

# Extract database connection details
# For tests, always use localhost (not Docker container name)
DB_HOST=localhost
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-password}
DB_NAME=${DB_NAME:-eu_email_webhook}
TEST_DB_NAME="${DB_NAME}_test"

echo "📊 Database details:"
echo "  Host: $DB_HOST"
echo "  User: $DB_USER"
echo "  Production DB: $DB_NAME"
echo "  Test DB: $TEST_DB_NAME"

# Check if PostgreSQL is running
if ! pg_isready -h $DB_HOST -U $DB_USER > /dev/null 2>&1; then
    echo "❌ PostgreSQL is not running or not accessible"
    echo "   Please start PostgreSQL and ensure it's accessible at $DB_HOST"
    exit 1
fi

echo "✅ PostgreSQL is running"

# Create test database if it doesn't exist
echo "🔧 Creating test database if it doesn't exist..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d postgres -tc "SELECT 1 FROM pg_database WHERE datname = '$TEST_DB_NAME'" | grep -q 1 || \
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USER -d postgres -c "CREATE DATABASE $TEST_DB_NAME"

echo "✅ Test database '$TEST_DB_NAME' is ready"

# Set test database URL
export TEST_DATABASE_URL="************************************************/$TEST_DB_NAME"
export DATABASE_URL="$TEST_DATABASE_URL"

echo "🔄 Running Prisma migrations on test database..."
npx prisma migrate deploy

echo "🔄 Generating Prisma client..."
npx prisma generate

echo "🌱 Seeding test database with minimal data..."
# We don't need to seed the test database as tests create their own data

echo "✅ Test database setup complete!"
echo "   Test Database URL: $TEST_DATABASE_URL"
echo ""
echo "🧪 You can now run tests with: npm test"
