#!/usr/bin/env node

/**
 * Create a test user for dashboard testing
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

const testUser = {
  email: '<EMAIL>',
  password: 'TestPass123!',
  name: 'Test User'
};

async function createTestUser() {
  try {
    console.log('🔧 Creating test user...');
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: testUser.email }
    });
    
    if (existingUser) {
      console.log(`✅ Test user already exists: ${testUser.email}`);
      console.log(`📧 Email: ${testUser.email}`);
      console.log(`🔑 Password: ${testUser.password}`);
      return;
    }
    
    // Create user
    const hashedPassword = await bcrypt.hash(testUser.password, 10);
    const user = await prisma.user.create({
      data: {
        email: testUser.email,
        password: hashedPassword,
        name: testUser.name,
        verified: true
      }
    });
    
    console.log(`✅ Created test user: ${user.email} (ID: ${user.id})`);
    console.log(`📧 Email: ${testUser.email}`);
    console.log(`🔑 Password: ${testUser.password}`);
    console.log(`🌐 Login at: http://localhost:3000/login`);
    
  } catch (error) {
    console.error('❌ Error creating test user:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
