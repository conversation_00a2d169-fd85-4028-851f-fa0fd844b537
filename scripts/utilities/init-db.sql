-- Database initialization script for PostgreSQL
-- This runs automatically when the PostgreSQL container starts for the first time

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create additional indexes for performance (Prisma will create the main tables)
-- These will be applied after Prisma migrations run

-- Note: The main table creation is handled by Prisma migrations
-- This file is for any additional database setup that's needed
