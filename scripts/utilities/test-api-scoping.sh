#!/bin/bash

# Simple test script to verify API key scoping functionality
# Run this after implementing the scoping system

echo "🔐 Testing API Key Scoping System"
echo "=================================="

# Check if jq is available for JSON parsing
if ! command -v jq &> /dev/null; then
    echo "❌ jq is required for this test script. Please install it first."
    exit 1
fi

# Set base URL (modify as needed)
BASE_URL="http://localhost:3000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "📝 Instructions:"
echo "1. Create a full-access API key in the UI first"
echo "2. Enter that API key when prompted"
echo "3. The script will create test keys and verify scoping"
echo ""

# Get full-access API key from user
read -p "Enter your full-access API key: " FULL_ACCESS_KEY

if [[ -z "$FULL_ACCESS_KEY" ]]; then
    echo "❌ API key is required"
    exit 1
fi

echo ""
echo "🧪 Testing scope system..."

# Test 1: Create a read-only API key
echo "Test 1: Creating read-only API key..."
READ_ONLY_RESPONSE=$(curl -s -X POST "$BASE_URL/api/api-keys" \
    -H "Content-Type: application/json" \
    -H "X-API-KEY: $FULL_ACCESS_KEY" \
    -d '{
        "name": "Test Read Only Key",
        "scopes": ["domains:read", "domains:status", "aliases:read", "webhooks:read"],
        "description": "Test key for scope validation"
    }')

if echo "$READ_ONLY_RESPONSE" | jq -e '.apiKey.key' > /dev/null; then
    READ_ONLY_KEY=$(echo "$READ_ONLY_RESPONSE" | jq -r '.apiKey.key')
    echo -e "${GREEN}✓${NC} Read-only API key created: ${READ_ONLY_KEY:0:15}..."
else
    echo -e "${RED}❌${NC} Failed to create read-only API key"
    echo "Response: $READ_ONLY_RESPONSE"
    exit 1
fi

# Test 2: Create an API user key (your requested scope)
echo "Test 2: Creating API user key..."
API_USER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/api-keys" \
    -H "Content-Type: application/json" \
    -H "X-API-KEY: $FULL_ACCESS_KEY" \
    -d '{
        "name": "Test API User Key",
        "scopes": ["domains:read", "domains:status", "domains:config", "aliases:*", "webhooks:*"],
        "description": "Test key for API user scope"
    }')

if echo "$API_USER_RESPONSE" | jq -e '.apiKey.key' > /dev/null; then
    API_USER_KEY=$(echo "$API_USER_RESPONSE" | jq -r '.apiKey.key')
    echo -e "${GREEN}✓${NC} API user key created: ${API_USER_KEY:0:15}..."
else
    echo -e "${RED}❌${NC} Failed to create API user key"
    echo "Response: $API_USER_RESPONSE"
    exit 1
fi

echo ""
echo "🔍 Testing scope restrictions..."

# Test 3: Read-only key should be able to read domains
echo "Test 3: Read-only key accessing GET /api/domains..."
DOMAINS_READ=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$BASE_URL/api/domains" \
    -H "X-API-KEY: $READ_ONLY_KEY")

if [[ "$DOMAINS_READ" == "200" ]]; then
    echo -e "${GREEN}✓${NC} Read-only key can read domains (200)"
else
    echo -e "${RED}❌${NC} Read-only key cannot read domains ($DOMAINS_READ)"
fi

# Test 4: Read-only key should NOT be able to create domains
echo "Test 4: Read-only key trying POST /api/domains..."
DOMAINS_CREATE=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$BASE_URL/api/domains" \
    -H "Content-Type: application/json" \
    -H "X-API-KEY: $READ_ONLY_KEY" \
    -d '{"domain": "test-scope.example.com"}')

if [[ "$DOMAINS_CREATE" == "403" ]]; then
    echo -e "${GREEN}✓${NC} Read-only key correctly blocked from creating domains (403)"
else
    echo -e "${RED}❌${NC} Read-only key should be blocked from creating domains (got $DOMAINS_CREATE)"
fi

# Test 5: API user key should be able to read domains
echo "Test 5: API user key accessing GET /api/domains..."
API_USER_READ=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$BASE_URL/api/domains" \
    -H "X-API-KEY: $API_USER_KEY")

if [[ "$API_USER_READ" == "200" ]]; then
    echo -e "${GREEN}✓${NC} API user key can read domains (200)"
else
    echo -e "${RED}❌${NC} API user key cannot read domains ($API_USER_READ)"
fi

# Test 6: API user key should NOT be able to create domains
echo "Test 6: API user key trying POST /api/domains..."
API_USER_CREATE=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$BASE_URL/api/domains" \
    -H "Content-Type: application/json" \
    -H "X-API-KEY: $API_USER_KEY" \
    -d '{"domain": "test-scope2.example.com"}')

if [[ "$API_USER_CREATE" == "403" ]]; then
    echo -e "${GREEN}✓${NC} API user key correctly blocked from creating domains (403)"
else
    echo -e "${RED}❌${NC} API user key should be blocked from creating domains (got $API_USER_CREATE)"
fi

# Test 7: API user key should be able to manage aliases
echo "Test 7: API user key accessing GET /api/aliases..."
API_USER_ALIASES=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$BASE_URL/api/aliases" \
    -H "X-API-KEY: $API_USER_KEY")

if [[ "$API_USER_ALIASES" == "200" ]] || [[ "$API_USER_ALIASES" == "404" ]]; then
    echo -e "${GREEN}✓${NC} API user key can access aliases endpoint ($API_USER_ALIASES)"
else
    echo -e "${RED}❌${NC} API user key cannot access aliases ($API_USER_ALIASES)"
fi

echo ""
echo "🧹 Cleaning up test keys..."

# Get list of API keys to find our test keys
API_KEYS_LIST=$(curl -s -X GET "$BASE_URL/api/api-keys" \
    -H "X-API-KEY: $FULL_ACCESS_KEY")

# Extract test key IDs and delete them
echo "$API_KEYS_LIST" | jq -r '.apiKeys[] | select(.name | contains("Test")) | .id' | while read key_id; do
    if [[ -n "$key_id" ]]; then
        DELETE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -X DELETE "$BASE_URL/api/api-keys/$key_id" \
            -H "X-API-KEY: $FULL_ACCESS_KEY")
        if [[ "$DELETE_RESPONSE" == "200" ]]; then
            echo -e "${GREEN}✓${NC} Deleted test key: $key_id"
        else
            echo -e "${YELLOW}⚠${NC} Could not delete test key: $key_id ($DELETE_RESPONSE)"
        fi
    fi
done

echo ""
echo "🎉 Scope testing complete!"
echo ""
echo "📋 Summary:"
echo "- ✅ Scoped API keys can be created"
echo "- ✅ Read-only keys are properly restricted"
echo "- ✅ API user keys have appropriate permissions"
echo "- ✅ Scope violations return 403 Forbidden"
echo ""
echo "🚀 Your API key scoping system is working correctly!"
