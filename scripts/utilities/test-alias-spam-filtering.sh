#!/bin/bash

# Test script for alias-based spam filtering
# This script validates that the new alias-based spam filtering approach works correctly
# 
# Tests:
# 1. Free user emails are processed by process-email (no spam filtering)
# 2. Pro+ user emails are processed by advanced-process-email (with spam filtering + attachments)
# 3. No cross-contamination between user types
# 4. Proper spam header processing for Pro+ users
# 5. Database routing is correct
# 6. Plan changes update routing correctly

set -e

echo "🧪 EU Email Webhook: Testing Alias-Based Spam Filtering"
echo "======================================================="
echo ""

# Configuration
SQLITE_DB="/opt/eu-email-webhook/data/postfix.db"
TEST_DOMAIN_FREE="test-free.emailconnect.eu"
TEST_DOMAIN_PRO="test-pro.emailconnect.eu"
TEST_EMAIL_FREE="test@${TEST_DOMAIN_FREE}"
TEST_EMAIL_PRO="test@${TEST_DOMAIN_PRO}"
POSTFIX_MANAGER_URL="http://localhost:3001"
APP_URL="http://localhost:3000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test result tracking
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TESTS_TOTAL++))
    log_info "Running test: $test_name"
    
    if eval "$test_command"; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

# Test functions
test_sqlite_database() {
    log_info "Testing SQLite database connectivity..."
    
    if [ ! -f "$SQLITE_DB" ]; then
        log_error "SQLite database not found at $SQLITE_DB"
        return 1
    fi
    
    # Test basic connectivity
    if ! sqlite3 "$SQLITE_DB" "SELECT COUNT(*) FROM virtual_domains;" > /dev/null 2>&1; then
        log_error "Cannot query virtual_domains table"
        return 1
    fi
    
    if ! sqlite3 "$SQLITE_DB" "SELECT COUNT(*) FROM virtual_aliases;" > /dev/null 2>&1; then
        log_error "Cannot query virtual_aliases table"
        return 1
    fi
    
    log_success "SQLite database connectivity test"
    return 0
}

test_postfix_manager_service() {
    log_info "Testing postfix-manager service..."
    
    if ! curl -s "$POSTFIX_MANAGER_URL/health" > /dev/null; then
        log_error "Postfix-manager service not responding at $POSTFIX_MANAGER_URL"
        return 1
    fi
    
    log_success "Postfix-manager service connectivity test"
    return 0
}

test_advanced_process_email_script() {
    log_info "Testing advanced-process-email script..."

    if [ ! -f "/opt/eu-email-webhook/scripts/production/advanced-process-email.js" ]; then
        log_error "advanced-process-email.js script not found"
        return 1
    fi

    if [ ! -x "/opt/eu-email-webhook/scripts/production/advanced-process-email.js" ]; then
        log_error "advanced-process-email.js script is not executable"
        return 1
    fi

    log_success "advanced-process-email script exists and is executable"
    return 0
}

test_aliases_configuration() {
    log_info "Testing aliases configuration..."
    
    if ! grep -q "advanced-process-email:" /etc/aliases; then
        log_error "advanced-process-email alias not found in /etc/aliases"
        return 1
    fi

    # Test alias resolution
    if ! postmap -q "advanced-process-email" /etc/aliases > /dev/null 2>&1; then
        log_error "advanced-process-email alias cannot be resolved"
        return 1
    fi
    
    log_success "Aliases configuration test"
    return 0
}

test_virtual_aliases_config() {
    log_info "Testing virtual_aliases.cf configuration..."
    
    if [ ! -f "/opt/eu-email-webhook/data/virtual_aliases.cf" ]; then
        log_error "virtual_aliases.cf not found"
        return 1
    fi
    
    # Check if it's using the correct query
    if ! grep -q "SELECT destination FROM virtual_aliases WHERE email='%s' AND active=1" /opt/eu-email-webhook/data/virtual_aliases.cf; then
        log_error "virtual_aliases.cf does not contain correct query"
        return 1
    fi
    
    log_success "virtual_aliases.cf configuration test"
    return 0
}

setup_test_domains() {
    log_info "Setting up test domains..."
    
    # Add test domain for free user
    curl -s -X POST "$POSTFIX_MANAGER_URL/domains" \
        -H "Content-Type: application/json" \
        -d "{\"domain\": \"$TEST_DOMAIN_FREE\", \"planType\": \"free\"}" > /dev/null
    
    # Add test domain for pro user
    curl -s -X POST "$POSTFIX_MANAGER_URL/domains" \
        -H "Content-Type: application/json" \
        -d "{\"domain\": \"$TEST_DOMAIN_PRO\", \"planType\": \"pro\"}" > /dev/null
    
    # Add catch-all aliases
    sqlite3 "$SQLITE_DB" "INSERT OR REPLACE INTO virtual_aliases (email, destination, domain, active) VALUES ('@$TEST_DOMAIN_FREE', 'process-email', '$TEST_DOMAIN_FREE', 1);"
    sqlite3 "$SQLITE_DB" "INSERT OR REPLACE INTO virtual_aliases (email, destination, domain, active) VALUES ('@$TEST_DOMAIN_PRO', 'advanced-process-email', '$TEST_DOMAIN_PRO', 1);"
    
    log_success "Test domains setup completed"
}

test_free_user_routing() {
    log_info "Testing free user email routing..."
    
    # Query the database for free user routing
    local destination=$(sqlite3 "$SQLITE_DB" "SELECT destination FROM virtual_aliases WHERE email='@$TEST_DOMAIN_FREE' AND active=1;")
    
    if [ "$destination" != "process-email" ]; then
        log_error "Free user domain not routing to process-email (got: $destination)"
        return 1
    fi
    
    # Test Postfix lookup
    local postfix_result=$(postmap -q "@$TEST_DOMAIN_FREE" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf)
    
    if [ "$postfix_result" != "process-email" ]; then
        log_error "Postfix lookup for free user not returning process-email (got: $postfix_result)"
        return 1
    fi
    
    log_success "Free user routing test"
    return 0
}

test_pro_user_routing() {
    log_info "Testing pro user email routing..."
    
    # Query the database for pro user routing
    local destination=$(sqlite3 "$SQLITE_DB" "SELECT destination FROM virtual_aliases WHERE email='@$TEST_DOMAIN_PRO' AND active=1;")
    
    if [ "$destination" != "advanced-process-email" ]; then
        log_error "Pro user domain not routing to advanced-process-email (got: $destination)"
        return 1
    fi

    # Test Postfix lookup
    local postfix_result=$(postmap -q "@$TEST_DOMAIN_PRO" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf)

    if [ "$postfix_result" != "advanced-process-email" ]; then
        log_error "Postfix lookup for pro user not returning advanced-process-email (got: $postfix_result)"
        return 1
    fi
    
    log_success "Pro user routing test"
    return 0
}

test_plan_change_sync() {
    log_info "Testing plan change synchronization..."
    
    # Test upgrading free user to pro
    curl -s -X PUT "$POSTFIX_MANAGER_URL/domains/$TEST_DOMAIN_FREE/plan" \
        -H "Content-Type: application/json" \
        -d "{\"planType\": \"pro\"}" > /dev/null
    
    sleep 1  # Allow time for sync
    
    # Check if routing was updated
    local destination=$(sqlite3 "$SQLITE_DB" "SELECT destination FROM virtual_aliases WHERE email='@$TEST_DOMAIN_FREE' AND active=1;")
    
    if [ "$destination" != "advanced-process-email" ]; then
        log_error "Plan upgrade did not update routing (got: $destination)"
        return 1
    fi
    
    # Test downgrading pro user to free
    curl -s -X PUT "$POSTFIX_MANAGER_URL/domains/$TEST_DOMAIN_PRO/plan" \
        -H "Content-Type: application/json" \
        -d "{\"planType\": \"free\"}" > /dev/null
    
    sleep 1  # Allow time for sync
    
    # Check if routing was updated
    destination=$(sqlite3 "$SQLITE_DB" "SELECT destination FROM virtual_aliases WHERE email='@$TEST_DOMAIN_PRO' AND active=1;")
    
    if [ "$destination" != "process-email" ]; then
        log_error "Plan downgrade did not update routing (got: $destination)"
        return 1
    fi
    
    log_success "Plan change synchronization test"
    return 0
}

test_spamassassin_integration() {
    log_info "Testing SpamAssassin integration..."
    
    # Check if SpamAssassin is running
    if ! systemctl is-active --quiet spamd; then
        log_error "SpamAssassin (spamd) service is not running"
        return 1
    fi
    
    # Test spamc command
    if ! echo "Test email content" | spamc -c > /dev/null 2>&1; then
        log_error "spamc command failed"
        return 1
    fi
    
    log_success "SpamAssassin integration test"
    return 0
}

cleanup_test_domains() {
    log_info "Cleaning up test domains..."
    
    # Remove test domains from database
    sqlite3 "$SQLITE_DB" "DELETE FROM virtual_aliases WHERE domain IN ('$TEST_DOMAIN_FREE', '$TEST_DOMAIN_PRO');"
    sqlite3 "$SQLITE_DB" "DELETE FROM virtual_domains WHERE domain IN ('$TEST_DOMAIN_FREE', '$TEST_DOMAIN_PRO');"
    
    log_success "Test domains cleanup completed"
}

# Main test execution
main() {
    echo "Starting comprehensive alias-based spam filtering tests..."
    echo ""
    
    # Prerequisites tests
    run_test "SQLite Database Connectivity" "test_sqlite_database"
    run_test "Postfix Manager Service" "test_postfix_manager_service"
    run_test "advanced-process-email Script" "test_advanced_process_email_script"
    run_test "Aliases Configuration" "test_aliases_configuration"
    run_test "Virtual Aliases Config" "test_virtual_aliases_config"
    run_test "SpamAssassin Integration" "test_spamassassin_integration"
    
    echo ""
    log_info "Setting up test environment..."
    setup_test_domains
    
    echo ""
    # Functional tests
    run_test "Free User Routing" "test_free_user_routing"
    run_test "Pro User Routing" "test_pro_user_routing"
    run_test "Plan Change Synchronization" "test_plan_change_sync"
    
    echo ""
    log_info "Cleaning up test environment..."
    cleanup_test_domains
    
    echo ""
    echo "=========================================="
    echo "Test Results Summary:"
    echo "=========================================="
    echo "Total Tests: $TESTS_TOTAL"
    echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Failed: ${RED}$TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo ""
        log_success "All tests passed! Alias-based spam filtering is working correctly."
        exit 0
    else
        echo ""
        log_error "Some tests failed. Please review the output above."
        exit 1
    fi
}

# Check if running as root or with sudo
if [[ $EUID -ne 0 ]] && ! sudo -n true 2>/dev/null; then
    echo "⚠️  Some tests require sudo access. Please run with sudo or as root for complete testing."
fi

# Quick validation function for basic checks
quick_validate() {
    echo "🔍 Quick Validation of Alias-Based Spam Filtering"
    echo "================================================"
    echo ""

    local issues=0

    # Check advanced-process-email script
    if [ ! -f "/opt/eu-email-webhook/scripts/production/advanced-process-email.js" ]; then
        log_error "advanced-process-email.js script missing"
        ((issues++))
    else
        log_success "advanced-process-email.js script exists"
    fi

    # Check aliases
    if ! grep -q "advanced-process-email:" /etc/aliases; then
        log_error "advanced-process-email alias missing from /etc/aliases"
        ((issues++))
    else
        log_success "advanced-process-email alias configured"
    fi

    # Check virtual_aliases.cf
    if [ ! -f "/opt/eu-email-webhook/data/virtual_aliases.cf" ]; then
        log_error "virtual_aliases.cf missing"
        ((issues++))
    elif ! grep -q "SELECT destination FROM virtual_aliases WHERE email='%s' AND active=1" /opt/eu-email-webhook/data/virtual_aliases.cf; then
        log_error "virtual_aliases.cf has incorrect query"
        ((issues++))
    else
        log_success "virtual_aliases.cf configured correctly"
    fi

    # Check SQLite database
    if [ ! -f "$SQLITE_DB" ]; then
        log_error "SQLite database missing"
        ((issues++))
    elif ! sqlite3 "$SQLITE_DB" "SELECT COUNT(*) FROM virtual_aliases;" > /dev/null 2>&1; then
        log_error "SQLite database not accessible"
        ((issues++))
    else
        log_success "SQLite database accessible"
    fi

    # Check SpamAssassin
    if ! systemctl is-active --quiet spamd 2>/dev/null; then
        log_warning "SpamAssassin (spamd) service not running"
    else
        log_success "SpamAssassin service running"
    fi

    echo ""
    if [ $issues -eq 0 ]; then
        log_success "Quick validation passed! Basic configuration looks correct."
        echo ""
        echo "💡 To run comprehensive tests, use: sudo ./scripts/test-alias-spam-filtering.sh"
    else
        log_error "Quick validation found $issues issues. Please fix them before proceeding."
        exit 1
    fi
}

# Check command line arguments
if [ "$1" = "--quick" ] || [ "$1" = "-q" ]; then
    quick_validate
else
    # Run main function
    main "$@"
fi
