#!/usr/bin/env node

/**
 * Quick test to debug the domain listing endpoint
 */

import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();
const API_BASE = process.env.API_BASE || 'http://localhost:3000';
const USER_JWT_SECRET = process.env.USER_JWT_SECRET || 'your-very-strong-and-secret-key-for-user-jwt';

async function testDomainsEndpoint() {
  console.log('🔍 Testing domains endpoint...');
  
  try {
    // Find an existing user
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('❌ No users found in database');
      return;
    }
    
    console.log(`✅ Found user: ${user.email} (ID: ${user.id})`);
    
    // Create token
    const token = jwt.sign(
      { userId: user.id, email: user.email }, 
      USER_JWT_SECRET, 
      { expiresIn: '1h' }
    );
    
    // Test domains endpoint
    const url = `${API_BASE}/api/domains`;
    console.log(`🌐 Testing: ${url}`);
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `user_token=${token}`
      }
    });
    
    console.log(`📊 Response status: ${response.status}`);
    
    if (response.status === 200) {
      const data = await response.json();
      console.log(`✅ Success! Found ${data.domains.length} domains`);
      console.log(`📈 Total: ${data.total}, Verified: ${data.verified_count}`);
    } else {
      const errorData = await response.json().catch(() => null);
      if (errorData) {
        console.log(`❌ Error response:`, errorData);
        if (errorData.details) {
          console.log(`🔍 Error details: ${errorData.details}`);
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ Error response (text): ${errorText}`);
      }
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testDomainsEndpoint();
