#!/usr/bin/env node

/**
 * Simple validation script for process-email.js
 * This ensures the critical production script is in the right location and executable
 */

const fs = require('fs');
const path = require('path');

const SCRIPT_PATH = '/home/<USER>/webapps/eu-email-webhook/scripts/production/process-email.js';

console.log('🔍 Validating process-email.js script...\n');

// Check if file exists
if (!fs.existsSync(SCRIPT_PATH)) {
  console.log('❌ FAIL: process-email.js not found at expected location');
  console.log(`   Expected: ${SCRIPT_PATH}`);
  process.exit(1);
}

console.log('✅ PASS: Script file exists');

// Check if file is readable
try {
  const content = fs.readFileSync(SCRIPT_PATH, 'utf8');
  
  // Check shebang
  if (!content.startsWith('#!/usr/bin/env node')) {
    console.log('❌ FAIL: Missing or incorrect shebang');
    process.exit(1);
  }
  console.log('✅ PASS: Correct shebang found');
  
  // Check key components
  const requiredComponents = [
    'process.stdin',
    'http.request',
    '/api/email/process',
    'X-Processing-Script'
  ];
  
  for (const component of requiredComponents) {
    if (!content.includes(component)) {
      console.log(`❌ FAIL: Missing required component: ${component}`);
      process.exit(1);
    }
  }
  console.log('✅ PASS: All required components found');
  
  // Check updated path reference
  if (!content.includes('/opt/eu-email-webhook/scripts/production/process-email.js')) {
    console.log('❌ FAIL: Script self-reference not updated');
    process.exit(1);
  }
  console.log('✅ PASS: Script self-reference updated');
  
} catch (error) {
  console.log('❌ FAIL: Cannot read script file:', error.message);
  process.exit(1);
}

// Check file permissions
try {
  fs.accessSync(SCRIPT_PATH, fs.constants.R_OK);
  console.log('✅ PASS: Script is readable');
} catch (error) {
  console.log('❌ FAIL: Script is not readable:', error.message);
  process.exit(1);
}

console.log('\n🎉 All validations passed!');
console.log('📍 process-email.js is properly located and configured');
console.log('🔧 Ready for production deployment');
