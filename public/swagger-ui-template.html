<!DOCTYPE html>
<html lang="en" data-theme="emerald">
<head>
  <meta charset="UTF-8">
  <title>EmailConnect.eu API Documentation</title>
  <link rel="stylesheet" type="text/css" href="./swagger-ui-bundle.css" />
  <link rel="stylesheet" type="text/css" href="./swagger-ui-standalone-preset.css" />
  <link rel="stylesheet" type="text/css" href="/css/swagger-ui-custom.css" />
  <link rel="icon" type="image/x-icon" href="/src/frontend/assets/images/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="EmailConnect.eu API Documentation - Manage your email domains, aliases, and webhooks programmatically">
  
  <!-- Inter font -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    
    *, *:before, *:after {
      box-sizing: inherit;
    }

    body {
      margin: 0;
      background: #fafafa;
      font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
    }
    
    /* Theme detection and switching */
    @media (prefers-color-scheme: dark) {
      html:not([data-theme]) {
        --swagger-primary: #34d399;
        --swagger-primary-hover: #10b981;
        --swagger-bg: #1f2937;
        --swagger-surface: #374151;
        --swagger-border: #4b5563;
        --swagger-text: #f9fafb;
        --swagger-text-muted: #9ca3af;
      }
      
      html:not([data-theme]) body {
        background: #1f2937;
      }
    }
  </style>
</head>

<body>
  <!-- Custom Header -->
  <div class="swagger-ui-custom-header">
    <div class="container">
      <div style="display: flex; align-items: center; gap: 12px;">
        <img src="/src/frontend/assets/images/logo-light.png" alt="EmailConnect.eu" class="logo" id="logo-light" width="32" height="32" />
        <img src="/src/frontend/assets/images/logo-dark.png" alt="EmailConnect.eu" class="logo" id="logo-dark" width="32" height="32" style="display: none;" />
        <div>
          <h1 style="margin: 0; font-size: 1.25rem; font-weight: 600; color: var(--swagger-text);">API Documentation</h1>
          <p style="margin: 0; font-size: 0.875rem; color: var(--swagger-text-muted);">Manage domains, aliases, and webhooks</p>
        </div>
      </div>
      <div class="nav-links">
        <a href="/" target="_blank">← Back to EmailConnect.eu</a>
        <a href="/register" target="_blank">Get Started</a>
        <a href="/login" target="_blank">Sign In</a>
      </div>
    </div>
  </div>

  <div id="swagger-ui"></div>
  
  <script src="./swagger-ui-bundle.js" charset="UTF-8"> </script>
  <script src="./swagger-ui-standalone-preset.js" charset="UTF-8"> </script>
  <script>
    // Theme detection and logo switching
    function updateTheme() {
      const theme = document.documentElement.getAttribute('data-theme') || 
                   (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dim' : 'emerald');
      
      const logoLight = document.getElementById('logo-light');
      const logoDark = document.getElementById('logo-dark');
      
      if (theme === 'dim') {
        logoLight.style.display = 'none';
        logoDark.style.display = 'block';
      } else {
        logoLight.style.display = 'block';
        logoDark.style.display = 'none';
      }
    }
    
    // Initial theme setup
    updateTheme();
    
    // Watch for theme changes
    const observer = new MutationObserver(updateTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });
    
    // Watch for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateTheme);
    
    window.onload = function() {
      // Begin Swagger UI call region
      const ui = SwaggerUIBundle({
        url: './json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        docExpansion: "list",
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        requestInterceptor: function(request) {
          // Add any custom request modifications here
          return request;
        },
        responseInterceptor: function(response) {
          // Add any custom response processing here
          return response;
        },
        onComplete: function() {
          // Custom initialization after Swagger UI loads
          console.log('EmailConnect.eu API Documentation loaded');
          
          // Add custom styling or behavior here
          const infoSection = document.querySelector('.swagger-ui .info');
          if (infoSection) {
            // Add custom content to info section if needed
          }
        }
      });
      
      // End Swagger UI call region
      window.ui = ui;
    };
  </script>
</body>
</html>
