/* Enhanced UX Loading States and Animations */

/* Loading spinner for buttons */
.loading-spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: currentColor;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced button loading states */
.button-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.button-loading::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  top: 50%;
  left: 50%;
  margin-left: -7px;
  margin-top: -7px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Table row loading states */
.row-loading {
  position: relative;
  pointer-events: none;
}

.row-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced toggle loading state */
.toggle-loading {
  position: relative;
  overflow: hidden;
}

.toggle-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: slideIn 1s infinite;
}

@keyframes slideIn {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Enhanced toast notifications with better positioning and animations */
.toast-notification {
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.toast-success {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.toast-error {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

.toast-warning {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.toast-info {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
}

/* Modal backdrop with glassmorphism */
.modal-backdrop {
  background: rgba(17, 24, 39, 0.7);
  backdrop-filter: blur(8px);
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Enhanced confirmation dialog */
.confirmation-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  animation: slideUp 0.2s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Loading overlay for entire sections */
.section-loading {
  position: relative;
  overflow: hidden;
}

.section-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 10;
}

.section-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  border: 3px solid rgba(59, 130, 246, 0.3);
  border-top-color: #3B82F6;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  z-index: 11;
}

/* Micro-interactions for better feedback */
.interactive-element {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

/* Enhanced focus states */
.focus-enhanced:focus {
  outline: none;
  box-shadow: 
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 0 0 1px rgba(59, 130, 246, 0.5);
  border-color: #3B82F6;
}

/* Skeleton loading for content */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Progress indicators */
.progress-bar {
  height: 2px;
  background: linear-gradient(90deg, #3B82F6, #8B5CF6);
  border-radius: 1px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced table hover effects */
.table-row-enhanced {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-row-enhanced:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(59, 130, 246, 0.01) 100%);
}

/* Status transition animations */
.status-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pulse animation for important elements */
.pulse-subtle {
  animation: pulseSubtle 3s ease-in-out infinite;
}

@keyframes pulseSubtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Enhanced dropdown animations */
.dropdown-enhanced {
  transform-origin: top;
  animation: dropdownSlide 0.15s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: scaleY(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scaleY(1) translateY(0);
  }
}