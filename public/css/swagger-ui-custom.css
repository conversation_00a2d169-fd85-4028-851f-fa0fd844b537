/* Custom Swagger UI styling to match EmailConnect.eu daisyUI theme */

/* CSS Variables for theme consistency */
:root {
  /* Light theme (emerald) colors */
  --swagger-primary: #059669;
  --swagger-primary-hover: #047857;
  --swagger-bg: #ffffff;
  --swagger-surface: #f8fafc;
  --swagger-border: #e2e8f0;
  --swagger-text: #1e293b;
  --swagger-text-muted: #64748b;
  --swagger-success: #10b981;
  --swagger-warning: #f59e0b;
  --swagger-error: #ef4444;
  --swagger-info: #3b82f6;
}

/* Dark theme (dim) colors */
[data-theme="dim"] {
  --swagger-primary: #34d399;
  --swagger-primary-hover: #10b981;
  --swagger-bg: #1f2937;
  --swagger-surface: #374151;
  --swagger-border: #4b5563;
  --swagger-text: #f9fafb;
  --swagger-text-muted: #9ca3af;
  --swagger-success: #10b981;
  --swagger-warning: #f59e0b;
  --swagger-error: #ef4444;
  --swagger-info: #60a5fa;
}

/* Main container styling */
.swagger-ui {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif !important;
  background-color: var(--swagger-bg) !important;
  color: var(--swagger-text) !important;
}

/* Header styling */
.swagger-ui .info {
  background-color: var(--swagger-surface) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 12px !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
}

.swagger-ui .info .title {
  color: var(--swagger-text) !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
}

.swagger-ui .info .description {
  color: var(--swagger-text-muted) !important;
  line-height: 1.6 !important;
}

/* Operation blocks */
.swagger-ui .opblock {
  background-color: var(--swagger-surface) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 8px !important;
  margin-bottom: 1rem !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.swagger-ui .opblock .opblock-summary {
  border-bottom: 1px solid var(--swagger-border) !important;
  padding: 1rem !important;
}

.swagger-ui .opblock .opblock-summary-description {
  color: var(--swagger-text-muted) !important;
}

/* HTTP method colors */
.swagger-ui .opblock.opblock-get {
  border-left: 4px solid var(--swagger-info) !important;
}

.swagger-ui .opblock.opblock-post {
  border-left: 4px solid var(--swagger-success) !important;
}

.swagger-ui .opblock.opblock-put {
  border-left: 4px solid var(--swagger-warning) !important;
}

.swagger-ui .opblock.opblock-delete {
  border-left: 4px solid var(--swagger-error) !important;
}

/* Buttons */
.swagger-ui .btn {
  background-color: var(--swagger-primary) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  transition: background-color 0.2s ease !important;
}

.swagger-ui .btn:hover {
  background-color: var(--swagger-primary-hover) !important;
}

.swagger-ui .btn.authorize {
  background-color: var(--swagger-primary) !important;
  border-color: var(--swagger-primary) !important;
}

/* Try it out button */
.swagger-ui .btn.try-out__btn {
  background-color: transparent !important;
  color: var(--swagger-primary) !important;
  border: 1px solid var(--swagger-primary) !important;
}

.swagger-ui .btn.try-out__btn:hover {
  background-color: var(--swagger-primary) !important;
  color: white !important;
}

/* Execute button */
.swagger-ui .btn.execute {
  background-color: var(--swagger-primary) !important;
  border-color: var(--swagger-primary) !important;
}

/* Input fields */
.swagger-ui input[type="text"],
.swagger-ui input[type="password"],
.swagger-ui textarea,
.swagger-ui select {
  background-color: var(--swagger-bg) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 6px !important;
  color: var(--swagger-text) !important;
  padding: 0.5rem !important;
}

.swagger-ui input[type="text"]:focus,
.swagger-ui input[type="password"]:focus,
.swagger-ui textarea:focus,
.swagger-ui select:focus {
  border-color: var(--swagger-primary) !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1) !important;
}

/* Tables */
.swagger-ui table {
  background-color: var(--swagger-surface) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 6px !important;
}

.swagger-ui table thead tr th {
  background-color: var(--swagger-surface) !important;
  color: var(--swagger-text) !important;
  border-bottom: 1px solid var(--swagger-border) !important;
}

.swagger-ui table tbody tr td {
  color: var(--swagger-text) !important;
  border-bottom: 1px solid var(--swagger-border) !important;
}

/* Response section */
.swagger-ui .responses-inner {
  background-color: var(--swagger-surface) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 6px !important;
}

/* Code blocks */
.swagger-ui .highlight-code {
  background-color: var(--swagger-surface) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 6px !important;
}

/* Authorization modal */
.swagger-ui .dialog-ux .modal-ux {
  background-color: var(--swagger-bg) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.swagger-ui .dialog-ux .modal-ux-header {
  background-color: var(--swagger-surface) !important;
  border-bottom: 1px solid var(--swagger-border) !important;
  color: var(--swagger-text) !important;
}

/* Custom header with logo */
.swagger-ui-custom-header {
  background-color: var(--swagger-bg) !important;
  border-bottom: 1px solid var(--swagger-border) !important;
  padding: 1rem 0 !important;
  margin-bottom: 2rem !important;
}

.swagger-ui-custom-header .container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.swagger-ui-custom-header .logo {
  height: 32px !important;
}

.swagger-ui-custom-header .nav-links {
  display: flex !important;
  gap: 1.5rem !important;
}

.swagger-ui-custom-header .nav-links a {
  color: var(--swagger-text-muted) !important;
  text-decoration: none !important;
  font-size: 0.875rem !important;
  transition: color 0.2s ease !important;
}

.swagger-ui-custom-header .nav-links a:hover {
  color: var(--swagger-primary) !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .swagger-ui-custom-header .container {
    padding: 0 1rem !important;
  }
  
  .swagger-ui-custom-header .nav-links {
    display: none !important;
  }
  
  .swagger-ui .info {
    padding: 1rem !important;
  }
}

/* Tag sections */
.swagger-ui .opblock-tag {
  background-color: var(--swagger-surface) !important;
  border: 1px solid var(--swagger-border) !important;
  border-radius: 8px !important;
  margin-bottom: 1rem !important;
  padding: 1rem !important;
}

.swagger-ui .opblock-tag-section h3 {
  color: var(--swagger-text) !important;
  font-weight: 600 !important;
}

/* Scrollbar styling */
.swagger-ui ::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.swagger-ui ::-webkit-scrollbar-track {
  background: var(--swagger-surface) !important;
  border-radius: 4px !important;
}

.swagger-ui ::-webkit-scrollbar-thumb {
  background: var(--swagger-border) !important;
  border-radius: 4px !important;
}

.swagger-ui ::-webkit-scrollbar-thumb:hover {
  background: var(--swagger-text-muted) !important;
}
