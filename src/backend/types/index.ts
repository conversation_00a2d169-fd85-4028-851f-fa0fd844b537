import { z } from 'zod';

// Email parsing types
export const EmailAddressSchema = z.object({
  name: z.string().optional(),
  address: z.string().email(),
});

export const ParsedEmailSchema = z.object({
  messageId: z.string(),
  from: EmailAddressSchema,
  to: z.array(EmailAddressSchema),
  cc: z.array(EmailAddressSchema).optional(),
  bcc: z.array(EmailAddressSchema).optional(),
  subject: z.string().optional(),
  text: z.string().optional(),
  html: z.string().optional(),
  date: z.date(),
  headers: z.record(z.string()),
  attachments: z.array(z.object({
    filename: z.string().optional(),
    contentType: z.string(),
    size: z.number(),
    content: z.string().optional(), // base64 encoded
  })).optional(),
});

export type ParsedEmail = z.infer<typeof ParsedEmailSchema>;
export type EmailAddress = z.infer<typeof EmailAddressSchema>;

// Enhanced Email JSON Structure for Webhook Payload - FIXED to match EmailParser implementation
export const EnhancedEmailWebhookPayloadSchema = z.object({
  // Core message content - what users care about
  message: z.object({
    sender: z.object({
      name: z.string().nullable(),
      email: z.string(),
    }),
    recipient: z.object({
      name: z.string().nullable(),
      email: z.string(),
    }),
    subject: z.string().nullable(),
    content: z.object({
      text: z.string().nullable(),
      html: z.string().nullable(),
    }),
    date: z.string(), // ISO format
    attachments: z.array(z.object({
      filename: z.string().nullable(),
      contentType: z.string(),
      size: z.number(),
      content: z.string().optional(), // base64 encoded (only for small text-based attachments)
      excluded: z.boolean().optional(), // true if attachment was excluded from processing
      excludeReason: z.enum(['non-text-file', 'too-large']).optional(), // reason for exclusion
    })),
  }),

  // Technical envelope data - for debugging/routing
  envelope: z.object({
    messageId: z.string(),
    xMailer: z.string().nullable(),
    deliveredTo: z.string().nullable(),
    xOriginalTo: z.string().nullable(),
    returnPath: z.string().nullable(),
    // Full recipient lists (CC, BCC) - FIXED: These are string arrays, not object arrays
    allRecipients: z.object({
      to: z.array(z.string()),
      cc: z.array(z.string()),
      bcc: z.array(z.string()),
    }),
    // Technical headers
    headers: z.record(z.string()),
    // Processing metadata
    processed: z.object({
      timestamp: z.string(),
      domain: z.string(),
      alias: z.string(), // The specific alias that received this email
      originalSize: z.number(),
    }),
  }),

  // Spam filtering information (optional - only present if SpamAssassin processed the email)
  spam: z.object({
    isSpam: z.boolean(),
    score: z.number(),
    level: z.string().nullable(),
    report: z.string().nullable(),
    version: z.string().nullable(),
    status: z.string().nullable(),
  }).optional(),
});

export type EnhancedEmailWebhookPayload = z.infer<typeof EnhancedEmailWebhookPayloadSchema>;

// Domain configuration types
export const DomainConfigSchema = z.object({
  domain: z.string(),
  webhookUrl: z.string().url(),
  active: z.boolean().default(true),
  aliases: z.array(z.object({
    alias: z.string(),
    webhookUrl: z.string().url(),
  })).optional(),
});

export type DomainConfig = z.infer<typeof DomainConfigSchema>;

// Verification status enum matching Prisma schema
export const VerificationStatusSchema = z.enum([
  'PENDING',     // Just added, waiting for verification
  'VERIFIED',    // DNS verification successful
  'ACTIVE',      // Verified and receiving emails  
  'WARNING',     // Verification failed, grace period active
  'SUSPENDED',   // Grace period expired, domain disabled
  'FAILED',      // Verification permanently failed
]);

export type VerificationStatus = z.infer<typeof VerificationStatusSchema>;

// Extended domain response type
export interface DomainResponse {
  id: string;
  domain: string;
  webhookUrl: string;
  active: boolean;
  verified: boolean;
  verificationStatus: VerificationStatus;
  postfix_configured: boolean;
  createdAt: string;
  updatedAt: string;
  aliases: Array<{
    id: string;
    email: string;
    webhookUrl: string;
    active: boolean;
  }>;
  lastVerificationAttempt?: string;
  nextVerificationCheck?: string;
  verificationFailureCount: number;
}

// Webhook delivery types - Original structure for backward compatibility
export const WebhookPayloadSchema = z.object({
  messageId: z.string(),
  timestamp: z.string(),
  from: EmailAddressSchema,
  to: z.array(EmailAddressSchema),
  subject: z.string().optional(),
  text: z.string().optional(),
  html: z.string().optional(),
  headers: z.record(z.string()),
  attachments: z.array(z.object({
    filename: z.string().optional(),
    contentType: z.string(),
    size: z.number(),
  })).optional(),
});

export type WebhookPayload = z.infer<typeof WebhookPayloadSchema>;

// Webhook verification payload type
export interface WebhookVerificationPayload {
  type: 'webhook_verification';
  verification_token: string;
  timestamp: number;
  webhook: {
    id: string;
    url: string;
  };
}

// Union type to support both old and new webhook payload formats, plus verification
export type AnyWebhookPayload = WebhookPayload | EnhancedEmailWebhookPayload | WebhookVerificationPayload;

// Internal queue payload type that includes tracking fields
export type InternalQueuePayload = AnyWebhookPayload & {
  _internalMessageId?: string;
};

// Database types
export interface EmailRecord {
  id: string;
  messageId: string;
  domain: string;
  fromAddress: string;
  toAddresses: string[];
  subject?: string;
  webhookUrl: string;
  deliveryStatus: 'pending' | 'delivered' | 'failed' | 'retrying';
  deliveryAttempts: number;
  lastAttemptAt?: Date;
  deliveredAt?: Date;
  errorMessage?: string;
  createdAt: Date;
  expiresAt: Date; // For GDPR compliance
}

export interface AxiosError extends Error {
  response?: {
    status: number;
    data: any;
  };
}

export interface WebhookJobData {
  webhookUrl: string;
  payload: AnyWebhookPayload;
}