import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { parseSpamHeaders } from '../services/email-parser';
import { PlanConfigService } from '../services/billing/plan-config.service';
import { DomainService } from '../services/user/domain.service';

describe('Spam Filtering', () => {
  describe('Email Parser - Spam Headers', () => {
    it('should parse SpamAssassin headers correctly', () => {
      const mockHeaders = {
        'x-spam-status': 'Yes, score=7.5 required=5.0 tests=BAYES_99,HTML_MESSAGE,URIBL_BLOCKED autolearn=spam autolearn_force=no version=3.4.6',
        'x-spam-score': '7.5',
        'x-spam-level': '*******',
        'x-spam-report': 'Spam detection software, running on the system "mail.example.com", has identified this incoming email as possible spam.',
        'x-spam-version': 'SpamAssassin 3.4.6 (2021-04-09) on mail.example.com'
      };

      const result = parseSpamHeaders(mockHeaders);

      expect(result).toEqual({
        isSpam: true,
        score: 7.5,
        level: '*******',
        report: 'Spam detection software, running on the system "mail.example.com", has identified this incoming email as possible spam.',
        version: 'SpamAssassin 3.4.6 (2021-04-09) on mail.example.com',
        status: 'Yes, score=7.5 required=5.0 tests=BAYES_99,HTML_MESSAGE,URIBL_BLOCKED autolearn=spam autolearn_force=no version=3.4.6'
      });
    });

    it('should handle non-spam emails', () => {
      const mockHeaders = {
        'x-spam-status': 'No, score=1.2 required=5.0 tests=BAYES_00 autolearn=ham autolearn_force=no version=3.4.6',
        'x-spam-score': '1.2',
        'x-spam-level': '*',
        'x-spam-version': 'SpamAssassin 3.4.6'
      };

      const result = parseSpamHeaders(mockHeaders);

      expect(result).toEqual({
        isSpam: false,
        score: 1.2,
        level: '*',
        report: null,
        version: 'SpamAssassin 3.4.6',
        status: 'No, score=1.2 required=5.0 tests=BAYES_00 autolearn=ham autolearn_force=no version=3.4.6'
      });
    });

    it('should return null when no spam headers present', () => {
      const mockHeaders = {
        'from': '<EMAIL>',
        'to': '<EMAIL>',
        'subject': 'Test Email'
      };

      const result = parseSpamHeaders(mockHeaders);
      expect(result).toBeNull();
    });

    it('should handle malformed spam headers gracefully', () => {
      const mockHeaders = {
        'x-spam-status': 'Invalid format',
        'x-spam-score': 'not-a-number'
      };

      const result = parseSpamHeaders(mockHeaders);
      expect(result).toEqual({
        isSpam: false,
        score: 0,
        level: null,
        report: null,
        version: null,
        status: 'Invalid format'
      });
    });
  });

  describe('Plan Configuration - Spam Filtering Permissions', () => {
    let planConfigService: PlanConfigService;

    beforeEach(() => {
      planConfigService = new PlanConfigService();
    });

    it('should allow spam filtering for Pro users', () => {
      const hasPermission = planConfigService.userHasPermission('pro', 'spam_filtering');
      expect(hasPermission).toBe(true);
    });

    it('should allow spam filtering for Enterprise users', () => {
      const hasPermission = planConfigService.userHasPermission('enterprise', 'spam_filtering');
      expect(hasPermission).toBe(true);
    });

    it('should deny spam filtering for Free users', () => {
      const hasPermission = planConfigService.userHasPermission('free', 'spam_filtering');
      expect(hasPermission).toBe(false);
    });

    it('should validate domain:spam_filter scope requires Pro+', () => {
      const scopePermissions = planConfigService.getScopePermissions('domain:spam_filter');
      expect(scopePermissions).toContain('spam_filtering');
    });
  });

  describe('Domain Service - Spam Filter Settings', () => {
    let domainService: DomainService;
    let mockDomain: any;

    beforeEach(() => {
      domainService = new DomainService();
      mockDomain = {
        id: 'test-domain-id',
        domain: 'test.example.com',
        configuration: {}
      };
    });

    it('should get default spam filter settings', async () => {
      // Mock the domain repository
      const mockFindUnique = jest.fn().mockResolvedValue(mockDomain);
      (domainService as any).domainRepository = { findUnique: mockFindUnique };

      const settings = await domainService.getSpamFilterSettings('test-domain-id');

      expect(settings).toEqual({
        enabled: false,
        thresholdGreen: 2.0,
        thresholdRed: 5.0
      });
    });

    it('should get existing spam filter settings', async () => {
      mockDomain.configuration = {
        spamFiltering: {
          enabled: true,
          thresholdGreen: 3.0,
          thresholdRed: 7.0
        }
      };

      const mockFindUnique = jest.fn().mockResolvedValue(mockDomain);
      (domainService as any).domainRepository = { findUnique: mockFindUnique };

      const settings = await domainService.getSpamFilterSettings('test-domain-id');

      expect(settings).toEqual({
        enabled: true,
        thresholdGreen: 3.0,
        thresholdRed: 7.0
      });
    });

    it('should validate threshold ranges', async () => {
      const mockFindUnique = jest.fn().mockResolvedValue(mockDomain);
      const mockUpdate = jest.fn().mockResolvedValue(mockDomain);
      (domainService as any).domainRepository = { 
        findUnique: mockFindUnique,
        update: mockUpdate 
      };

      // Mock PostfixManager
      const mockUpdateDomainSpamFiltering = jest.fn().mockResolvedValue(undefined);
      (domainService as any).postfixManager = {
        updateDomainSpamFiltering: mockUpdateDomainSpamFiltering
      };

      // Test invalid thresholds
      await expect(
        domainService.updateSpamFilterSettings('test-domain-id', {
          enabled: true,
          thresholdGreen: -1,
          thresholdRed: 5.0
        })
      ).rejects.toThrow('Green threshold must be between 0 and 20');

      await expect(
        domainService.updateSpamFilterSettings('test-domain-id', {
          enabled: true,
          thresholdGreen: 2.0,
          thresholdRed: 25
        })
      ).rejects.toThrow('Red threshold must be between 0 and 20');

      await expect(
        domainService.updateSpamFilterSettings('test-domain-id', {
          enabled: true,
          thresholdGreen: 8.0,
          thresholdRed: 5.0
        })
      ).rejects.toThrow('Green threshold must be less than red threshold');
    });

    it('should update spam filter settings successfully', async () => {
      const mockFindUnique = jest.fn().mockResolvedValue(mockDomain);
      const mockUpdate = jest.fn().mockResolvedValue({
        ...mockDomain,
        configuration: {
          spamFiltering: {
            enabled: true,
            thresholdGreen: 3.0,
            thresholdRed: 6.0
          }
        }
      });
      (domainService as any).domainRepository = { 
        findUnique: mockFindUnique,
        update: mockUpdate 
      };

      const mockUpdateDomainSpamFiltering = jest.fn().mockResolvedValue(undefined);
      (domainService as any).postfixManager = {
        updateDomainSpamFiltering: mockUpdateDomainSpamFiltering
      };

      const result = await domainService.updateSpamFilterSettings('test-domain-id', {
        enabled: true,
        thresholdGreen: 3.0,
        thresholdRed: 6.0
      });

      expect(result).toEqual({
        enabled: true,
        thresholdGreen: 3.0,
        thresholdRed: 6.0
      });

      expect(mockUpdateDomainSpamFiltering).toHaveBeenCalledWith('test.example.com', true);
    });
  });
});

// Helper function tests
describe('Spam Header Parsing Utilities', () => {
  it('should extract spam score from status header', () => {
    const status = 'Yes, score=7.5 required=5.0 tests=BAYES_99';
    const scoreMatch = status.match(/score=([\d.-]+)/);
    expect(scoreMatch?.[1]).toBe('7.5');
    expect(parseFloat(scoreMatch?.[1] || '0')).toBe(7.5);
  });

  it('should determine spam status from header', () => {
    expect('Yes, score=7.5'.toLowerCase().startsWith('yes')).toBe(true);
    expect('No, score=1.2'.toLowerCase().startsWith('yes')).toBe(false);
  });
});
