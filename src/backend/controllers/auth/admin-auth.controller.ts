import { FastifyRequest, FastifyReply } from 'fastify';
import { AdminAuthService, AdminLoginData } from '../../services/auth/admin-auth.service.js';
import { logger } from '../../utils/logger.js';

const adminAuthService = new AdminAuthService();

export class AdminAuthController {
  /**
   * Handle admin login
   */
  async login(request: FastifyRequest, reply: FastifyReply) {
    try {
      const credentials = request.body as AdminLoginData;

      // Authenticate admin
      const authResult = await adminAuthService.authenticateAdmin(credentials);
      if (!authResult.success) {
        const statusCode = authResult.error?.includes('required') ? 400 : 
                          authResult.error?.includes('Invalid credentials') ? 401 : 500;
        return reply.status(statusCode).send({ error: authResult.error });
      }

      // Generate token
      const tokenResult = adminAuthService.generateToken();
      if (!tokenResult.success) {
        return reply.status(500).send({ error: tokenResult.error });
      }

      // Set cookie
      const cookieConfig = adminAuthService.getCookieConfig();
      reply.setCookie('admin_token', tokenResult.token!, cookieConfig);

      return reply.send({ 
        message: 'Admin login successful', 
        token: tokenResult.token 
      });

    } catch (error: any) {
      logger.error({ err: error }, 'Error in admin login controller');
      return reply.status(500).send({ 
        error: 'Internal server error during admin authentication' 
      });
    }
  }

  /**
   * Handle admin logout
   */
  async logout(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Clear the admin token cookie
      reply.clearCookie('admin_token', { path: '/' });
      
      return reply.send({ message: 'Admin logout successful' });
    } catch (error: any) {
      logger.error({ err: error }, 'Error in admin logout controller');
      return reply.status(500).send({ 
        error: 'Internal server error during logout' 
      });
    }
  }
}
