import { FastifyRequest, FastifyReply } from 'fastify';
import { WebhookService, CreateWebhookData, UpdateWebhookData } from '../../services/user/webhook.service.js';
import { logger } from '../../utils/logger.js';

const webhookService = new WebhookService();

export class WebhooksController {
  /**
   * Get user's webhooks
   */
  async getWebhooks(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const webhooks = await webhookService.getUserWebhooks(user.id);
      
      return reply.send({
        webhooks,
        total: webhooks.length
      });
    } catch (error: any) {
      logger.error({ error: error?.message, stack: error.stack }, 'Failed to get user webhooks');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve webhooks' 
      });
    }
  }

  /**
   * Get specific webhook
   */
  async getWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { webhookId } = request.params as { webhookId: string };

      const webhook = await webhookService.getWebhookById(webhookId, user.id);

      if (!webhook) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Webhook not found'
        });
      }

      return reply.send(webhook);
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to get webhook');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve webhook' 
      });
    }
  }

  /**
   * Create new webhook
   */
  async createWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const body = request.body as Omit<CreateWebhookData, 'userId'>;

      const createData: CreateWebhookData = {
        ...body,
        userId: user.id
      };

      const result = await webhookService.createWebhook(createData);

      return reply.code(201).send({
        success: true,
        webhook: {
          ...result.webhook,
          webhookSecret: result.generatedSecret // Only return secret if auto-generated
        }
      });
    } catch (error: any) {
      if (error.message === 'Webhook with this URL already exists') {
        return reply.code(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to create webhook');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to create webhook' 
      });
    }
  }

  /**
   * Update webhook
   */
  async updateWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { webhookId } = request.params as { webhookId: string };
      const updates = request.body as UpdateWebhookData;

      const result = await webhookService.updateWebhook(webhookId, user.id, updates);

      return reply.send({
        success: true,
        webhook: {
          ...result.webhook,
          webhookSecret: result.generatedSecret // Only return if auto-generated
        }
      });
    } catch (error: any) {
      if (error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      if (error.message === 'Another webhook with this URL already exists') {
        return reply.code(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to update webhook');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to update webhook' 
      });
    }
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { webhookId } = request.params as { webhookId: string };

      await webhookService.deleteWebhook(webhookId, user.id);

      return reply.send({
        success: true,
        message: 'Webhook deleted successfully'
      });
    } catch (error: any) {
      if (error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      if (error.message.includes('Cannot delete webhook that is currently in use')) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to delete webhook');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to delete webhook' 
      });
    }
  }

  /**
   * Verify webhook
   */
  async verifyWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { webhookId } = request.params as { webhookId: string };

      const result = await webhookService.verifyWebhook(webhookId, user.id);

      return reply.send({
        success: true,
        webhook: result
      });
    } catch (error: any) {
      if (error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to verify webhook');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to verify webhook'
      });
    }
  }

  /**
   * Complete webhook verification with token
   */
  async completeWebhookVerification(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { webhookId } = request.params as { webhookId: string };
      const { verificationToken } = request.body as { verificationToken: string };

      if (!verificationToken) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: 'Verification token is required'
        });
      }

      const result = await webhookService.completeWebhookVerification(webhookId, user.id, verificationToken);

      return reply.send({
        success: true,
        webhook: result
      });
    } catch (error: any) {
      if (error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      if (error.message === 'Invalid verification token') {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to complete webhook verification');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to complete webhook verification'
      });
    }
  }

  /**
   * Test webhook with mock payload
   */
  async testWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { webhookId } = request.params as { webhookId: string };
      const { customPayload } = request.body as { customPayload?: { subject: string; content: { text: string; html: string } } };

      const result = await webhookService.testWebhook(webhookId, user.id, customPayload);

      return reply.send({
        success: true,
        message: 'Test webhook payload sent successfully',
        webhook: {
          id: result.webhook.id,
          url: result.webhook.url,
          name: result.webhook.name
        },
        test: {
          messageId: result.messageId,
          jobId: result.jobId,
          sentAt: result.sentAt
        }
      });
    } catch (error: any) {
      if (error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      if (error.message === 'Webhook must be verified before testing') {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to test webhook');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to test webhook'
      });
    }
  }
}
