import { FastifyRequest, FastifyReply } from 'fastify';
import { UserSettingsService, UserSettingsData } from '../../services/user/user-settings.service.js';
import { logger } from '../../utils/logger.js';

const userSettingsService = new UserSettingsService();

export class UserSettingsController {
  /**
   * Get current user's settings
   */
  async getSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      const settings = await userSettingsService.getUserSettings(user.id);
      
      return reply.send({
        success: true,
        settings
      });
    } catch (error: any) {
      logger.error({ error: error.message, userId: (request as any).user?.id }, 'Failed to get user settings');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve settings'
      });
    }
  }

  /**
   * Update user settings
   */
  async updateSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const data = request.body as UserSettingsData;

      // Validate Pro features if needed
      const validation = await userSettingsService.validateProFeatures(user.id, data);
      if (!validation.allowed) {
        return reply.status(402).send({
          statusCode: 402,
          error: 'Payment Required',
          message: validation.reason
        });
      }

      const settings = await userSettingsService.upsertUserSettings(user.id, data);
      
      return reply.send({
        success: true,
        message: 'Settings updated successfully',
        settings
      });
    } catch (error: any) {
      logger.error({ error: error.message, userId: (request as any).user?.id }, 'Failed to update user settings');
      
      // Handle validation errors
      if (error.message.includes('must be') || error.message.includes('requires')) {
        return reply.status(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }
      
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to update settings'
      });
    }
  }

  /**
   * Reset user settings to defaults
   */
  async resetSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      
      const result = await userSettingsService.deleteUserSettings(user.id);
      
      return reply.send({
        success: true,
        message: result.message
      });
    } catch (error: any) {
      logger.error({ error: error.message, userId: (request as any).user?.id }, 'Failed to reset user settings');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to reset settings'
      });
    }
  }
}
