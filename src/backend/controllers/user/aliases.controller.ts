import { FastifyRequest, FastifyReply } from 'fastify';
import { AliasService, CreateAliasData, UpdateAliasData } from '../../services/user/alias.service.js';
import { logger } from '../../utils/logger.js';

const aliasService = new AliasService();

export class AliasesController {
  /**
   * Get user's aliases
   */
  async getAliases(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const result = await aliasService.getUserAliases(user.id);
      
      return reply.send(result);
    } catch (error: any) {
      logger.error({ error: error?.message, stack: error.stack }, 'Failed to get user aliases');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve aliases' 
      });
    }
  }

  /**
   * Get specific alias by ID
   */
  async getAlias(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { aliasId } = request.params as { aliasId: string };

      const alias = await aliasService.getAliasById(aliasId, user.id);

      if (!alias) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Alias not found'
        });
      }

      return reply.send(alias);
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to get alias');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve alias' 
      });
    }
  }

  /**
   * Create new alias
   */
  async createAlias(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const body = request.body as Omit<CreateAliasData, 'userId'>;

      const createData: CreateAliasData = {
        ...body,
        userId: user.id
      };

      const result = await aliasService.createAlias(createData);

      return reply.code(201).send(result);
    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to create alias');

      if (error.message === 'Invalid email format' ||
          error.message === 'Could not extract domain from email' ||
          error.message.includes('reserved and cannot be used') ||
          error.message.includes('does not match domain')) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }

      if (error.message === 'Cannot create aliases for unverified domains') {
        return reply.code(403).send({
          statusCode: 403,
          error: 'Forbidden',
          message: error.message
        });
      }

      if (error.message === 'Domain not found' || error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      if (error.message === 'Alias with this email already exists for this domain') {
        return reply.code(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: error.message
        });
      }

      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to create alias' 
      });
    }
  }

  /**
   * Update alias
   */
  async updateAlias(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { aliasId } = request.params as { aliasId: string };
      const updates = request.body as UpdateAliasData;

      const result = await aliasService.updateAlias(aliasId, user.id, updates);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Alias not found' || error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to update alias');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to update alias' 
      });
    }
  }

  /**
   * Update alias webhook
   */
  async updateAliasWebhook(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { aliasId } = request.params as { aliasId: string };
      const { webhookId } = request.body as { webhookId: string };

      const result = await aliasService.updateAliasWebhook(aliasId, user.id, webhookId);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Alias not found' || error.message === 'Webhook not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to update alias webhook');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to update alias webhook' 
      });
    }
  }

  /**
   * Delete alias
   */
  async deleteAlias(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { aliasId } = request.params as { aliasId: string };

      const result = await aliasService.deleteAlias(aliasId, user.id);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Alias not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      if (error.message.includes('Cannot delete the last alias') ||
          error.message.includes('Cannot delete the catch-all alias')) {
        return reply.code(400).send({
          statusCode: 400,
          error: 'Bad Request',
          message: error.message
        });
      }

      logger.error({ error: error.message, stack: error.stack }, 'Failed to delete alias');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to delete alias'
      });
    }
  }

  /**
   * Get aliases for a specific domain
   */
  async getDomainAliases(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const { domainId } = request.params as { domainId: string };

      const result = await aliasService.getDomainAliases(domainId, user.id);

      return reply.send(result);
    } catch (error: any) {
      if (error.message === 'Domain not found') {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: error.message
        });
      }

      logger.error({ error: error?.message, stack: error.stack }, 'Failed to get domain aliases');
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve domain aliases' 
      });
    }
  }
}
