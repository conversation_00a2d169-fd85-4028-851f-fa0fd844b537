import { FastifyRequest, FastifyReply } from 'fastify';
import { LogsService } from '../../services/user/logs.service.js';
import { logger } from '../../utils/logger.js';

const logsService = new LogsService();

interface LogsQueryParams {
  domainId?: string;
  aliasId?: string;
  status?: 'PENDING' | 'DELIVERED' | 'FAILED' | 'RETRYING' | 'EXPIRED';
  limit?: number;
  offset?: number;
}

export class LogsController {
  /**
   * Get user's email logs
   */
  async getLogs(request: FastifyRequest, reply: FastifyReply) {
    try {
      const user = (request as any).user;
      const query = request.query as LogsQueryParams;
      
      const result = await logsService.getUserLogs(user.id, {
        domainId: query.domainId,
        aliasId: query.aliasId,
        status: query.status,
        limit: query.limit || 50,
        offset: query.offset || 0
      });
      
      return reply.send(result);
    } catch (error: any) {
      logger.error({ 
        error: error?.message, 
        stack: error.stack,
        userId: (request as any).user?.id 
      }, 'Failed to get user logs');
      
      return reply.code(500).send({ 
        statusCode: 500, 
        error: 'Internal Server Error', 
        message: 'Failed to retrieve logs' 
      });
    }
  }
}
