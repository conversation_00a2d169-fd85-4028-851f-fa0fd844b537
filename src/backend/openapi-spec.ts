import { OpenAPIV3 } from 'openapi-types';
import { publicSchemas, allResponses } from './schemas/openapi-schemas.js';

export const openApiSpecification: OpenAPIV3.Document = {
  openapi: '3.0.0',
  info: {
    title: 'EmailConnect.eu API',
    version: '1.0.0',
    description: [
      '# EmailConnect.eu API Documentation',
      '',
      'Welcome to the EmailConnect.eu API! This API allows you to programmatically manage your email domains, aliases, and webhooks.',
      '',
      '## Authentication',
      '',
      'All API endpoints require authentication using your API key. Include your API key in the request header:',
      '',
      '```',
      'X-API-KEY: your-api-key-here',
      '```',
      '',
      `You can find your API key in your [account settings](${process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000'}/settings).`,
      '',
      '### API Key Scopes',
      '',
      'API keys can be created with specific scopes to limit access to certain operations:',
      '',
      '- **Full Access** (`["*"]`) - Complete access to all endpoints',
      '- **Read Only** (`["domains:read", "domains:status", "aliases:read", "webhooks:read"]`) - Read-only access',
      '- **API User** (`["domains:read", "domains:status", "domains:config", "aliases:*", "webhooks:*"]`) - Limited domain access with full alias/webhook management',
      '',
      'Scope violations will return `403 Forbidden` responses.',
      '',
      '## Rate Limits',
      '',
      'API requests are rate-limited to ensure fair usage:',
      '- **Free Plan**: 100 requests per hour',
      '- **Pro Plan**: 1,000 requests per hour',
      '- **Enterprise Plan**: 10,000 requests per hour',
      '',
      '## Support',
      '',
      `Need help? Contact us at [<EMAIL>](mailto:<EMAIL>) or visit our [help center](${process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000'}/docs).`
    ].join('\n'),
    contact: {
      name: 'EmailConnect.eu Support',
      email: '<EMAIL>',
      url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000'
    },
    license: {
      name: 'Terms of Service',
      url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/terms-of-service' : 'http://localhost:3000/terms-of-service'
    }
  },
  servers: [
    {
      url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu' : 'http://localhost:3000',
      description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server',
    },
  ],
  components: {
    securitySchemes: {
      userApiKey: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-KEY',
        description: 'API Key for user authentication. Find your API key in your account settings.',
      },
    },
    schemas: publicSchemas,
    responses: allResponses,
  },
  tags: [
    {
      name: 'User Domains',
      description: 'Manage your email domains. Add domains, verify ownership, and configure email routing.',
      externalDocs: {
        description: 'Domain setup guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#domains' : 'http://localhost:3000/docs#domains'
      }
    },
    {
      name: 'User Aliases',
      description: 'Create and manage email aliases for your verified domains. Route specific email addresses to different webhooks.',
      externalDocs: {
        description: 'Alias configuration guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#aliases' : 'http://localhost:3000/docs#aliases'
      }
    },
    {
      name: 'User Webhooks',
      description: 'Configure webhook endpoints to receive email data. Set up multiple webhooks per domain for different processing needs.',
      externalDocs: {
        description: 'Webhook integration guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#webhooks' : 'http://localhost:3000/docs#webhooks'
      }
    },
    {
      name: 'Webhook-Alias',
      description: 'Atomic operations for creating webhooks and aliases together. Ideal for n8n integrations and simplified setup workflows.',
      externalDocs: {
        description: 'Webhook-Alias integration guide',
        url: process.env.NODE_ENV === 'production' ? 'https://emailconnect.eu/docs#webhook-alias' : 'http://localhost:3000/docs#webhook-alias'
      }
    },
  ],
  paths: {
    // User Domains API
    '/api/domains': {
      get: {
        tags: ['User Domains'],
        summary: 'List user domains',
        description: 'Retrieve all domains owned by the authenticated user.',
        security: [{ userApiKey: [] }],
        responses: {
          '200': {
            description: 'List of user domains',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/Domain' }
                }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' }
        }
      },
      post: {
        tags: ['User Domains'],
        summary: 'Add a new domain',
        description: 'Add a new domain to your account. The domain will need to be verified before it can receive emails.',
        security: [{ userApiKey: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateDomainPayload' }
            }
          }
        },
        responses: {
          '201': {
            description: 'Domain created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Domain' }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' }
        }
      }
    },
    '/api/domains/{domainId}': {
      get: {
        tags: ['User Domains'],
        summary: 'Get domain details',
        description: 'Retrieve details for a specific domain including verification status.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID'
          }
        ],
        responses: {
          '200': {
            description: 'Domain details',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Domain' }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      },
      delete: {
        tags: ['User Domains'],
        summary: 'Delete a domain',
        description: 'Delete a domain and all associated aliases and webhooks.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID'
          }
        ],
        responses: {
          '204': { description: 'Domain deleted successfully' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },
    '/api/domains/{domainId}/status': {
      put: {
        tags: ['User Domains'],
        summary: 'Toggle domain status',
        description: 'Toggle the active status of a domain by ID.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID'
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateDomainStatusRequest' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Domain status updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/UpdateDomainStatusResponse' }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },
    '/api/domains/{domainId}/verify': {
      post: {
        tags: ['User Domains'],
        summary: 'Trigger domain verification',
        description: 'Manually trigger verification for a domain. This will check DNS records and update the verification status.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID'
          }
        ],
        responses: {
          '200': {
            description: 'Verification triggered successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/DomainStatus' }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },

    // ADDED: Domain webhook management endpoint
    '/api/domains/{domainId}/webhook': {
      put: {
        tags: ['User Domains'],
        summary: 'Update domain webhook',
        description: 'Update the webhook configuration for a domain.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID'
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  webhookId: { type: 'string' }
                },
                required: ['webhookId']
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Domain webhook updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Domain' }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },

    // User Aliases API
    '/api/aliases': {
      get: {
        tags: ['User Aliases'],
        summary: 'List aliases for a domain',
        description: 'Retrieve all aliases for a specific domain.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'query',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID to filter aliases'
          }
        ],
        responses: {
          '200': {
            description: 'List of aliases',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/Alias' }
                }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      },
      post: {
        tags: ['User Aliases'],
        summary: 'Create a new alias',
        description: 'Create a new email alias for a domain.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'domainId',
            in: 'query',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopl002uru1qqyx7b18a'
            },
            description: 'Domain ID for the alias'
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateAliasPayload' }
            }
          }
        },
        responses: {
          '201': {
            description: 'Alias created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Alias' }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },
    '/api/aliases/{aliasId}': {
      get: {
        tags: ['User Aliases'],
        summary: 'Get alias details',
        description: 'Retrieve details for a specific alias.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'aliasId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopp002wru1qk18cowjg'
            },
            description: 'Alias ID'
          }
        ],
        responses: {
          '200': {
            description: 'Alias details',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Alias' }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      },
      put: {
        tags: ['User Aliases'],
        summary: 'Update an alias',
        description: 'Update the destination email for an alias.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'aliasId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopp002wru1qk18cowjg'
            },
            description: 'Alias ID'
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateAliasPayload' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Alias updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Alias' }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      },
      delete: {
        tags: ['User Aliases'],
        summary: 'Delete an alias',
        description: 'Delete an email alias.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'aliasId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw6hopp002wru1qk18cowjg'
            },
            description: 'Alias ID'
          }
        ],
        responses: {
          '204': { description: 'Alias deleted successfully' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },

    // User Webhooks API
    '/api/webhooks': {
      get: {
        tags: ['User Webhooks'],
        summary: 'List user webhooks',
        description: 'Retrieve all webhooks owned by the authenticated user.',
        security: [{ userApiKey: [] }],
        responses: {
          '200': {
            description: 'List of user webhooks',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/Webhook' }
                }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' }
        }
      },
      post: {
        tags: ['User Webhooks'],
        summary: 'Create a new webhook',
        description: 'Create a new webhook endpoint to receive email data.',
        security: [{ userApiKey: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  url: {
                    type: 'string',
                    format: 'uri',
                    example: 'https://myapp.com/webhook/email'
                  },
                  description: {
                    type: 'string',
                    example: 'Main email processing webhook'
                  }
                },
                required: ['url']
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Webhook created successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    id: { 
                      type: 'string', 
                      pattern: '^[a-z0-9]{25}$',
                      example: 'cmbw62nwc002bru1qf7d72akd'
                    },
                    url: { type: 'string', format: 'uri' },
                    description: { type: 'string' },
                    createdAt: { type: 'string', format: 'date-time' }
                  }
                }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' }
        }
      }
    },
    '/api/webhooks/{webhookId}': {
      get: {
        tags: ['User Webhooks'],
        summary: 'Get webhook details',
        description: 'Retrieve details for a specific webhook.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'webhookId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw62nwc002bru1qf7d72akd'
            },
            description: 'Webhook ID'
          }
        ],
        responses: {
          '200': {
            description: 'Webhook details',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    id: { 
                      type: 'string', 
                      pattern: '^[a-z0-9]{25}$',
                      example: 'cmbw62nwc002bru1qf7d72akd'
                    },
                    url: { type: 'string', format: 'uri' },
                    description: { type: 'string' },
                    createdAt: { type: 'string', format: 'date-time' },
                    lastTriggered: { type: 'string', format: 'date-time', nullable: true }
                  }
                }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      },
      put: {
        tags: ['User Webhooks'],
        summary: 'Update a webhook',
        description: 'Update webhook URL or description.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'webhookId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw62nwc002bru1qf7d72akd'
            },
            description: 'Webhook ID'
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  url: {
                    type: 'string',
                    format: 'uri',
                    example: 'https://myapp.com/webhook/email-updated'
                  },
                  description: {
                    type: 'string',
                    example: 'Updated email processing webhook'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Webhook updated successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    id: { 
                    type: 'string', 
                    pattern: '^[a-z0-9]{25}$',
                    example: 'cmbw62nwc002bru1qf7d72akd'
                  },
                    url: { type: 'string', format: 'uri' },
                    description: { type: 'string' },
                    updatedAt: { type: 'string', format: 'date-time' }
                  }
                }
              }
            }
          },
          '400': { $ref: '#/components/responses/BadRequest' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      },
      delete: {
        tags: ['User Webhooks'],
        summary: 'Delete a webhook',
        description: 'Delete a webhook endpoint.',
        security: [{ userApiKey: [] }],
        parameters: [
          {
            name: 'webhookId',
            in: 'path',
            required: true,
            schema: { 
              type: 'string', 
              pattern: '^[a-z0-9]{25}$',
              example: 'cmbw62nwc002bru1qf7d72akd'
            },
            description: 'Webhook ID'
          }
        ],
        responses: {
          '204': { description: 'Webhook deleted successfully' },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': { $ref: '#/components/responses/NotFound' }
        }
      }
    },

    // Webhook-Alias API
    '/api/webhooks/alias': {
      post: {
        tags: ['Webhook-Alias'],
        summary: 'Create webhook and alias in one atomic operation',
        description: `
          Creates a webhook and alias together in a single atomic operation.
          Supports both catch-all (*@domain.com) and specific (<EMAIL>) aliases.
          For catch-all aliases, optionally syncs the webhook with the domain's webhook.
          Provides auto-verification option using the last 5 characters method.
        `,
        security: [{ userApiKey: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateWebhookAliasRequest' },
              examples: {
                catchall: {
                  summary: 'Create catch-all alias with webhook',
                  value: {
                    domainId: 'domain-123',
                    webhookUrl: 'https://myapp.com/webhook',
                    webhookName: 'My Catch-All Webhook',
                    webhookDescription: 'Handles all emails for this domain',
                    aliasType: 'catchall',
                    syncWithDomain: true,
                    autoVerify: false
                  }
                },
                specific: {
                  summary: 'Create specific alias with webhook',
                  value: {
                    domainId: 'domain-123',
                    webhookUrl: 'https://myapp.com/webhook/support',
                    webhookName: 'Support Webhook',
                    webhookDescription: 'Handles support emails',
                    aliasType: 'specific',
                    localPart: 'support',
                    syncWithDomain: false,
                    autoVerify: true
                  }
                }
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'Webhook and alias created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/CreateWebhookAliasResponse' }
              }
            }
          },
          '400': {
            description: 'Bad request - validation failed',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
              }
            }
          },
          '401': { $ref: '#/components/responses/Unauthorized' },
          '404': {
            description: 'Domain not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
              }
            }
          },
          '409': {
            description: 'Conflict - alias already exists',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/WebhookAliasErrorResponse' }
              }
            }
          }
        }
      }
    }
  },
};

// Helper function to merge paths from route files
export function mergeRoutePaths(additionalPaths: Record<string, OpenAPIV3.PathItemObject>) {
  return {
    ...openApiSpecification,
    paths: {
      ...openApiSpecification.paths,
      ...additionalPaths,
    },
  };
}
