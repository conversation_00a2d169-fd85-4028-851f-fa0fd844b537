import { createMollieClient, MollieApiError, PaymentMethod } from '@mollie/api-client';
import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';
import crypto from 'crypto';

export class MollieService {
  private client;

  constructor() {
    if (!env.MOLLIE_API_KEY) {
      logger.warn('MOLLIE_API_KEY not configured - payment processing will be disabled');
      return;
    }

    this.client = createMollieClient({
      apiKey: env.MOLLIE_API_KEY,
    });

    logger.info({ 
      testMode: env.MOLLIE_TEST_MODE 
    }, 'Mollie service initialized');
  }

  /**
   * Check if <PERSON><PERSON> is properly configured
   */
  isConfigured(): boolean {
    return !!this.client && !!env.MOLLIE_API_KEY;
  }

  /**
   * Create a new payment
   */
  async createPayment(params: {
    amount: { value: string; currency: string };
    description: string;
    redirectUrl: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
    method?: string;
    methods?: string[];
  }) {
    if (!this.isConfigured()) {
      throw new Error('<PERSON>llie service not configured');
    }

    try {
      const paymentData: any = {
        amount: params.amount,
        description: params.description,
        redirectUrl: params.redirectUrl,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
      };

      // For test environment, use a specific method that's commonly available
      if (env.MOLLIE_TEST_MODE) {
        paymentData.method = params.method || PaymentMethod.ideal;
      } else if (params.method) {
        paymentData.method = params.method;
      }
      // If no method is specified in production, Mollie will show all available payment methods

      const payment = await this.client.payments.create(paymentData);

      logger.info({ 
        paymentId: payment.id,
        amount: params.amount,
        description: params.description 
      }, 'Payment created successfully');

      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating payment');
      } else {
        logger.error({ error }, 'Unexpected error creating payment');
      }
      throw error;
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const payment = await this.client.payments.get(paymentId);
      return payment;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          paymentId,
          error: error.message,
          field: error.field
        }, 'Mollie API error getting payment');
      } else {
        logger.error({ paymentId, error }, 'Unexpected error getting payment');
      }
      throw error;
    }
  }

  /**
   * Create a customer for recurring payments
   */
  async createCustomer(params: {
    name: string;
    email: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const customer = await this.client.customers.create({
        name: params.name,
        email: params.email,
        metadata: params.metadata,
      });

      logger.info({ 
        customerId: customer.id,
        email: params.email 
      }, 'Customer created successfully');

      return customer;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating customer');
      } else {
        logger.error({ error }, 'Unexpected error creating customer');
      }
      throw error;
    }
  }

  /**
   * Create a subscription
   */
  async createSubscription(params: {
    customerId: string;
    amount: { value: string; currency: string };
    interval: string;
    description: string;
    webhookUrl?: string;
    metadata?: Record<string, any>;
  }) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.create({
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval,
        description: params.description,
        webhookUrl: params.webhookUrl || env.MOLLIE_WEBHOOK_URL,
        metadata: params.metadata,
      });

      logger.info({ 
        subscriptionId: subscription.id,
        customerId: params.customerId,
        amount: params.amount,
        interval: params.interval 
      }, 'Subscription created successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          error: error.message,
          field: error.field
        }, 'Mollie API error creating subscription');
      } else {
        logger.error({ error }, 'Unexpected error creating subscription');
      }
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(customerId: string, subscriptionId: string) {
    if (!this.isConfigured()) {
      throw new Error('Mollie service not configured');
    }

    try {
      const subscription = await this.client.customerSubscriptions.cancel(subscriptionId, {
        customerId,
      });

      logger.info({ 
        subscriptionId,
        customerId 
      }, 'Subscription cancelled successfully');

      return subscription;
    } catch (error) {
      if (error instanceof MollieApiError) {
        logger.error({
          subscriptionId,
          customerId,
          error: error.message,
          field: error.field
        }, 'Mollie API error cancelling subscription');
      } else {
        logger.error({ subscriptionId, customerId, error }, 'Unexpected error cancelling subscription');
      }
      throw error;
    }
  }

  /**
   * Verify webhook signature (if webhook secret is configured)
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    if (!env.MOLLIE_WEBHOOK_SECRET) {
      logger.warn('MOLLIE_WEBHOOK_SECRET not configured - webhook signature verification disabled');
      return true; // Allow webhook if no secret is configured
    }

    const expectedSignature = crypto
      .createHmac('sha256', env.MOLLIE_WEBHOOK_SECRET)
      .update(body)
      .digest('hex');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValid) {
      logger.warn({ signature, expectedSignature }, 'Invalid webhook signature');
    }

    return isValid;
  }
}

// Export singleton instance
export const mollieService = new MollieService();
