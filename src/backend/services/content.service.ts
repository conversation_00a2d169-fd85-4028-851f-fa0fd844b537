import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import matter from 'gray-matter';

export interface ChangelogEntry {
  slug: string;
  date: string;
  type: 'added' | 'changed' | 'fixed';
  title: string;
  excerpt: string;
  content: string;
}

export interface HelpArticle {
  slug: string;
  title: string;
  excerpt: string;
  category: string;
  order: number;
  content: string;
}

export interface StaticPage {
  title: string;
  slug: string;
  lastUpdated: string;
  description: string;
  content: string;
}

export class ContentService {
  private contentDir = path.join(process.cwd(), 'content');
  private cache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes in development, can be longer in production

  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    return expiry ? Date.now() < expiry : false;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
  }

  private getCache(key: string): any {
    if (this.isCacheValid(key)) {
      return this.cache.get(key);
    }
    // Clean up expired cache
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
    return null;
  }

  async getChangelogEntries(): Promise<ChangelogEntry[]> {
    const cacheKey = 'changelog-entries';
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const changelogDir = path.join(this.contentDir, 'changelog');
      
      // Check if directory exists
      try {
        await fs.access(changelogDir);
      } catch {
        // Directory doesn't exist, return empty array
        return [];
      }

      const files = await fs.readdir(changelogDir);

      const entries = await Promise.all(
        files
          .filter(file => file.endsWith('.md'))
          .map(async (file) => {
            try {
              const filePath = path.join(changelogDir, file);
              const fileContent = await fs.readFile(filePath, 'utf-8');
              const { data, content } = matter(fileContent);

              return {
                slug: file.replace('.md', ''),
                date: data.date || '',
                type: data.type || 'changed',
                title: data.title || 'Untitled',
                excerpt: data.excerpt || '',
                content: content || ''
              } as ChangelogEntry;
            } catch (error) {
              console.error(`Error processing changelog file ${file}:`, error);
              return null;
            }
          })
      );

      // Filter out null entries and sort by date descending
      const validEntries = entries
        .filter((entry): entry is ChangelogEntry => entry !== null)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      this.setCache(cacheKey, validEntries);
      return validEntries;
    } catch (error) {
      console.error('Error reading changelog entries:', error);
      return [];
    }
  }

  async getHelpArticles(): Promise<HelpArticle[]> {
    const cacheKey = 'help-articles';
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const helpDir = path.join(this.contentDir, 'help');
      
      // Check if directory exists
      try {
        await fs.access(helpDir);
      } catch {
        // Directory doesn't exist, return empty array
        return [];
      }

      const files = await fs.readdir(helpDir);

      const articles = await Promise.all(
        files
          .filter(file => file.endsWith('.md'))
          .map(async (file) => {
            try {
              const filePath = path.join(helpDir, file);
              const fileContent = await fs.readFile(filePath, 'utf-8');
              const { data, content } = matter(fileContent);

              return {
                slug: data.slug || file.replace('.md', ''),
                title: data.title || 'Untitled',
                excerpt: data.excerpt || '',
                category: data.category || 'general',
                order: data.order || 999,
                content: content || ''
              } as HelpArticle;
            } catch (error) {
              console.error(`Error processing help file ${file}:`, error);
              return null;
            }
          })
      );

      // Filter out null entries and sort by order, then by title
      const validArticles = articles
        .filter((article): article is HelpArticle => article !== null)
        .sort((a, b) => {
          if (a.order !== b.order) {
            return a.order - b.order;
          }
          return a.title.localeCompare(b.title);
        });

      this.setCache(cacheKey, validArticles);
      return validArticles;
    } catch (error) {
      console.error('Error reading help articles:', error);
      return [];
    }
  }

  async getHelpArticleBySlug(slug: string): Promise<HelpArticle | null> {
    const articles = await this.getHelpArticles();
    return articles.find(article => article.slug === slug) || null;
  }

  async getChangelogEntryBySlug(slug: string): Promise<ChangelogEntry | null> {
    const entries = await this.getChangelogEntries();
    return entries.find(entry => entry.slug === slug) || null;
  }

  // Method to clear cache (useful for development or when content is updated)
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  // Method to get cache statistics (useful for monitoring)
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // Static pages methods
  async getStaticPages(): Promise<StaticPage[]> {
    const cacheKey = 'static-pages';

    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const staticDir = path.join(this.contentDir, 'static');

      if (!fsSync.existsSync(staticDir)) {
        return [];
      }

      const files = fsSync.readdirSync(staticDir)
        .filter(file => file.endsWith('.html'))
        .sort();

      const pages: StaticPage[] = [];

      for (const file of files) {
        const filePath = path.join(staticDir, file);
        const fileContent = fsSync.readFileSync(filePath, 'utf-8');
        const { data: frontmatter, content } = matter(fileContent);

        const page: StaticPage = {
          title: frontmatter.title || 'Untitled',
          slug: frontmatter.slug || path.basename(file, '.html'),
          lastUpdated: frontmatter.lastUpdated || 'Unknown',
          description: frontmatter.description || '',
          content: content.trim()
        };

        pages.push(page);
      }

      this.cache.set(cacheKey, pages);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);

      return pages;
    } catch (error) {
      console.error('Error reading static pages:', error);
      return [];
    }
  }

  async getStaticPageBySlug(slug: string): Promise<StaticPage | null> {
    const pages = await this.getStaticPages();
    return pages.find(page => page.slug === slug) || null;
  }
}
