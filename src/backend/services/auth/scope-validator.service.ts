import { FastifyRequest } from 'fastify'
import { logger } from '../../utils/logger.js'

export interface ScopeValidationResult {
  allowed: boolean
  reason?: string
}

/**
 * Service for validating API key scopes against request routes and methods
 */
export class ScopeValidatorService {
  
  /**
   * Predefined scope configurations for easy selection
   */
  static readonly PRESET_SCOPES = {
    'full-access': ['*'],
    'read-only': ['domains:read', 'domains:status', 'aliases:read', 'webhooks:read'],
    'api-user': ['domains:read', 'domains:status', 'domains:config', 'aliases:*', 'webhooks:*'],
  } as const

  /**
   * Map routes to required scopes based on the OpenAPI spec
   */
  private static readonly ROUTE_SCOPE_MAP: Record<string, string> = {
    // Domain routes
    'GET:/api/domains': 'domains:read',
    'POST:/api/domains': 'domains:write',
    'GET:/api/domains/:id': 'domains:read',
    'GET:/api/domains/:domain': 'domains:read',
    'PUT:/api/domains/:id': 'domains:config', // Special handling for configuration updates
    'PUT:/api/domains/:id/status': 'domains:write', // Domain status toggle
    'PUT:/api/domains/:id/webhook': 'domains:write', // Domain webhook update
    'GET:/api/domains/:id/spam-filter': 'domain:spam_filter', // Get spam filtering settings
    'PUT:/api/domains/:id/spam-filter': 'domain:spam_filter', // Update spam filtering settings
    'DELETE:/api/domains/:id': 'domains:write', // Domain deletion
    'DELETE:/api/domains/:domain': 'domains:write',
    'GET:/api/domains/:id/status': 'domains:status',
    'POST:/api/domains/:id/verify': 'domains:write', // Domain verification
    'POST:/api/domains/:domain/verify': 'domains:write',
    
    // Alias routes
    'GET:/api/aliases': 'aliases:read',
    'POST:/api/aliases': 'aliases:write', 
    'GET:/api/aliases/:aliasId': 'aliases:read',
    'PUT:/api/aliases/:aliasId': 'aliases:write',
    'DELETE:/api/aliases/:aliasId': 'aliases:write',
    'GET:/api/domains/:domainId/aliases': 'aliases:read',
    
    // Webhook routes
    'GET:/api/webhooks': 'webhooks:read',
    'POST:/api/webhooks': 'webhooks:write',
    'GET:/api/webhooks/:webhookId': 'webhooks:read',
    'PUT:/api/webhooks/:webhookId': 'webhooks:write',
    'DELETE:/api/webhooks/:webhookId': 'webhooks:write',
  }

  /**
   * Validate if the given scopes allow access to a specific route and method
   */
  static validateRequest(
    userScopes: string[], 
    request: FastifyRequest
  ): ScopeValidationResult {
    // Full access wildcard
    if (userScopes.includes('*')) {
      return { allowed: true }
    }

    const routeKey = this.getRouteKey(request)
    const requiredScope = this.ROUTE_SCOPE_MAP[routeKey]
    
    if (!requiredScope) {
      logger.warn({ routeKey, url: request.url, method: request.method }, 'No scope mapping found for route')
      return { allowed: false, reason: `Route not found in scope mapping: ${routeKey}` }
    }

    // Check if user has the required scope
    const hasRequiredScope = this.checkScopePermission(userScopes, requiredScope)
    
    if (!hasRequiredScope) {
      logger.warn({ 
        userScopes, 
        requiredScope, 
        routeKey, 
        url: request.url, 
        method: request.method 
      }, 'Scope validation failed')
      return { 
        allowed: false, 
        reason: `Insufficient permissions. Required scope: ${requiredScope}` 
      }
    }

    // Special validation for domain configuration updates
    if (requiredScope === 'domains:config' && request.method === 'PUT') {
      return this.validateDomainConfigRequest(request)
    }

    logger.debug({ 
      userScopes, 
      requiredScope, 
      routeKey 
    }, 'Scope validation passed')
    
    return { allowed: true }
  }

  /**
   * Check if user scopes include the required permission
   */
  private static checkScopePermission(userScopes: string[], requiredScope: string): boolean {
    // Direct match
    if (userScopes.includes(requiredScope)) {
      return true
    }

    // Check for wildcard matches
    const [resource, action] = requiredScope.split(':')
    
    // Resource wildcard (e.g., "aliases:*" allows "aliases:read", "aliases:write")
    if (userScopes.includes(`${resource}:*`)) {
      return true
    }

    return false
  }

  /**
   * Generate route key from request for scope mapping lookup
   */
  private static getRouteKey(request: FastifyRequest): string {
    const method = request.method
    let url = request.url
    
    // Remove query parameters
    url = url.split('?')[0]
    
    // Normalize route parameters to use consistent parameter names
    url = url.replace(/\/[a-f0-9-]{20,}/g, '/:id') // UUIDs/CUIDs
    url = url.replace(/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/|$)/g, '/:domain$1') // Domain names
    url = url.replace(/\/alias_[a-f0-9-]+/g, '/:aliasId') // Alias IDs
    url = url.replace(/\/webhook_[a-f0-9-]+/g, '/:webhookId') // Webhook IDs
    
    return `${method}:${url}`
  }

  /**
   * Special validation for domain configuration updates
   * Only allows changes to configuration.allowAttachments and configuration.includeEnvelopeData
   */
  private static validateDomainConfigRequest(request: FastifyRequest): ScopeValidationResult {
    const body = request.body as any
    
    if (!body || typeof body !== 'object') {
      return { allowed: false, reason: 'Invalid request body' }
    }

    // Get all the keys being modified
    const modifiedKeys = Object.keys(body)
    
    // Only allow configuration changes
    const allowedKeys = ['configuration']
    const hasDisallowedKeys = modifiedKeys.some(key => !allowedKeys.includes(key))
    
    if (hasDisallowedKeys) {
      return { 
        allowed: false, 
        reason: 'domains:config scope only allows modification of configuration fields'
      }
    }

    // If configuration is being modified, validate the specific fields
    if (body.configuration && typeof body.configuration === 'object') {
      const configKeys = Object.keys(body.configuration)
      const allowedConfigKeys = ['allowAttachments', 'includeEnvelopeData']
      const hasDisallowedConfigKeys = configKeys.some(key => !allowedConfigKeys.includes(key))
      
      if (hasDisallowedConfigKeys) {
        return {
          allowed: false,
          reason: 'domains:config scope only allows modification of allowAttachments and includeEnvelopeData'
        }
      }
    }

    return { allowed: true }
  }

  /**
   * Get human-readable description of scopes
   */
  static getScopeDescription(scopes: string[]): string {
    if (scopes.includes('*')) {
      return 'Full access to all API endpoints'
    }

    const descriptions: string[] = []
    
    if (scopes.includes('domains:read') || scopes.includes('domains:*')) {
      descriptions.push('Read domain information')
    }
    if (scopes.includes('domains:write') || scopes.includes('domains:*')) {
      descriptions.push('Create and delete domains')
    }
    if (scopes.includes('domains:config')) {
      descriptions.push('Update domain configuration')
    }
    if (scopes.includes('domains:status')) {
      descriptions.push('Check domain verification status')
    }
    if (scopes.includes('aliases:read') || scopes.includes('aliases:*')) {
      descriptions.push('Read alias information')
    }
    if (scopes.includes('aliases:write') || scopes.includes('aliases:*')) {
      descriptions.push('Create, update, and delete aliases')
    }
    if (scopes.includes('webhooks:read') || scopes.includes('webhooks:*')) {
      descriptions.push('Read webhook information')
    }
    if (scopes.includes('webhooks:write') || scopes.includes('webhooks:*')) {
      descriptions.push('Create, update, and delete webhooks')
    }

    return descriptions.length > 0 ? descriptions.join(', ') : 'No permissions'
  }

  /**
   * Validate scope format
   */
  static validateScopeFormat(scopes: string[]): { valid: boolean; error?: string } {
    if (!Array.isArray(scopes) || scopes.length === 0) {
      return { valid: false, error: 'Scopes must be a non-empty array' }
    }

    const validScopes = [
      '*',
      'domains:read', 'domains:write', 'domains:config', 'domains:status', 'domains:*',
      'aliases:read', 'aliases:write', 'aliases:*',
      'webhooks:read', 'webhooks:write', 'webhooks:*'
    ]

    for (const scope of scopes) {
      if (typeof scope !== 'string' || !validScopes.includes(scope)) {
        return { valid: false, error: `Invalid scope: ${scope}` }
      }
    }

    return { valid: true }
  }

  /**
   * Get available scopes with descriptions
   */
  static getAvailableScopes(): Record<string, string> {
    return {
      '*': 'Full access to all API endpoints',
      'domains:read': 'Read domain information',
      'domains:write': 'Create and delete domains',
      'domains:config': 'Update domain configuration (allowAttachments, includeEnvelopeData)',
      'domains:status': 'Check domain verification status',
      'domains:*': 'Full access to domain endpoints',
      'aliases:read': 'Read alias information',
      'aliases:write': 'Create, update, and delete aliases',
      'aliases:*': 'Full access to alias endpoints',
      'webhooks:read': 'Read webhook information',
      'webhooks:write': 'Create, update, and delete webhooks',
      'webhooks:*': 'Full access to webhook endpoints'
    }
  }
}
