import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { env } from '../../config/env.js';
import { logger } from '../../utils/logger.js';

// Admin auth constants
const ADMIN_USERNAME = env.ADMIN_USERNAME;
const ADMIN_PASSWORD = env.ADMIN_PASSWORD;
const ADMIN_JWT_SECRET = env.ADMIN_JWT_SECRET;
const ADMIN_JWT_EXPIRES_IN = env.ADMIN_JWT_EXPIRES_IN;

export interface AdminLoginData {
  username: string;
  password: string;
}

export interface AdminTokenPayload {
  username: string;
  role: string;
  iat: number;
  exp: number;
}

export class AdminAuthService {
  private hashedPassword: string = '';

  constructor() {
    this.validateEnvironment();
    this.initializePassword();
  }

  private validateEnvironment() {
    if (!ADMIN_PASSWORD) {
      logger.error('CRITICAL: ADMIN_PASSWORD is not set in environment variables. Admin login will fail.');
    }
    if (!ADMIN_JWT_SECRET || typeof ADMIN_JWT_SECRET !== 'string' || ADMIN_JWT_SECRET.trim() === '') {
      logger.error('CRITICAL: ADMIN_JWT_SECRET is not set, not a string, or is empty in environment variables. JWT signing will fail.');
    }
    if (!ADMIN_JWT_EXPIRES_IN || typeof ADMIN_JWT_EXPIRES_IN !== 'string' || ADMIN_JWT_EXPIRES_IN.trim() === '') {
      logger.error('CRITICAL: ADMIN_JWT_EXPIRES_IN is not set, not a string, or is empty in environment variables. JWT signing will fail.');
    }
  }

  private async initializePassword() {
    if (ADMIN_PASSWORD) {
      // Basic check to see if it might already be a bcrypt hash
      if (ADMIN_PASSWORD.startsWith('$2b$') || ADMIN_PASSWORD.startsWith('$2a$')) {
        this.hashedPassword = ADMIN_PASSWORD;
      } else {
        try {
          const saltRounds = 10;
          this.hashedPassword = await bcrypt.hash(ADMIN_PASSWORD, saltRounds);
          logger.info('Admin password has been hashed.');
        } catch (error) {
          logger.error('Failed to hash admin password:', error);
          process.exit(1);
        }
      }
    } else {
      logger.error('ADMIN_PASSWORD is not set. Admin login will not function.');
    }
  }

  /**
   * Authenticate admin credentials
   */
  async authenticateAdmin(credentials: AdminLoginData): Promise<{ success: boolean; error?: string }> {
    if (!credentials.username || !credentials.password) {
      return { success: false, error: 'Username and password are required' };
    }

    if (credentials.username !== ADMIN_USERNAME) {
      return { success: false, error: 'Invalid credentials' };
    }

    if (!this.hashedPassword) {
      logger.error('Admin password hash is not available. Login failed.');
      return { success: false, error: 'Internal server error: Admin auth not configured' };
    }

    try {
      const passwordMatch = await bcrypt.compare(credentials.password, this.hashedPassword);
      if (!passwordMatch) {
        return { success: false, error: 'Invalid credentials' };
      }
    } catch (err) {
      logger.error({ err }, 'Error comparing password for admin login');
      return { success: false, error: 'Internal server error during authentication' };
    }

    return { success: true };
  }

  /**
   * Generate admin JWT token
   */
  generateToken(): { success: boolean; token?: string; error?: string } {
    // Explicit check before signing
    if (!ADMIN_JWT_SECRET || typeof ADMIN_JWT_SECRET !== 'string' || ADMIN_JWT_SECRET.trim() === '') {
      logger.error('ADMIN_JWT_SECRET is invalid or not configured.');
      return { success: false, error: 'Internal server error: Auth configuration problem' };
    }

    if (!ADMIN_JWT_EXPIRES_IN || typeof ADMIN_JWT_EXPIRES_IN !== 'string' || ADMIN_JWT_EXPIRES_IN.trim() === '') {
      logger.error('ADMIN_JWT_EXPIRES_IN is invalid or not configured.');
      return { success: false, error: 'Internal server error: Auth configuration problem' };
    }

    try {
      const payload = { username: ADMIN_USERNAME, role: 'admin' };
      const secret: jwt.Secret = ADMIN_JWT_SECRET;
      const options: jwt.SignOptions = { expiresIn: ADMIN_JWT_EXPIRES_IN as any };

      const token = jwt.sign(payload, secret, options);
      return { success: true, token };
    } catch (signError: any) {
      logger.error({
        err: signError,
        ADMIN_JWT_SECRET_type: typeof ADMIN_JWT_SECRET,
        ADMIN_JWT_EXPIRES_IN_type: typeof ADMIN_JWT_EXPIRES_IN,
      }, 'Failed to sign JWT for admin');
      return { success: false, error: 'Internal server error: Failed to generate admin token' };
    }
  }

  /**
   * Verify admin JWT token
   */
  verifyToken(token: string): { success: boolean; payload?: AdminTokenPayload; error?: string } {
    try {
      const decoded = jwt.verify(token, ADMIN_JWT_SECRET) as AdminTokenPayload;
      if (decoded.role !== 'admin' || decoded.username !== ADMIN_USERNAME) {
        return { success: false, error: 'Invalid token role or username' };
      }
      return { success: true, payload: decoded };
    } catch (error) {
      logger.warn({ err: error }, 'Admin JWT verification failed');
      return { success: false, error: 'Invalid token' };
    }
  }

  /**
   * Get cookie configuration for admin token
   */
  getCookieConfig() {
    return {
      path: '/',
      httpOnly: true,
      secure: env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: parseInt(ADMIN_JWT_EXPIRES_IN) * 1000,
    };
  }
}
