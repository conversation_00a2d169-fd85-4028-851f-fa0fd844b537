import { promises as dns } from 'dns';
import { logger } from '../utils/logger.js';
import { env } from '../config/env.js';

// Cache interface for storing verification results
interface CacheEntry {
  result: boolean;
  timestamp: number;
  error?: string;
}

export interface DNSVerificationResult {
  verified: boolean;
  domain: string;
  expectedRecord: string;
  foundRecords: string[];
  cached: boolean;
  error?: string;
  timestamp: string;
}

export class DNSVerifier {
  private cache = new Map<string, CacheEntry>();
  private readonly verificationPrefix = 'verify-ec=';

  /**
   * Verify domain ownership via DNS TXT record
   * Looks for TXT record with format: verify-ec={domain}
   */
  async verifyDomainOwnership(domain: string): Promise<DNSVerificationResult> {
    const startTime = Date.now();
    
    try {
      logger.info({ domain }, 'Starting DNS verification for domain');

      // Check cache first
      const cachedResult = this.getCachedResult(domain);
      if (cachedResult) {
        logger.debug({ domain }, 'Returning cached DNS verification result');
        return {
          verified: cachedResult.result,
          domain,
          expectedRecord: `${this.verificationPrefix}${domain}`,
          foundRecords: [],
          cached: true,
          error: cachedResult.error,
          timestamp: new Date().toISOString(),
        };
      }

      // Perform DNS lookup with retry logic
      const { verified, foundRecords, error } = await this.performDNSLookupWithRetry(domain);

      // Cache the result
      this.cacheResult(domain, verified, error);

      const result: DNSVerificationResult = {
        verified,
        domain,
        expectedRecord: `${this.verificationPrefix}${domain}`,
        foundRecords,
        cached: false,
        error,
        timestamp: new Date().toISOString(),
      };

      const duration = Date.now() - startTime;
      logger.info({ 
        domain, 
        verified, 
        duration,
        foundRecords: foundRecords.length
      }, 'DNS verification completed');

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error({ 
        domain, 
        error: error.message, 
        duration 
      }, 'DNS verification failed');

      // Cache failed result to prevent repeated failures
      this.cacheResult(domain, false, error.message);

      return {
        verified: false,
        domain,
        expectedRecord: `${this.verificationPrefix}${domain}`,
        foundRecords: [],
        cached: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Perform DNS TXT record lookup with retry logic
   */
  private async performDNSLookupWithRetry(domain: string): Promise<{
    verified: boolean;
    foundRecords: string[];
    error?: string;
  }> {
    const expectedRecord = `${this.verificationPrefix}${domain}`;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= env.DNS_VERIFICATION_RETRY_ATTEMPTS; attempt++) {
      try {
        logger.debug({ domain, attempt }, 'Attempting DNS TXT lookup');

        // Perform TXT record lookup with timeout
        const txtRecords = await Promise.race([
          dns.resolveTxt(domain),
          this.createTimeoutPromise<string[][]>(env.DNS_VERIFICATION_TIMEOUT_MS)
        ]);

        // Flatten the TXT records (they come as arrays of strings)
        const flatRecords: string[] = [];
        for (const recordArray of txtRecords) {
          for (const record of recordArray) {
            flatRecords.push(record);
          }
        }
        
        logger.debug({ 
          domain, 
          foundRecords: flatRecords.length,
          records: flatRecords
        }, 'DNS TXT records found');

        // Check if our verification record exists
        const verified = flatRecords.some(record => 
          record.trim() === expectedRecord
        );

        return {
          verified,
          foundRecords: flatRecords,
        };

      } catch (error) {
        lastError = error;
        logger.warn({ 
          domain, 
          attempt, 
          maxAttempts: env.DNS_VERIFICATION_RETRY_ATTEMPTS,
          error: error.message 
        }, 'DNS lookup attempt failed');

        // Wait before retry (exponential backoff)
        if (attempt < env.DNS_VERIFICATION_RETRY_ATTEMPTS) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await this.delay(delay);
        }
      }
    }

    // All attempts failed
    throw lastError || new Error('DNS verification failed after all retry attempts');
  }

  /**
   * Create a timeout promise that rejects after specified milliseconds
   */
  private createTimeoutPromise<T>(timeoutMs: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`DNS lookup timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  /**
   * Get cached verification result if still valid
   */
  private getCachedResult(domain: string): CacheEntry | null {
    const cached = this.cache.get(domain);
    if (!cached) {
      return null;
    }

    const isExpired = Date.now() - cached.timestamp > env.DNS_VERIFICATION_CACHE_TTL_MS;
    if (isExpired) {
      this.cache.delete(domain);
      return null;
    }

    return cached;
  }

  /**
   * Cache verification result
   */
  private cacheResult(domain: string, result: boolean, error?: string): void {
    this.cache.set(domain, {
      result,
      timestamp: Date.now(),
      error,
    });

    logger.debug({ domain, result, cached: true }, 'DNS verification result cached');
  }

  /**
   * Clear cache for a specific domain
   */
  public clearCache(domain?: string): void {
    if (domain) {
      this.cache.delete(domain);
      logger.debug({ domain }, 'DNS verification cache cleared for domain');
    } else {
      this.cache.clear();
      logger.debug('DNS verification cache cleared completely');
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  public getCacheStats(): { size: number; entries: Array<{ domain: string; cached: boolean; timestamp: string }> } {
    const entries = Array.from(this.cache.entries()).map(([domain, entry]) => ({
      domain,
      cached: entry.result,
      timestamp: new Date(entry.timestamp).toISOString(),
    }));

    return {
      size: this.cache.size,
      entries,
    };
  }

  /**
   * Utility function to create delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate the expected TXT record value for a domain  
   * This is a utility function for displaying instructions to users
   */
  public static getExpectedTXTRecord(domain: string): string {
    return `verify-ec=${domain}`;
  }

  /**
   * Validate domain name format
   */
  public static isValidDomain(domain: string): boolean {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.([a-zA-Z]{2,}\.?)+$/;
    return domainRegex.test(domain) && domain.length <= 253;
  }
}
