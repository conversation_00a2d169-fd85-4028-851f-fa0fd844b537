import Bull from 'bull';
import { Redis } from 'ioredis';
import type { Redis as RedisType } from 'ioredis';
import axios from 'axios';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';
import { AnyWebhookPayload, InternalQueuePayload } from '../types/index.js';
import { prisma } from '../lib/prisma.js';

interface WebhookJobData {
  webhookUrl: string;
  payload: InternalQueuePayload;
  webhookSecret?: string;
  customHeaders?: Record<string, string>;
}

let webhookQueue: Bull.Queue<WebhookJobData>;
let redisClient: RedisType;

export async function initializeQueue() {
  redisClient = new Redis(env.REDIS_URL);
  
  webhookQueue = new Bull('webhook-delivery', {
    redis: env.REDIS_URL,
    defaultJobOptions: {
      attempts: env.WEBHOOK_RETRY_ATTEMPTS,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100, // Keep last 100 completed jobs
      removeOnFail: 50, // Keep last 50 failed jobs
    },
  });

  // Process webhook delivery jobs
  webhookQueue.process('webhook-delivery', async (job) => {
    const { webhookUrl, payload, webhookSecret, customHeaders } = job.data;

    try {
      // Get messageId from either old or new payload structure
      const messageId = getMessageId(payload);

      logger.debug({
        webhookUrl,
        messageId,
        hasSecret: !!webhookSecret,
        payloadType: 'type' in payload ? payload.type : 'email'
      }, 'Delivering webhook');

      // Update delivery attempt in database
      await updateEmailDeliveryAttempt(messageId);

      // Prepare webhook payload by removing internal tracking fields
      const webhookPayload = { ...payload };
      delete webhookPayload._internalMessageId;

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'EmailConnect/1.0',
        'X-Email-Webhook': 'true',
        ...customHeaders // Merge custom headers
      };

      // Add HMAC signature if webhook secret is provided
      if (webhookSecret) {
        const crypto = await import('crypto');
        const payloadString = JSON.stringify(webhookPayload);
        const signature = crypto
          .createHmac('sha256', webhookSecret)
          .update(payloadString, 'utf8')
          .digest('hex');

        headers['X-Webhook-Signature'] = `sha256=${signature}`;
        headers['X-Webhook-Timestamp'] = Math.floor(Date.now() / 1000).toString();
      }

      const response = await axios.post(webhookUrl, webhookPayload, {
        timeout: env.WEBHOOK_TIMEOUT_MS,
        headers,
      });

      logger.info({
        webhookUrl,
        messageId,
        status: response.status,
        hasSignature: !!webhookSecret
      }, 'Webhook delivered successfully');

      // Update email status to DELIVERED on success
      await updateEmailDeliveryStatus(messageId, 'DELIVERED', null);

      return { status: 'delivered', httpStatus: response.status };
    } catch (error: any) {
      const messageId = getMessageId(payload);
      logger.error({
        error: error?.message,
        webhookUrl,
        messageId,
        attemptsMade: job.attemptsMade,
        maxAttempts: job.opts.attempts
      }, 'Webhook delivery failed');

      // Update email status based on whether this is the final attempt
      const isLastAttempt = job.attemptsMade >= (job.opts.attempts || 1);

      console.log(`📊 Webhook job status for message ${messageId}:`, {
        attemptsMade: job.attemptsMade,
        maxAttempts: job.opts.attempts,
        isLastAttempt,
        jobId: job.id,
        timestamp: new Date().toISOString(),
        error: error?.message
      });

      if (isLastAttempt) {
        await updateEmailDeliveryStatus(messageId, 'FAILED', error?.message || 'Unknown error');
        console.log(`❌ Webhook delivery failed permanently for message ${messageId} after ${job.attemptsMade} attempts`);
      } else {
        await updateEmailDeliveryStatus(messageId, 'RETRYING', error?.message || 'Unknown error');
        console.log(`🔄 Webhook delivery failed for message ${messageId}, will retry (attempt ${job.attemptsMade}/${job.opts.attempts})`);
      }

      throw error; // This will trigger retry logic
    }
  });

  // Log queue events
  webhookQueue.on('completed', (job) => {
    logger.info({ jobId: job.id }, 'Webhook job completed');
  });

  webhookQueue.on('failed', (job, err) => {
    logger.error({ jobId: job.id, error: err.message }, 'Webhook job failed');
  });

  logger.info('Webhook queue initialized');
}

export async function queueWebhookDelivery(
  webhookUrl: string,
  payload: InternalQueuePayload,
  webhookSecret?: string,
  customHeaders?: Record<string, string>
) {
  if (!webhookQueue) {
    throw new Error('Queue not initialized');
  }

  const messageId = getMessageId(payload);

  const job = await webhookQueue.add('webhook-delivery', {
    webhookUrl,
    payload,
    webhookSecret,
    customHeaders,
  }, {
    priority: 1,
    delay: 0,
  });

  logger.debug({
    jobId: job.id,
    webhookUrl,
    messageId,
    hasSecret: !!webhookSecret,
    hasCustomHeaders: !!customHeaders && Object.keys(customHeaders).length > 0
  }, 'Webhook queued for delivery');

  return job.id;
}

// Helper function to get messageId from either old or new payload structure
function getMessageId(payload: InternalQueuePayload): string {
  // Check if it's a webhook verification payload (not an email)
  if ('type' in payload && payload.type === 'webhook_verification') {
    return 'webhook_verification'; // Special identifier for verification payloads
  }

  // Check for preserved internal messageId (used when envelope is filtered out)
  if ('_internalMessageId' in payload && payload._internalMessageId) {
    return payload._internalMessageId;
  }

  // Check if it's the new enhanced structure
  if ('envelope' in payload && payload.envelope?.messageId) {
    return payload.envelope.messageId;
  }

  // Check if it's the old structure
  if ('messageId' in payload && payload.messageId) {
    return payload.messageId;
  }

  return 'unknown';
}

export function getQueue() {
  return webhookQueue;
}

export function getRedisClient() {
  return redisClient;
}

/**
 * Update email delivery attempt count and timestamp
 */
async function updateEmailDeliveryAttempt(messageId: string) {
  // Skip update if messageId is unknown/invalid or for webhook verification
  if (!messageId || messageId === 'unknown' || messageId === 'webhook_verification') {
    logger.debug({ messageId }, 'Skipping delivery attempt update for non-email payload');
    return;
  }

  try {
    await prisma.email.update({
      where: { messageId },
      data: {
        deliveryAttempts: { increment: 1 },
        lastAttemptAt: new Date(),
      },
    });
  } catch (error: any) {
    logger.error({ messageId, error: error.message }, 'Failed to update email delivery attempt');
  }
}

/**
 * Update email delivery status and related fields
 */
async function updateEmailDeliveryStatus(
  messageId: string,
  status: 'DELIVERED' | 'FAILED' | 'RETRYING',
  errorMessage: string | null
) {
  // Skip update if messageId is unknown/invalid or for webhook verification
  if (!messageId || messageId === 'unknown' || messageId === 'webhook_verification') {
    logger.debug({ messageId, status }, 'Skipping delivery status update for non-email payload');
    return;
  }

  try {
    const updateData: any = {
      deliveryStatus: status,
      lastAttemptAt: new Date(),
    };

    // Set deliveredAt timestamp only on successful delivery
    if (status === 'DELIVERED') {
      updateData.deliveredAt = new Date();
      updateData.errorMessage = null; // Clear any previous error
    } else if (errorMessage) {
      updateData.errorMessage = errorMessage;
    }

    await prisma.email.update({
      where: { messageId },
      data: updateData,
    });

    logger.debug({ messageId, status, errorMessage }, 'Email delivery status updated');
  } catch (error: any) {
    logger.error({
      messageId,
      status,
      error: error.message
    }, 'Failed to update email delivery status');
  }
}