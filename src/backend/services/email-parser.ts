import { simpleParser, ParsedMail, AddressObject } from 'mailparser';
import { EnhancedEmailWebhookPayload } from '../types/index.js';
import { env } from '../config/env.js';

export class EmailParser {
  static async parseToWebhookPayload(rawEmail: string | Buffer, domain: string): Promise<EnhancedEmailWebhookPayload> {
    const parsed: ParsedMail = await simpleParser(rawEmail);

    const primaryRecipient = this.extractPrimaryAddress(parsed.to) || { address: '', name: undefined };

    const payload: EnhancedEmailWebhookPayload = {
      message: {
        sender: {
          name: parsed.from?.value?.[0]?.name || null,
          email: parsed.from?.value?.[0]?.address || '',
        },
        recipient: {
          name: primaryRecipient.name || null,
          email: primaryRecipient.address,
        },
        subject: parsed.subject || null,
        content: {
          text: parsed.text || null,
          html: typeof parsed.html === 'string' ? parsed.html : null,
        },
        date: (parsed.date || new Date()).toISOString(),
        attachments: parsed.attachments?.map(att => {
          const sizeInKB = att.size / 1024;
          const maxInlineSize = 128; // 128KB for free users

          // Define text-based file types allowed for free users
          const allowedTextTypes = [
            'text/plain',
            'text/csv',
            'text/markdown',
            'text/calendar',
            'application/pdf',
            'application/json',
            'application/xml',
            'text/xml'
          ];

          const isTextBased = allowedTextTypes.some(type =>
            att.contentType.toLowerCase().startsWith(type)
          );

          const isSmallEnough = sizeInKB <= maxInlineSize;
          const shouldIncludeContent = isTextBased && isSmallEnough;

          return {
            filename: att.filename || null,
            contentType: att.contentType,
            size: att.size,
            // Only include content for small text-based attachments
            content: shouldIncludeContent ? att.content?.toString('base64') : undefined,
            // Add metadata for excluded attachments
            excluded: !shouldIncludeContent,
            excludeReason: !isTextBased ? 'non-text-file' : !isSmallEnough ? 'too-large' : undefined,
          };
        }) || [],
      },

      envelope: {
        messageId: parsed.messageId || `generated-${Date.now()}-${Math.random()}`,
        xMailer: this.getHeader(parsed.headers, 'x-mailer'),
        // deliveredTo: this.getHeader(parsed.headers, 'delivered-to'), // Contains Postix data
        xOriginalTo: this.getHeader(parsed.headers, 'x-original-to'),
        returnPath: this.getHeader(parsed.headers, 'return-path'),
        allRecipients: {
          to: this.extractAddresses(parsed.to),
          cc: this.extractAddresses(parsed.cc),
          bcc: this.extractAddresses(parsed.bcc),
        },
        headers: this.extractHeaders(parsed.headers),
        processed: {
          timestamp: new Date().toISOString(),
          domain: domain,
          alias: this.getHeader(parsed.headers, 'x-original-to') || primaryRecipient.address, // Extract alias from x-original-to header
          originalSize: Buffer.byteLength(typeof rawEmail === 'string' ? rawEmail : rawEmail.toString()),
        },
      },
    };

    // Add spam information if SpamAssassin headers are present
    const spamInfo = this.parseSpamHeaders(parsed.headers);
    if (spamInfo) {
      payload.spam = spamInfo;
    }

    return payload;
  }

  private static extractPrimaryAddress(field: AddressObject | AddressObject[] | undefined): { address: string; name?: string } | null {
    const obj = field as AddressObject;
    if (obj?.value?.length) {
      return {
        address: obj.value[0].address,
        name: obj.value[0].name,
      };
    }
    return null;
  }

  private static extractAddresses(field: AddressObject | AddressObject[] | undefined): string[] {
    const obj = field as AddressObject;
    return obj?.value?.map(addr => addr.address) || [];
  }

  private static extractHeaders(headers: Map<string, any>): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      if (Array.isArray(value)) {
        result[key.toLowerCase()] = value.map(v => EmailParser.stringifyHeaderValue(v)).join(', ');
      } else {
        result[key.toLowerCase()] = EmailParser.stringifyHeaderValue(value);
      }
    });
    return result;
  }

  private static getHeader(headers: Map<string, any>, key: string): string | null {
    const value = headers.get(key);
    if (value === undefined) return null;
    return Array.isArray(value)
      ? value.map(v => EmailParser.stringifyHeaderValue(v)).join(', ')
      : EmailParser.stringifyHeaderValue(value);
  }

  private static stringifyHeaderValue(value: any): string {
    if (typeof value === 'string') return value;
    if (value?.address) return value.address; // handle AddressObject
    if (typeof value === 'object') return JSON.stringify(value);
    if (value === undefined || value === null) return '';
    return String(value);
  }

  static extractDomainFromEmail(email: string): string {
    const match = email.match(/@(.+)$/);
    return match ? match[1].toLowerCase() : '';
  }

  /**
   * Parse SpamAssassin headers if present
   */
  private static parseSpamHeaders(headers: Map<string, any>) {
    const spamStatus = this.getHeader(headers, 'x-spam-status');
    const spamScore = this.getHeader(headers, 'x-spam-score');

    // Only return spam info if SpamAssassin headers are present
    if (!spamStatus && !spamScore) {
      return undefined;
    }

    return {
      isSpam: spamStatus?.toLowerCase().includes('yes') || false,
      score: parseFloat(spamScore || '0'),
      level: this.getHeader(headers, 'x-spam-level'),
      report: this.getHeader(headers, 'x-spam-report'),
      // version: this.getHeader(headers, 'x-spam-checker-version'),
      status: spamStatus
    };
  }
}