import { prisma } from '../../lib/prisma.js';
import { PlanConfigService } from './plan-config.service.js';
import { logger } from '../../utils/logger.js';

export interface GrandfatheringPolicy {
  planType: string;
  interval: string;
  oldPrice: number;
  newPrice: number;
  reason: string;
  gracePeriodDays?: number; // Days to re-subscribe and keep grandfathering
  maxDiscountPercent?: number; // Maximum discount allowed (e.g., 50%)
  expiresAt?: Date; // When grandfathering expires
}

export interface GrandfatheringStatus {
  isGrandfathered: boolean;
  grandfatheredPrice?: number;
  currentPrice: number;
  savings?: number;
  savingsPercent?: number;
  reason?: string;
  appliedAt?: Date;
  expiresAt?: Date;
}

export class GrandfatheringService {
  /**
   * Apply grandfathering to existing active subscriptions when prices increase
   */
  static async grandfatherExistingSubscriptions(policy: GrandfatheringPolicy): Promise<number> {
    try {
      logger.info({ policy }, 'Starting grandfathering process for existing subscriptions');

      // Find all active subscriptions for the plan that would be affected
      const activeSubscriptions = await prisma.subscription.findMany({
        where: {
          planType: policy.planType,
          interval: policy.interval,
          status: 'ACTIVE',
          isGrandfathered: false, // Don't re-grandfather already grandfathered subscriptions
          amount: policy.oldPrice // Only grandfather subscriptions at the old price
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true
            }
          }
        }
      });

      if (activeSubscriptions.length === 0) {
        logger.info({ policy }, 'No subscriptions found to grandfather');
        return 0;
      }

      // Apply grandfathering to each subscription
      const grandfatheredCount = await prisma.subscription.updateMany({
        where: {
          id: {
            in: activeSubscriptions.map(sub => sub.id)
          }
        },
        data: {
          isGrandfathered: true,
          grandfatheredPrice: policy.oldPrice,
          grandfatheredAt: new Date(),
          grandfatheringReason: policy.reason
        }
      });

      logger.info({ 
        count: grandfatheredCount.count,
        policy 
      }, 'Successfully applied grandfathering to subscriptions');

      // Log individual grandfathering for audit trail
      for (const subscription of activeSubscriptions) {
        logger.info({
          userId: subscription.userId,
          userEmail: subscription.user.email,
          subscriptionId: subscription.id,
          oldPrice: policy.oldPrice,
          newPrice: policy.newPrice,
          reason: policy.reason
        }, 'User grandfathered');
      }

      return grandfatheredCount.count;
    } catch (error: any) {
      logger.error({ error: error.message, policy }, 'Failed to apply grandfathering');
      throw new Error(`Failed to apply grandfathering: ${error.message}`);
    }
  }

  /**
   * Check if a user should keep their grandfathering status
   */
  static async validateGrandfathering(userId: string, newPlanType: string, newInterval: string): Promise<boolean> {
    try {
      const currentSubscription = await prisma.subscription.findFirst({
        where: {
          userId,
          status: 'ACTIVE'
        }
      });

      if (!currentSubscription || !currentSubscription.isGrandfathered) {
        return false; // No grandfathering to validate
      }

      // Business rules for keeping grandfathering:
      
      // 1. Same plan type and interval - keep grandfathering
      if (currentSubscription.planType === newPlanType && currentSubscription.interval === newInterval) {
        return true;
      }

      // 2. Upgrading to higher plan - lose grandfathering (they get new plan benefits)
      const planHierarchy = ['free', 'pro', 'enterprise'];
      const currentPlanIndex = planHierarchy.indexOf(currentSubscription.planType);
      const newPlanIndex = planHierarchy.indexOf(newPlanType);
      
      if (newPlanIndex > currentPlanIndex) {
        logger.info({
          userId,
          currentPlan: currentSubscription.planType,
          newPlan: newPlanType,
          reason: 'plan_upgrade'
        }, 'User loses grandfathering due to plan upgrade');
        return false;
      }

      // 3. Downgrading - lose grandfathering (they chose to downgrade)
      if (newPlanIndex < currentPlanIndex) {
        logger.info({
          userId,
          currentPlan: currentSubscription.planType,
          newPlan: newPlanType,
          reason: 'plan_downgrade'
        }, 'User loses grandfathering due to plan downgrade');
        return false;
      }

      // 4. Changing interval (monthly to yearly or vice versa) - keep grandfathering
      if (currentSubscription.planType === newPlanType && currentSubscription.interval !== newInterval) {
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error({ error: error.message, userId, newPlanType, newInterval }, 'Failed to validate grandfathering');
      return false; // Default to removing grandfathering on error
    }
  }

  /**
   * Remove grandfathering status from a subscription
   */
  static async removeGrandfathering(subscriptionId: string, reason: string): Promise<void> {
    try {
      await prisma.subscription.update({
        where: { id: subscriptionId },
        data: {
          isGrandfathered: false,
          grandfatheredPrice: null,
          grandfatheredAt: null,
          grandfatheringReason: null
        }
      });

      logger.info({
        subscriptionId,
        reason
      }, 'Grandfathering removed from subscription');
    } catch (error: any) {
      logger.error({ error: error.message, subscriptionId, reason }, 'Failed to remove grandfathering');
      throw new Error(`Failed to remove grandfathering: ${error.message}`);
    }
  }

  /**
   * Get grandfathering status for a user
   */
  static async getGrandfatheringStatus(userId: string): Promise<GrandfatheringStatus | null> {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: {
          userId,
          status: 'ACTIVE'
        }
      });

      if (!subscription) {
        return null;
      }

      const currentPrice = PlanConfigService.getPlanConfig(subscription.planType).price?.[subscription.interval as 'monthly' | 'yearly'] || Number(subscription.amount);

      if (!subscription.isGrandfathered) {
        return {
          isGrandfathered: false,
          currentPrice
        };
      }

      const grandfatheredPrice = Number(subscription.grandfatheredPrice!);
      const savings = currentPrice - grandfatheredPrice;
      const savingsPercent = Math.round((savings / currentPrice) * 100);

      return {
        isGrandfathered: true,
        grandfatheredPrice,
        currentPrice,
        savings,
        savingsPercent,
        reason: subscription.grandfatheringReason || undefined,
        appliedAt: subscription.grandfatheredAt || undefined
      };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to get grandfathering status');
      return null;
    }
  }

  /**
   * Apply early adopter grandfathering to specific users
   */
  static async applyEarlyAdopterGrandfathering(userIds: string[], reason: string = 'early_adopter'): Promise<number> {
    try {
      logger.info({ userIds, reason }, 'Applying early adopter grandfathering');

      // Get subscriptions to grandfather
      const subscriptions = await prisma.subscription.findMany({
        where: {
          userId: {
            in: userIds
          },
          status: 'ACTIVE',
          isGrandfathered: false
        }
      });

      // Update each subscription individually to set grandfatheredPrice to current amount
      const updatePromises = subscriptions.map(subscription =>
        prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            isGrandfathered: true,
            grandfatheredPrice: subscription.amount, // Lock in their current price
            grandfatheredAt: new Date(),
            grandfatheringReason: reason
          }
        })
      );

      await Promise.all(updatePromises);
      const result = { count: subscriptions.length };

      logger.info({ 
        count: result.count,
        userIds,
        reason 
      }, 'Successfully applied early adopter grandfathering');

      return result.count;
    } catch (error: any) {
      logger.error({ error: error.message, userIds, reason }, 'Failed to apply early adopter grandfathering');
      throw new Error(`Failed to apply early adopter grandfathering: ${error.message}`);
    }
  }

  /**
   * Get effective price for a subscription (grandfathered or current)
   */
  static async getEffectivePrice(subscriptionId: string): Promise<number> {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId }
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      if (subscription.isGrandfathered && subscription.grandfatheredPrice) {
        return Number(subscription.grandfatheredPrice);
      }

      return Number(subscription.amount);
    } catch (error: any) {
      logger.error({ error: error.message, subscriptionId }, 'Failed to get effective price');
      throw error;
    }
  }

  /**
   * Handle subscription cancellation - remove grandfathering after grace period
   */
  static async handleSubscriptionCancellation(subscriptionId: string, gracePeriodDays: number = 30): Promise<void> {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId }
      });

      if (!subscription || !subscription.isGrandfathered) {
        return; // Nothing to do
      }

      // Schedule grandfathering removal after grace period
      // In a real implementation, you'd use a job queue or cron job
      logger.info({
        subscriptionId,
        gracePeriodDays,
        removeGrandfatheringAt: new Date(Date.now() + gracePeriodDays * 24 * 60 * 60 * 1000)
      }, 'Grandfathering will be removed after grace period if not reactivated');

      // For now, we'll just log this. In production, you'd implement:
      // - Job queue to remove grandfathering after grace period
      // - Check on reactivation if within grace period
    } catch (error: any) {
      logger.error({ error: error.message, subscriptionId }, 'Failed to handle subscription cancellation');
    }
  }
}
