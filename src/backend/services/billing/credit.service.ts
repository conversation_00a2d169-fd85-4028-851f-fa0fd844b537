import { prisma } from '../../lib/prisma.js';
import { logger } from '../../utils/logger.js';
import { PlanConfigService } from './plan-config.service.js';

export interface CreditBalance {
  totalCredits: number;
  expiringCredits: number;
  nextExpirationDate: Date | null;
}

export interface CreditUsageResult {
  success: boolean;
  creditsUsed: number;
  remainingCredits: number;
  error?: string;
}

export interface CreditPurchaseResult {
  success: boolean;
  batchId?: string;
  creditsAdded: number;
  newBalance: number;
  expiresAt: Date;
  error?: string;
}

export class CreditService {
  /**
   * Get user's current credit balance with expiration info
   */
  static async getCreditBalance(userId: string): Promise<CreditBalance> {
    const batches = await prisma.creditBatch.findMany({
      where: {
        userId,
        isExpired: false,
        remainingAmount: { gt: 0 },
        expiresAt: { gt: new Date() }
      },
      orderBy: { expiresAt: 'asc' }
    });

    const totalCredits = batches.reduce((sum, batch) => sum + batch.remainingAmount, 0);
    const nextBatch = batches[0];
    
    // Credits expiring in next 30 days
    const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    const expiringCredits = batches
      .filter(batch => batch.expiresAt <= thirtyDaysFromNow)
      .reduce((sum, batch) => sum + batch.remainingAmount, 0);

    return {
      totalCredits,
      expiringCredits,
      nextExpirationDate: nextBatch?.expiresAt || null
    };
  }

  /**
   * Purchase credits for a user
   */
  static async purchaseCredits(
    userId: string, 
    creditAmount: number, 
    paymentId: string
  ): Promise<CreditPurchaseResult> {
    try {
      const expiresAt = new Date(Date.now() + 180 * 24 * 60 * 60 * 1000); // 180 days

      // Start transaction
      const result = await prisma.$transaction(async (tx) => {
        // Extend ALL existing non-expired credits to new expiration date
        await tx.creditBatch.updateMany({
          where: {
            userId,
            isExpired: false,
            remainingAmount: { gt: 0 },
            expiresAt: { gt: new Date() }
          },
          data: {
            expiresAt,
            extensionCount: { increment: 1 }
          }
        });

        // Create new credit batch
        const batch = await tx.creditBatch.create({
          data: {
            userId,
            amount: creditAmount,
            remainingAmount: creditAmount,
            expiresAt,
            paymentId
          }
        });

        // Log the purchase transaction
        await tx.creditTransaction.create({
          data: {
            userId,
            batchId: batch.id,
            type: 'purchase',
            amount: creditAmount,
            description: `Purchased ${creditAmount} email credits`
          }
        });

        return batch;
      });

      // Get new balance
      const balance = await this.getCreditBalance(userId);

      logger.info({
        userId,
        creditAmount,
        paymentId,
        batchId: result.id,
        newBalance: balance.totalCredits
      }, 'Credits purchased successfully');

      return {
        success: true,
        batchId: result.id,
        creditsAdded: creditAmount,
        newBalance: balance.totalCredits,
        expiresAt
      };

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        creditAmount,
        paymentId
      }, 'Failed to purchase credits');

      return {
        success: false,
        creditsAdded: 0,
        newBalance: 0,
        expiresAt: new Date(),
        error: error.message
      };
    }
  }

  /**
   * Use credits for email processing (FIFO - oldest first)
   */
  static async useCredits(userId: string, emailCount: number): Promise<CreditUsageResult> {
    try {
      // Get available credit batches (oldest first)
      const batches = await prisma.creditBatch.findMany({
        where: {
          userId,
          isExpired: false,
          remainingAmount: { gt: 0 },
          expiresAt: { gt: new Date() }
        },
        orderBy: { purchasedAt: 'asc' } // FIFO
      });

      const totalAvailable = batches.reduce((sum, batch) => sum + batch.remainingAmount, 0);
      
      if (totalAvailable < emailCount) {
        return {
          success: false,
          creditsUsed: 0,
          remainingCredits: totalAvailable,
          error: `Insufficient credits. Need ${emailCount}, have ${totalAvailable}`
        };
      }

      // Use credits from batches (FIFO)
      let remaining = emailCount;
      const usedBatches: { batchId: string; amount: number }[] = [];

      await prisma.$transaction(async (tx) => {
        for (const batch of batches) {
          if (remaining <= 0) break;

          const toUse = Math.min(remaining, batch.remainingAmount);
          
          // Update batch
          await tx.creditBatch.update({
            where: { id: batch.id },
            data: { remainingAmount: batch.remainingAmount - toUse }
          });

          // Log transaction
          await tx.creditTransaction.create({
            data: {
              userId,
              batchId: batch.id,
              type: 'usage',
              amount: -toUse,
              description: `Used ${toUse} credits for email processing`
            }
          });

          usedBatches.push({ batchId: batch.id, amount: toUse });
          remaining -= toUse;
        }
      });

      const newBalance = await this.getCreditBalance(userId);

      logger.info({
        userId,
        emailCount,
        creditsUsed: emailCount,
        remainingCredits: newBalance.totalCredits,
        usedBatches
      }, 'Credits used successfully');

      return {
        success: true,
        creditsUsed: emailCount,
        remainingCredits: newBalance.totalCredits
      };

    } catch (error: any) {
      logger.error({
        error: error.message,
        userId,
        emailCount
      }, 'Failed to use credits');

      return {
        success: false,
        creditsUsed: 0,
        remainingCredits: 0,
        error: error.message
      };
    }
  }

  /**
   * Get credit pricing for a user based on their plan
   */
  static async getCreditPricing(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { planType: true }
    });

    if (!user) {
      throw new Error('User not found');
    }

    return PlanConfigService.getCreditPricing(user.planType);
  }

  /**
   * Mark expired credits as expired (cleanup job)
   */
  static async markExpiredCredits(): Promise<number> {
    const result = await prisma.creditBatch.updateMany({
      where: {
        isExpired: false,
        expiresAt: { lte: new Date() }
      },
      data: { isExpired: true }
    });

    if (result.count > 0) {
      logger.info({ expiredBatches: result.count }, 'Marked expired credit batches');
    }

    return result.count;
  }
}
