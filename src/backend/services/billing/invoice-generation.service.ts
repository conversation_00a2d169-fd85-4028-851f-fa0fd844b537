import puppeteer from 'puppeteer';
import { promises as fs } from 'fs';
import path from 'path';
import { prisma } from '../../lib/prisma.js';
import { env } from '../../config/env.js';

export interface InvoiceData {
  invoiceNumber: string;
  paymentId: string;
  userId: string;
  amount: number;
  currency: string;
  description: string;
  billingPeriod?: string;
  paidAt: Date;
  userEmail: string;
  userName?: string;
}

export class InvoiceGenerationService {
  private static invoiceCounter = 0;

  /**
   * Generate a unique invoice number
   */
  private static async generateInvoiceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    
    // Get the latest invoice number for this year
    const latestInvoice = await prisma.invoice.findFirst({
      where: {
        invoiceNumber: {
          startsWith: `INV-${year}-`
        }
      },
      orderBy: {
        invoiceNumber: 'desc'
      }
    });

    let nextNumber = 1;
    if (latestInvoice) {
      const match = latestInvoice.invoiceNumber.match(/INV-\d{4}-(\d+)/);
      if (match) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `INV-${year}-${nextNumber.toString().padStart(3, '0')}`;
  }

  /**
   * Generate HTML content for the invoice
   */
  private static generateInvoiceHTML(data: InvoiceData): string {
    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const formatCurrency = (amount: number, currency: string) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency.toUpperCase()
      }).format(amount);
    };

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${data.invoiceNumber}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
        }
        .company-info {
            flex: 1;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .company-details {
            color: #6b7280;
            font-size: 14px;
        }
        .invoice-info {
            text-align: right;
            flex: 1;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .invoice-date {
            color: #6b7280;
            font-size: 14px;
        }
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
        }
        .billing-info {
            flex: 1;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .customer-info {
            color: #6b7280;
            font-size: 14px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 40px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .items-table th {
            background: #f9fafb;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
        }
        .items-table tr:last-child td {
            border-bottom: none;
        }
        .amount-cell {
            text-align: right;
            font-weight: 600;
        }
        .total-section {
            margin-top: 30px;
            text-align: right;
        }
        .total-row {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }
        .total-label {
            width: 150px;
            text-align: right;
            padding-right: 20px;
            font-weight: 600;
        }
        .total-amount {
            width: 120px;
            text-align: right;
            font-weight: 600;
        }
        .grand-total {
            font-size: 18px;
            color: #1f2937;
            border-top: 2px solid #e5e7eb;
            padding-top: 10px;
            margin-top: 10px;
        }
        .footer {
            margin-top: 60px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 12px;
        }
        .payment-info {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .payment-status {
            color: #166534;
            font-weight: 600;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-info">
            <div class="company-name">EmailConnect</div>
            <div class="company-details">
                EU Email Webhook Service<br>
                VAT: NL123456789B01<br>
                <EMAIL>
            </div>
        </div>
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number">${data.invoiceNumber}</div>
            <div class="invoice-date">Date: ${formatDate(data.paidAt)}</div>
        </div>
    </div>

    <div class="billing-section">
        <div class="billing-info">
            <div class="section-title">Bill To:</div>
            <div class="customer-info">
                ${data.userName || 'Customer'}<br>
                ${data.userEmail}
            </div>
        </div>
        <div class="billing-info">
            <div class="section-title">Payment Details:</div>
            <div class="customer-info">
                Payment ID: ${data.paymentId}<br>
                ${data.billingPeriod ? `Billing Period: ${data.billingPeriod}<br>` : ''}
                Payment Date: ${formatDate(data.paidAt)}
            </div>
        </div>
    </div>

    <div class="payment-info">
        <div class="payment-status">✓ Payment Received</div>
        <div>This invoice has been paid in full via online payment.</div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>${data.description}</td>
                <td>1</td>
                <td class="amount-cell">${formatCurrency(data.amount, data.currency)}</td>
                <td class="amount-cell">${formatCurrency(data.amount, data.currency)}</td>
            </tr>
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-row">
            <div class="total-label">Subtotal:</div>
            <div class="total-amount">${formatCurrency(data.amount, data.currency)}</div>
        </div>
        <div class="total-row">
            <div class="total-label">VAT (0%):</div>
            <div class="total-amount">${formatCurrency(0, data.currency)}</div>
        </div>
        <div class="total-row grand-total">
            <div class="total-label">Total:</div>
            <div class="total-amount">${formatCurrency(data.amount, data.currency)}</div>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated invoice. No signature required.</p>
        <p>For questions about this invoice, <NAME_EMAIL></p>
    </div>
</body>
</html>`;
  }

  /**
   * Generate PDF invoice and save to file system
   */
  static async generateInvoice(data: InvoiceData): Promise<string> {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      const html = this.generateInvoiceHTML(data);
      
      await page.setContent(html, { waitUntil: 'networkidle0' });
      
      // Ensure invoices directory exists
      const invoicesDir = path.join(process.cwd(), 'storage', 'invoices');
      await fs.mkdir(invoicesDir, { recursive: true });
      
      // Generate PDF file path
      const fileName = `${data.invoiceNumber}.pdf`;
      const filePath = path.join(invoicesDir, fileName);
      
      // Generate PDF
      await page.pdf({
        path: filePath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm'
        }
      });

      return filePath;
    } finally {
      await browser.close();
    }
  }

  /**
   * Create invoice record in database and generate PDF
   */
  static async createInvoice(paymentId: string): Promise<string> {
    // Get payment details
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        user: true,
        subscription: true
      }
    });

    if (!payment || !payment.paidAt) {
      throw new Error('Payment not found or not paid');
    }

    // Generate invoice number
    const invoiceNumber = await this.generateInvoiceNumber();

    // Determine billing period for subscriptions
    let billingPeriod: string | undefined;
    if (payment.subscription) {
      const paidDate = payment.paidAt;
      billingPeriod = paidDate.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long' 
      });
    }

    // Prepare invoice data
    const invoiceData: InvoiceData = {
      invoiceNumber,
      paymentId: payment.id,
      userId: payment.userId,
      amount: Number(payment.amount),
      currency: payment.currency,
      description: payment.description || 'Service Payment',
      billingPeriod,
      paidAt: payment.paidAt,
      userEmail: payment.user.email,
      userName: payment.user.name
    };

    // Generate PDF
    const pdfPath = await this.generateInvoice(invoiceData);

    // Create invoice record
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        pdfPath,
        paymentId: payment.id,
        userId: payment.userId,
        amount: payment.amount,
        currency: payment.currency,
        description: payment.description || 'Service Payment',
        billingPeriod
      }
    });

    return invoice.id;
  }
}
