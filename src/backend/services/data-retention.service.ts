import { prisma } from '../lib/prisma.js';
import { logger } from '../utils/logger.js';

export class DataRetentionService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 30 * 60 * 1000; // Check every 30 minutes

  /**
   * Start the data retention cleanup worker
   */
  start(): void {
    if (this.intervalId) {
      logger.warn('Data retention worker already running');
      return;
    }

    logger.info('Starting data retention cleanup worker');
    
    // Run immediately, then on interval
    this.runCleanup();
    this.intervalId = setInterval(() => {
      this.runCleanup();
    }, this.CHECK_INTERVAL_MS);
  }

  /**
   * Stop the data retention cleanup worker
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('Data retention worker stopped');
    }
  }

  /**
   * Run the data retention cleanup process
   */
  private async runCleanup(): Promise<void> {
    try {
      logger.debug('Running data retention cleanup');

      const now = new Date();
      
      // Find expired emails based on user settings and plan defaults
      const expiredEmails = await this.findExpiredEmails(now);
      
      if (expiredEmails.length === 0) {
        logger.debug('No expired emails found for cleanup');
        return;
      }

      logger.info({ emailCount: expiredEmails.length }, 'Found expired emails for cleanup');

      // Delete expired emails in batches to avoid overwhelming the database
      const batchSize = 100;
      let deletedCount = 0;

      for (let i = 0; i < expiredEmails.length; i += batchSize) {
        const batch = expiredEmails.slice(i, i + batchSize);
        const emailIds = batch.map(email => email.id);

        const deleteResult = await prisma.email.deleteMany({
          where: {
            id: {
              in: emailIds
            }
          }
        });

        deletedCount += deleteResult.count;
        
        logger.debug({ 
          batchSize: batch.length, 
          deletedInBatch: deleteResult.count,
          totalDeleted: deletedCount 
        }, 'Processed email deletion batch');
      }

      logger.info({ 
        totalExpired: expiredEmails.length,
        totalDeleted: deletedCount,
        timestamp: now.toISOString()
      }, 'Data retention cleanup completed');

    } catch (error: any) {
      logger.error({ error: error.message, stack: error.stack }, 'Failed to run data retention cleanup');
    }
  }

  /**
   * Find emails that have expired based on user settings and plan defaults
   */
  private async findExpiredEmails(now: Date): Promise<Array<{ id: string; userId: string | null; createdAt: Date }>> {
    // Get all users with their settings and plan types
    const usersWithSettings = await prisma.user.findMany({
      select: {
        id: true,
        planType: true,
        settings: {
          select: {
            dataRetentionHours: true
          }
        }
      }
    });

    // Build a map of userId to effective retention hours
    const userRetentionMap = new Map<string, number>();

    for (const user of usersWithSettings) {
      let retentionHours: number;

      // Use custom setting if available, otherwise use plan default
      if (user.settings?.dataRetentionHours !== null && user.settings?.dataRetentionHours !== undefined) {
        retentionHours = user.settings.dataRetentionHours;
      } else {
        // Plan-based defaults
        switch (user.planType) {
          case 'free':
            retentionHours = 2;
            break;
          case 'pro':
          case 'enterprise':
            retentionHours = 24;
            break;
          default:
            retentionHours = 2; // Default to free plan retention
        }
      }

      userRetentionMap.set(user.id, retentionHours);
    }

    // Find expired emails for each user (including emails with userId)
    const expiredEmails: Array<{ id: string; userId: string | null; createdAt: Date }> = [];

    for (const [userId, retentionHours] of userRetentionMap.entries()) {
      const cutoffTime = new Date(now.getTime() - (retentionHours * 60 * 60 * 1000));

      const userExpiredEmails = await prisma.email.findMany({
        where: {
          userId: userId,
          createdAt: {
            lt: cutoffTime
          }
        },
        select: {
          id: true,
          userId: true,
          createdAt: true
        }
      });

      expiredEmails.push(...userExpiredEmails);
    }

    // Also handle legacy emails without userId (use domain-based lookup)
    const legacyExpiredEmails = await prisma.email.findMany({
      where: {
        userId: null,
        createdAt: {
          lt: new Date(now.getTime() - (2 * 60 * 60 * 1000)) // Default to 2 hours for legacy emails
        }
      },
      select: {
        id: true,
        userId: true,
        createdAt: true
      }
    });

    expiredEmails.push(...legacyExpiredEmails);

    return expiredEmails;
  }

  /**
   * Manually clean up expired emails for a specific user
   */
  async cleanupUserEmails(userId: string): Promise<{ success: boolean; deletedCount: number; error?: string }> {
    try {
      const now = new Date();
      
      // Get user's effective retention hours
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          planType: true,
          settings: {
            select: {
              dataRetentionHours: true
            }
          }
        }
      });

      if (!user) {
        return { success: false, deletedCount: 0, error: 'User not found' };
      }

      let retentionHours: number;
      if (user.settings?.dataRetentionHours !== null && user.settings?.dataRetentionHours !== undefined) {
        retentionHours = user.settings.dataRetentionHours;
      } else {
        switch (user.planType) {
          case 'free':
            retentionHours = 2;
            break;
          case 'pro':
          case 'enterprise':
            retentionHours = 24;
            break;
          default:
            retentionHours = 2;
        }
      }

      const cutoffTime = new Date(now.getTime() - (retentionHours * 60 * 60 * 1000));
      
      const deleteResult = await prisma.email.deleteMany({
        where: {
          userId: userId,
          createdAt: {
            lt: cutoffTime
          }
        }
      });

      logger.info({
        userId,
        retentionHours,
        deletedCount: deleteResult.count,
        cutoffTime: cutoffTime.toISOString()
      }, 'Manual user email cleanup completed');

      return { success: true, deletedCount: deleteResult.count };
    } catch (error: any) {
      logger.error({ error: error.message, userId }, 'Failed to cleanup user emails');
      return { success: false, deletedCount: 0, error: error.message };
    }
  }

  /**
   * Get data retention statistics
   */
  async getRetentionStats(): Promise<{
    totalEmails: number;
    expiredEmails: number;
    userRetentionSettings: Array<{
      userId: string;
      planType: string;
      customRetentionHours: number | null;
      effectiveRetentionHours: number;
      emailCount: number;
    }>;
  }> {
    const now = new Date();
    
    const [totalEmails, usersWithSettings] = await Promise.all([
      prisma.email.count(),
      prisma.user.findMany({
        select: {
          id: true,
          planType: true,
          settings: {
            select: {
              dataRetentionHours: true
            }
          },
          _count: {
            select: {
              emails: true
            }
          }
        }
      })
    ]);

    let expiredEmailsCount = 0;
    const userRetentionSettings = [];

    for (const user of usersWithSettings) {
      let effectiveRetentionHours: number;
      const customRetentionHours = user.settings?.dataRetentionHours ?? null;
      
      if (customRetentionHours !== null) {
        effectiveRetentionHours = customRetentionHours;
      } else {
        switch (user.planType) {
          case 'free':
            effectiveRetentionHours = 2;
            break;
          case 'pro':
          case 'enterprise':
            effectiveRetentionHours = 24;
            break;
          default:
            effectiveRetentionHours = 2;
        }
      }

      const cutoffTime = new Date(now.getTime() - (effectiveRetentionHours * 60 * 60 * 1000));
      
      const userExpiredCount = await prisma.email.count({
        where: {
          userId: user.id,
          createdAt: {
            lt: cutoffTime
          }
        }
      });

      expiredEmailsCount += userExpiredCount;

      userRetentionSettings.push({
        userId: user.id,
        planType: user.planType,
        customRetentionHours,
        effectiveRetentionHours,
        emailCount: user._count.emails
      });
    }

    return {
      totalEmails,
      expiredEmails: expiredEmailsCount,
      userRetentionSettings
    };
  }
}
