import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'
import { prisma } from '../lib/prisma.js'
import { adminAuthMiddleware } from '../lib/auth.js'
import { getViteAssetPath, getViteAssetCss } from '../lib/vite-manifest.js'

export default async function authRoutes(fastify: FastifyInstance) {
  // =============================================================================
  // ADMIN ROUTES (JSON API - No templates)
  // =============================================================================
  
  // Admin login endpoint (JSON only)
  fastify.get('/admin/login', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login Endpoint',
      description: 'Admin login endpoint. Returns JSON response. Admin UI not yet implemented.',
      response: {
        '200': {
          description: 'Admin login endpoint info.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  loginEndpoint: { type: 'string' }
                }
              },
            },
          },
        },
        '302': {
          description: 'Redirects to admin dashboard if already authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Check if already logged in, redirect to dashboard if so
    if (request.cookies.admin_token) {
        try {
            return reply.status(302).redirect('/admin/dashboard');
        } catch (e) {
            reply.clearCookie('admin_token', { path: '/' });
        }
    }
    return reply.send({
      message: 'Admin login endpoint. Use POST /admin/login to authenticate.',
      loginEndpoint: 'POST /admin/login'
    });
  });

  // Admin dashboard API (JSON only)
  fastify.get('/admin/dashboard', { 
    preHandler: [adminAuthMiddleware],
    schema: {
      tags: ['Admin Management'],
      summary: 'Admin Dashboard API',
      description: 'Protected admin dashboard API endpoint. Returns JSON data.',
      security: [{ adminAuth: [] }],
      response: {
        '200': {
          description: 'Admin dashboard data.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  stats: {
                    type: 'object',
                    properties: {
                      totalDomains: { type: 'integer' },
                      verifiedDomains: { type: 'integer' },
                      pendingDomains: { type: 'integer' },
                      todayEmails: { type: 'integer' }
                    }
                  },
                  user: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '302': {
          description: 'Redirects to login if not authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    return reply.send({
      title: 'Admin Dashboard',
      stats: {
        totalDomains: 0,
        verifiedDomains: 0,
        pendingDomains: 0,
        todayEmails: 0
      },
      user: { name: 'Admin' },
      message: 'Admin dashboard API endpoint. Admin UI not yet implemented.'
    });
  });

  // Admin login POST endpoint
  fastify.post('/admin/login', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login',
      description: 'Authenticate admin user and return JWT token.',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 1 }
        }
      },
      response: {
        '200': {
          description: 'Login successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      role: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '400': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, password } = request.body as { email: string; password: string };

    try {
      // Use AdminAuthService for consistent authentication
      const { AdminAuthService } = await import('../services/auth/admin-auth.service.js');
      const adminAuthService = new AdminAuthService();

      // Convert email/password to username/password for admin service
      const credentials = { username: email, password };
      const authResult = await adminAuthService.authenticateAdmin(credentials);

      if (!authResult.success) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid admin credentials'
        });
      }

      // Generate JWT token using AdminAuthService
      const tokenResult = adminAuthService.generateToken();
      if (!tokenResult.success) {
        return reply.status(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Token generation failed'
        });
      }

      // Set HTTP-only cookie using consistent configuration
      const cookieConfig = adminAuthService.getCookieConfig();
      reply.setCookie('admin_token', tokenResult.token!, cookieConfig);

      return reply.send({
        message: 'Admin login successful',
        user: {
          id: 'admin',
          email: process.env.ADMIN_EMAIL || '<EMAIL>',
          role: 'admin'
        }
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Admin login error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Login failed'
      });
    }
  });

  // Admin logout
  fastify.post('/admin/logout', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Logout',
      description: 'Logout admin user and clear session.',
      response: {
        '200': {
          description: 'Logout successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' }
                }
              },
            },
          },
        },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    reply.clearCookie('admin_token', { path: '/' });
    return reply.send({ message: 'Admin logout successful' });
  });

  // =============================================================================
  // USER AUTHENTICATION API ENDPOINTS
  // =============================================================================

  // User registration POST endpoint
  fastify.post('/register', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Registration',
      description: 'Register a new user account.',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 6 },
          name: { type: 'string' }
        }
      },
      response: {
        '201': {
          description: 'Registration successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  message: { type: 'string' },
                  token: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '400': { $ref: 'ErrorResponse#' },
        '409': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, password, name } = request.body as { email: string; password: string; name?: string };



    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return reply.status(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: 'User with this email already exists'
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name: name || email.split('@')[0]
        },
        select: {
          id: true,
          email: true,
          name: true
        }
      });

      // Generate JWT token using UserAuthService
      const { UserAuthService } = await import('../services/auth/user-auth.service.js');
      const userAuthService = new UserAuthService();
      const tokenResult = userAuthService.generateToken({ userId: user.id, email: user.email });

      if (!tokenResult.success) {
        return reply.status(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Token generation failed'
        });
      }

      // Set HTTP-only cookie using consistent configuration
      const cookieConfig = userAuthService.getCookieConfig();
      reply.setCookie('user_token', tokenResult.token!, cookieConfig);

      // Secure response - token only in httpOnly cookie
      const response: any = {
        success: true,
        message: 'Registration successful',
        user
      };

      // Always include token for API usage (tests and development)
      response.token = tokenResult.token;


      return reply.status(201).send(response);
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Registration error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Registration failed'
      });
    }
  });

  // User login POST endpoint
  fastify.post('/login', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Login',
      description: 'Authenticate user and return JWT token.',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 1 }
        }
      },
      response: {
        '200': {
          description: 'Login successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean' },
                  message: { type: 'string' },
                  token: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '400': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, password } = request.body as { email: string; password: string };

    try {
      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          name: true,
          password: true
        }
      });

      if (!user) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid email or password'
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid email or password'
        });
      }

      // Generate JWT token using UserAuthService
      const { UserAuthService } = await import('../services/auth/user-auth.service.js');
      const userAuthService = new UserAuthService();
      const tokenResult = userAuthService.generateToken({ userId: user.id, email: user.email });

      if (!tokenResult.success) {
        return reply.status(500).send({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Token generation failed'
        });
      }

      // Set HTTP-only cookie using consistent configuration
      const cookieConfig = userAuthService.getCookieConfig();
      reply.setCookie('user_token', tokenResult.token!, cookieConfig);

      // Secure response - token only in httpOnly cookie
      const response: any = {
        success: true,
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        }
      };

      return reply.send(response);
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Login error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Login failed'
      });
    }
  });

  // User logout
  fastify.post('/logout', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Logout',
      description: 'Logout user and clear session.',
      response: {
        '200': {
          description: 'Logout successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' }
                }
              },
            },
          },
        },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    reply.clearCookie('user_token', { path: '/' });
    return reply.send({ message: 'Logout successful' });
  });

  // Authentication check endpoint for frontend router
  // This endpoint handles invalid users gracefully by clearing cookies
  fastify.get('/api/auth/check', {
    schema: {
      tags: ['User Authentication'],
      summary: 'Check authentication status',
      description: 'Verifies if the user is authenticated via cookie',
      response: {
        200: {
          type: 'object',
          properties: {
            authenticated: { type: 'boolean' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' }
              }
            }
          }
        },
        401: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Custom auth check that handles invalid users gracefully
      const cookieToken = request.cookies.user_token;

      if (!cookieToken) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Not authenticated'
        });
      }

      // Verify JWT token
      const { UserAuthService } = await import('../services/auth/user-auth.service.js');
      const userAuthService = new UserAuthService();
      const verifyResult = userAuthService.verifyToken(cookieToken);

      if (!verifyResult.success) {
        reply.clearCookie('user_token', { path: '/' });
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid token'
        });
      }

      // Check if user still exists in database
      const user = await prisma.user.findUnique({
        where: { id: verifyResult.payload!.userId },
        select: { id: true, email: true, planType: true }
      });

      if (!user) {
        // User no longer exists - clear cookie and return unauthenticated
        reply.clearCookie('user_token', { path: '/' });
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'User not found'
        });
      }

      return reply.send({
        authenticated: true,
        user: {
          id: user.id,
          email: user.email
        }
      });
    } catch (error) {
      // Clear cookie on any error
      reply.clearCookie('user_token', { path: '/' });
      return reply.status(401).send({
        statusCode: 401,
        error: 'Unauthorized',
        message: 'Authentication error'
      });
    }
  });

  // =============================================================================
  // SPA ROUTES - Removed - Vue Router handles all client-side routing
  // The main catch-all route in index.ts serves the SPA for all non-API routes
  // =============================================================================

  // =============================================================================
  // LEGACY ROUTE REDIRECTS - Removed
  // Vue Router now handles all client-side routing without backend redirects
  // =============================================================================

  fastify.log.info('Auth routes registered');
}
