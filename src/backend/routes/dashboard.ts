import { FastifyPluginAsync } from 'fastify';
import { prisma } from '../lib/prisma.js';
import { requireAuth } from '../lib/auth.js';
import { UsageCalculationService } from '../services/billing/usage-calculation.service.js';
import { PlanConfigService } from '../services/billing/plan-config.service.js';

export const dashboardRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user dashboard metrics
  fastify.get('/dashboard/metrics', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['User Dashboard'],
      summary: 'Get user dashboard metrics',
      description: 'Retrieves metrics for the authenticated user\'s dashboard.',
      response: {
        200: {
          type: 'object',
          properties: {
            domains: { type: 'integer', description: 'Total number of domains' },
            verified_domains: { type: 'integer', description: 'Number of verified domains' },
            emails_processed_24h: { type: 'integer', description: 'Emails processed in last 24 hours' },
            success_rate: { type: 'number', description: 'Email delivery success rate percentage' },
            monthly_usage: { type: 'integer', description: 'Current month email usage' },
            monthly_limit: { type: 'integer', description: 'Monthly email limit' },
            total_available_emails: { type: 'integer', description: 'Total available emails (monthly + credits)' },
            purchased_credits: { type: 'integer', description: 'Available purchased credits' },
            webhooks: { type: 'integer', description: 'Total number of webhooks' },
            aliases: { type: 'integer', description: 'Total number of aliases' },
            user: {
              type: 'object',
              nullable: true,
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string', nullable: true },
                planType: { type: 'string' },
                verified: { type: 'boolean' }
              }
            }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      const [
        domainsCount, 
        verifiedDomainsCount, 
        emailsLast24h, 
        successfulEmailsLast24h,
        userInfo,
        fullUserInfo,
        webhooksCount,
        aliasesCount
      ] = await Promise.all([
        prisma.domain.count({ where: { userId: user.id } }),
        prisma.domain.count({ where: { userId: user.id, verified: true } }),
        prisma.email.count({ 
          where: { 
            domain: { userId: user.id },
            createdAt: { gte: yesterday }
          }
        }),
        prisma.email.count({ 
          where: { 
            domain: { userId: user.id },
            createdAt: { gte: yesterday },
            deliveryStatus: 'DELIVERED'
          }
        }),
        prisma.user.findUnique({
          where: { id: user.id },
          select: { currentMonthEmails: true }
        }),
        prisma.user.findUnique({
          where: { id: user.id },
          select: { id: true, email: true, name: true, planType: true, verified: true }
        }),
        prisma.webhook.count({ where: { userId: user.id } }),
        prisma.alias.count({
          where: {
            domain: { userId: user.id }
          }
        })
      ]);

      const successRate = emailsLast24h > 0 ? Math.round((successfulEmailsLast24h / emailsLast24h) * 100) : 100;

      // Get enhanced usage information including credits
      const simpleUsage = await UsageCalculationService.getSimpleUsage(user.id);

      // Calculate monthly limit based on plan type instead of stored value
      const planType = fullUserInfo?.planType || 'free';
      const planConfig = PlanConfigService.getPlanConfig(planType);
      const monthlyLimit = planConfig.monthlyEmailLimit;

      return reply.send({
        domains: domainsCount,
        verified_domains: verifiedDomainsCount,
        emails_processed_24h: emailsLast24h,
        success_rate: successRate,
        monthly_usage: userInfo?.currentMonthEmails || 0,
        monthly_limit: monthlyLimit,
        total_available_emails: simpleUsage.emailsAvailable,
        purchased_credits: simpleUsage.emailsAvailable - monthlyLimit + (userInfo?.currentMonthEmails || 0),
        webhooks: webhooksCount,
        aliases: aliasesCount,
        user: fullUserInfo ? {
          id: fullUserInfo.id,
          email: fullUserInfo.email,
          name: fullUserInfo.name,
          planType: fullUserInfo.planType,
          verified: fullUserInfo.verified
        } : null
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Failed to get dashboard metrics');
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to retrieve dashboard metrics' });
    }
  });

  // Get recent activity for dashboard
  fastify.get('/dashboard/activity', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['User Dashboard'],
      summary: 'Get recent user activity',
      description: 'Retrieves recent activity for the authenticated user.',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', default: 10, minimum: 1, maximum: 50 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            activities: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string', enum: ['email_received', 'domain_verified', 'domain_added', 'webhook_created'] },
                  title: { type: 'string' },
                  description: { type: 'string' },
                  timestamp: { type: 'string', format: 'date-time' },
                  metadata: { type: 'object', additionalProperties: true }
                }
              }
            }
          }
        },
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { limit } = request.query as { limit?: number };
      const maxLimit = Math.min(limit || 10, 50);

      // Get recent emails
      const recentEmails = await prisma.email.findMany({
        where: {
          domain: { userId: user.id }
        },
        select: {
          id: true,
          messageId: true,
          subject: true,
          fromAddress: true,
          toAddresses: true,
          deliveryStatus: true,
          createdAt: true,
          domain: {
            select: { domain: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: Math.ceil(maxLimit / 2)
      });

      // Get recent domain activities from audit log
      const recentAudits = await prisma.auditLog.findMany({
        where: {
          action: { in: ['domain_added', 'domain_verified', 'webhook_created'] },
          // Note: We'd need to add userId to auditLog to filter properly
          // For now, we'll get all and filter by resourceId
        },
        orderBy: { createdAt: 'desc' },
        take: Math.ceil(maxLimit / 2)
      });

      // Combine and format activities
      const activities = [
        ...recentEmails.map(email => ({
          id: email.id,
          type: 'email_received' as const,
          title: 'Email Received',
          description: `From ${email.fromAddress} to ${email.toAddresses[0]} on ${email.domain.domain}`,
          timestamp: email.createdAt.toISOString(),
          metadata: {
            messageId: email.messageId,
            subject: email.subject,
            status: email.deliveryStatus
          }
        })),
        ...recentAudits.map(audit => ({
          id: audit.id,
          type: audit.action as 'domain_added' | 'domain_verified' | 'webhook_created',
          title: audit.action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: JSON.stringify(audit.metadata),
          timestamp: audit.createdAt.toISOString(),
          metadata: audit.metadata
        }))
      ];

      // Sort by timestamp and limit
      activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      return reply.send({
        activities: activities.slice(0, maxLimit)
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Failed to get dashboard activity');
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to retrieve dashboard activity' });
    }
  });
};
