import { FastifyPluginAsync } from 'fastify';
import { requireAuth, requireAdmin } from '../lib/auth.js';
import { prisma } from '../lib/prisma.js';
import { promises as fs } from 'fs';
import path from 'path';

export const invoicesRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user's invoices
  fastify.get('/invoices', {
    preHandler: requireAuth,
    schema: {
      tags: ['Invoices'],
      summary: 'Get user invoices',
      description: 'Retrieve list of invoices for the authenticated user',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            invoices: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  invoiceNumber: { type: 'string' },
                  amount: { type: 'number' },
                  currency: { type: 'string' },
                  description: { type: 'string' },
                  billingPeriod: { type: 'string', nullable: true },
                  generatedAt: { type: 'string', format: 'date-time' },
                  paymentId: { type: 'string' },
                  paidAt: { type: 'string', format: 'date-time' }
                }
              }
            }
          }
        },
        401: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;

      const invoices = await prisma.invoice.findMany({
        where: {
          userId: user.id
        },
        include: {
          payment: {
            select: {
              paidAt: true
            }
          }
        },
        orderBy: {
          generatedAt: 'desc'
        }
      });

      const formattedInvoices = invoices.map(invoice => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        amount: Number(invoice.amount),
        currency: invoice.currency,
        description: invoice.description,
        billingPeriod: invoice.billingPeriod,
        generatedAt: invoice.generatedAt.toISOString(),
        paymentId: invoice.paymentId,
        paidAt: invoice.payment.paidAt?.toISOString()
      }));

      return reply.send({
        success: true,
        invoices: formattedInvoices
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get invoices');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve invoices'
      });
    }
  });

  // Download invoice PDF
  fastify.get('/invoices/:invoiceId/download', {
    preHandler: requireAuth,
    schema: {
      tags: ['Invoices'],
      summary: 'Download invoice PDF',
      description: 'Download PDF file for a specific invoice',
      params: {
        type: 'object',
        properties: {
          invoiceId: { type: 'string' }
        },
        required: ['invoiceId']
      },
      response: {
        200: {
          type: 'string',
          format: 'binary',
          description: 'PDF file'
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { invoiceId } = request.params as { invoiceId: string };

      // Find invoice and verify ownership
      const invoice = await prisma.invoice.findFirst({
        where: {
          id: invoiceId,
          userId: user.id
        }
      });

      if (!invoice) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice not found'
        });
      }

      if (!invoice.pdfPath) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice PDF not available'
        });
      }

      // Check if file exists
      try {
        await fs.access(invoice.pdfPath);
      } catch {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Invoice PDF file not found'
        });
      }

      // Set headers for PDF download
      const fileName = `${invoice.invoiceNumber}.pdf`;
      reply.header('Content-Type', 'application/pdf');
      reply.header('Content-Disposition', `attachment; filename="${fileName}"`);

      // Stream the file
      const fileStream = await fs.readFile(invoice.pdfPath);
      return reply.send(fileStream);

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to download invoice');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to download invoice'
      });
    }
  });

  // Generate invoice for a payment (admin/testing endpoint)
  fastify.post('/invoices/generate/:paymentId', {
    preHandler: requireAdmin,
    schema: {
      tags: ['Invoices'],
      summary: 'Generate invoice for payment',
      description: 'Generate an invoice for a specific payment (testing/admin use)',
      params: {
        type: 'object',
        properties: {
          paymentId: { type: 'string' }
        },
        required: ['paymentId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            invoiceId: { type: 'string' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, async (request, reply) => {
    try {
      const user = (request as any).user;
      const { paymentId } = request.params as { paymentId: string };

      // Verify payment belongs to user
      const payment = await prisma.payment.findFirst({
        where: {
          id: paymentId,
          userId: user.id,
          status: 'PAID'
        }
      });

      if (!payment) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Payment not found or not paid'
        });
      }

      // Check if invoice already exists
      const existingInvoice = await prisma.invoice.findFirst({
        where: {
          paymentId: paymentId
        }
      });

      if (existingInvoice) {
        return reply.send({
          success: true,
          invoiceId: existingInvoice.id,
          message: 'Invoice already exists'
        });
      }

      // Generate invoice
      const { InvoiceGenerationService } = await import('../services/billing/invoice-generation.service.js');
      const invoiceId = await InvoiceGenerationService.createInvoice(paymentId);

      return reply.send({
        success: true,
        invoiceId,
        message: 'Invoice generated successfully'
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to generate invoice');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to generate invoice'
      });
    }
  });
};
