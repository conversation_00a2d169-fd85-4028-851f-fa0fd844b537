import { FastifyPluginAsync } from 'fastify';

export const templateRoutes: FastifyPluginAsync = async (fastify) => {
  // Health check for templates (keeping for API completeness)
  fastify.get('/templates/health', {
    schema: {
      tags: ['Templates'],
      summary: 'Template system health check',
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    return reply.send({
      status: 'ok',
      message: 'All templates migrated to Vue components'
    });
  });
};
