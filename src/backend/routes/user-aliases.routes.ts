import { FastifyPluginAsync } from 'fastify';
import { AliasesController } from '../controllers/user/aliases.controller.js';
import { aliasSchemas } from '../schemas/user/alias.schemas.js';
import { aliasAuth, aliasOwnerAuth, domainAuth } from '../lib/auth.js';

const aliasesController = new AliasesController();

export const userAliasRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get all aliases for authenticated user
  fastify.get('/aliases', {
    preHandler: [aliasAuth.read()],
    schema: {
      tags: ['User Aliases'],
      summary: 'Get user aliases',
      description: 'Retrieves all aliases for the authenticated user. Requires aliases:read scope.',
      response: {
        200: aliasSchemas.AliasListResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, aliasesController.getAliases.bind(aliasesController));

  // Create new alias - UNIFIED SECURITY: validates scope + plan + limits
  fastify.post('/aliases', {
    preHandler: [aliasAuth.create()],
    schema: {
      tags: ['User Aliases'],
      summary: 'Create a new alias',
      description: 'Creates a new email alias for a verified domain. Validates aliases:write scope and available alias slots.',
      body: aliasSchemas.CreateAliasRequest,
      response: {
        201: aliasSchemas.CreateAliasResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        402: { $ref: 'ErrorResponse#' }, // Payment Required
        403: errorResponseSchema,
        404: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.createAlias.bind(aliasesController));

  // Get specific alias
  fastify.get('/aliases/:aliasId', {
    preHandler: [aliasOwnerAuth], // Validates ownership + read permissions
    schema: {
      tags: ['User Aliases'],
      summary: 'Get alias by ID',
      description: 'Retrieves a specific alias by ID. Validates ownership and aliases:read scope.',
      params: aliasSchemas.AliasIdParam,
      response: {
        200: aliasSchemas.AliasDetailResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.getAlias.bind(aliasesController));

  // Update alias
  fastify.put('/aliases/:aliasId', {
    preHandler: [aliasOwnerAuth],
    schema: {
      tags: ['User Aliases'],
      summary: 'Update alias',
      description: 'Updates an alias. Validates ownership and aliases:write scope.',
      params: aliasSchemas.AliasIdParam,
      body: aliasSchemas.UpdateAliasRequest,
      response: {
        200: aliasSchemas.UpdateAliasResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.updateAlias.bind(aliasesController));

  // Update alias webhook
  fastify.put('/aliases/:aliasId/webhook', {
    preHandler: [aliasOwnerAuth],
    schema: {
      tags: ['User Aliases'],
      summary: 'Update alias webhook',
      description: 'Update the webhook for an alias by ID. Validates ownership and aliases:write scope.',
      params: aliasSchemas.AliasIdParam,
      body: aliasSchemas.UpdateAliasWebhookRequest,
      response: {
        200: aliasSchemas.UpdateAliasWebhookResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, aliasesController.updateAliasWebhook.bind(aliasesController));

  // Delete alias
  fastify.delete('/aliases/:aliasId', {
    preHandler: [aliasOwnerAuth],
    schema: {
      tags: ['User Aliases'],
      summary: 'Delete alias',
      description: 'Deletes an alias. Validates ownership and aliases:write scope.',
      params: aliasSchemas.AliasIdParam,
      response: {
        200: aliasSchemas.DeleteAliasResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.deleteAlias.bind(aliasesController));

  // Get aliases for a specific domain
  fastify.get('/domains/:domainId/aliases', {
    preHandler: [domainAuth.read()], // Domain-scoped read access
    schema: {
      tags: ['User Aliases'],
      summary: 'Get aliases for a domain',
      description: 'Retrieves all aliases for a specific domain. Requires domains:read scope.',
      params: aliasSchemas.DomainIdParam,
      response: {
        200: aliasSchemas.DomainAliasListResponse,
        401: { $ref: 'ErrorResponse#' },
        403: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.getDomainAliases.bind(aliasesController));
};
