import { FastifyPluginAsync } from 'fastify';
import { <PERSON><PERSON><PERSON>ebhookController } from '../controllers/webhooks/mollie-webhook.controller.js';

const mollieWebhookController = new <PERSON>llieWebhookController();

export const mollieWebhookRoutes: FastifyPluginAsync = async (fastify) => {
  // Mollie payment webhook
  fastify.post('/payment', {
    schema: {
      tags: ['Webhooks'],
      summary: 'Mollie payment webhook',
      description: 'Receive payment status updates from <PERSON><PERSON>',
      body: {
        type: 'object',
        properties: {
          id: { 
            type: 'string',
            description: 'Mollie payment ID'
          }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' }
          }
        },
        400: {
          type: 'object',
          properties: {
            statusCode: { type: 'integer' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        401: {
          type: 'object',
          properties: {
            statusCode: { type: 'integer' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        500: {
          type: 'object',
          properties: {
            statusCode: { type: 'integer' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, mollieWebhookController.handlePaymentWebhook);

  // Mollie subscription webhook
  fastify.post('/subscription', {
    schema: {
      tags: ['Webhooks'],
      summary: 'Mollie subscription webhook',
      description: 'Receive subscription status updates from Mollie',
      body: {
        type: 'object',
        properties: {
          id: { 
            type: 'string',
            description: 'Mollie subscription ID'
          }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' }
          }
        },
        400: {
          type: 'object',
          properties: {
            statusCode: { type: 'integer' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        401: {
          type: 'object',
          properties: {
            statusCode: { type: 'integer' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        500: {
          type: 'object',
          properties: {
            statusCode: { type: 'integer' },
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, mollieWebhookController.handleSubscriptionWebhook);
};
