import { PrismaClient } from '@prisma/client';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';

// Global variable to store the Prisma client instance
let client: PrismaClient | null = null;

/**
 * Get or create a singleton Prisma client instance
 * This ensures we don't create multiple connections in serverless environments
 */
export function getPrismaClient(): PrismaClient {
  if (!client) {
    logger.info('Initializing Prisma client...');
    
    client = new PrismaClient({
      datasources: {
        db: {
          url: env.DATABASE_URL,
        },
      },
      log: env.NODE_ENV === 'development' 
        ? ['query', 'info', 'warn', 'error']
        : ['warn', 'error'],
    });

    logger.info('Prisma client initialized successfully');
  }

  return client;
}

/**
 * Connect to the database
 * Call this during application startup
 */
export async function connectDatabase(): Promise<void> {
  try {
    const prismaClient = getPrismaClient();
    await prismaClient.$connect();
    logger.info('Database connected successfully');
  } catch (error) {
    logger.error({ error: error.message }, 'Failed to connect to database');
    throw error;
  }
}

/**
 * Disconnect from the database
 * Call this during application shutdown
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    if (client) {
      await client.$disconnect();
      client = null;
      logger.info('Database disconnected successfully');
    }
  } catch (error) {
    logger.error({ error: error.message }, 'Error disconnecting from database');
    throw error;
  }
}

/**
 * Check database connection health
 * Used for health checks and monitoring
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const prismaClient = getPrismaClient();
    await prismaClient.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error({ error: error.message }, 'Database health check failed');
    return false;
  }
}

/**
 * Export the client instance for direct use
 * This is the main export that other modules should use
 */
export const prisma = getPrismaClient();

// Default export for convenience
export default prisma;
