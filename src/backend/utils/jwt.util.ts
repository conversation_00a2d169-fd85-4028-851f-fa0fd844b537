import jwt from 'jsonwebtoken';
import { logger } from './logger.js';

const USER_JWT_SECRET = process.env.USER_JWT_SECRET || 'your-secret-key-change-in-production';

export interface UserTokenPayload {
  userId: string;
  email: string;
  iat?: number;
  exp?: number;
}

/**
 * JWT utility functions for token verification
 * This utility is separate from UserAuthService to avoid circular dependencies
 */
export class JWTUtil {
  /**
   * Verify user JWT token
   */
  static verifyToken(token: string): { success: boolean; payload?: UserTokenPayload; error?: string } {
    try {
      const decoded = jwt.verify(token, USER_JWT_SECRET) as UserTokenPayload;
      return { success: true, payload: decoded };
    } catch (error) {
      logger.warn({ err: error }, 'User JWT verification failed');
      return { success: false, error: 'Invalid token' };
    }
  }

  /**
   * Generate user JWT token
   */
  static generateToken(payload: { userId: string; email: string }): string {
    return jwt.sign(payload, USER_JWT_SECRET, { expiresIn: '7d' });
  }
}
