// Centralized OpenAPI schemas that can be imported by route files and openapi-spec.ts
import { OpenAPIV3 } from 'openapi-types';

export const commonSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // General Error Schema
  ErrorResponse: {
    type: 'object',
    properties: {
      statusCode: { type: 'integer', example: 400 },
      error: { type: 'string', example: 'Bad Request' },
      message: { type: 'string', example: 'Invalid input' },
    },
    required: ['statusCode', 'error', 'message'],
  },
};

export const authSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // Admin Login Schemas
  AdminLoginPayload: {
    type: 'object',
    properties: {
      username: { type: 'string', example: 'admin' },
      password: { type: 'string', format: 'password', example: 'securepassword123' },
    },
    required: ['username', 'password'],
  },
  AdminLoginSuccessResponse: {
    type: 'object',
    properties: {
      message: { type: 'string', example: 'Admin login successful' },
      token: { type: 'string', description: "JWT token (also set as httpOnly cookie 'admin_token')" }
    },
  },

  // User Profile Schema
  UserProfile: {
    type: 'object',
    properties: {
      id: { type: 'string', example: 'cuid123' },
      email: { type: 'string', format: 'email', example: '<EMAIL>' },
      name: { type: 'string', nullable: true, example: 'John Doe' },
      monthlyEmailLimit: { type: 'integer', example: 50 },
      planType: { type: 'string', enum: ['free', 'pro', 'enterprise'], example: 'free' },
      currentMonthEmails: { type: 'integer', example: 25 },
      verified: { type: 'boolean', example: true }
    },
    required: ['id', 'email', 'monthlyEmailLimit', 'planType', 'currentMonthEmails', 'verified']
  },
};

export const domainSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Domain Schemas ---
  Domain: {
    type: 'object',
    properties: {
      id: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a', readOnly: true },
      domain: { type: 'string', example: 'example.com' },
      webhook: {
        type: 'object',
        nullable: true,
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string', format: 'uri' },
          verified: { type: 'boolean' }
        }
      },
      webhookUrl: { type: 'string', nullable: true, format: 'uri', example: 'https://myapp.com/webhook' },
      webhookName: { type: 'string', nullable: true, example: 'Main Webhook' },
      active: { type: 'boolean', example: true },
      isVerified: { type: 'boolean', example: true, readOnly: true },
      verificationStatus: {
        type: 'string',
        enum: ['PENDING', 'VERIFIED', 'ACTIVE', 'WARNING', 'SUSPENDED', 'FAILED'],
        example: 'VERIFIED',
        readOnly: true
      },
      configuration: {
        type: 'object',
        nullable: true,
        properties: {
          allowAttachments: { type: 'boolean' },
          includeEnvelope: { type: 'boolean' }
        }
      },
      postfix_configured: { type: 'boolean', example: true, readOnly: true },
      lastVerificationAttempt: { type: 'string', format: 'date-time', nullable: true, readOnly: true },
      nextVerificationCheck: { type: 'string', format: 'date-time', nullable: true, readOnly: true },
      verificationFailureCount: { type: 'integer', example: 0, readOnly: true },
      expectedTxtRecord: { type: 'string', nullable: true, readOnly: true },
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            webhookName: { type: 'string', nullable: true },
            active: { type: 'boolean' }
          }
        }
      },
      createdAt: { type: 'string', format: 'date-time', readOnly: true },
      updatedAt: { type: 'string', format: 'date-time', readOnly: true },
    },
    required: ['id', 'domain', 'active', 'isVerified'],
  },
  CreateDomainPayload: {
    type: 'object',
    properties: {
      domain: { type: 'string', example: 'mydomain.com', description: 'Domain name to add' },
      webhookId: { type: 'string', example: 'cmbw62nwc002bru1qf7d72akd', description: 'ID of existing webhook to use' },
      webhookUrl: { type: 'string', format: 'uri', example: 'https://myapp.com/webhook', description: 'URL for new webhook (alternative to webhookId)' },
      webhookName: { type: 'string', example: 'My Domain Webhook', description: 'Name for new webhook (used with webhookUrl)' },
      webhookDescription: { type: 'string', example: 'Handles emails for mydomain.com', description: 'Description for new webhook' },
      createCatchAll: { type: 'boolean', default: true, description: 'Whether to create a catch-all alias (*@domain.com)' },
      active: { type: 'boolean', default: true, description: 'Whether domain should be active' }
    },
    required: ['domain'],
    oneOf: [
      { required: ['domain', 'webhookId'] },
      { required: ['domain', 'webhookUrl', 'webhookName'] }
    ]
  },
  UpdateDomainStatusRequest: {
    type: 'object',
    properties: {
      active: { type: 'boolean', description: 'New active status' }
    },
    required: ['active']
  },
  UpdateDomainStatusResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' },
          domain: { type: 'string' },
          active: { type: 'boolean' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      message: { type: 'string' }
    }
  },
  DomainStatus: {
    type: 'object',
    properties: {
      domain: { type: 'string', example: 'example.com' },
      isVerified: { type: 'boolean' },
      dnsRecords: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            type: { type: 'string', example: 'TXT' },
            name: { type: 'string', example: 'example.com or _dkim.example.com' },
            value: { type: 'string', example: 'verification-token or dkim-public-key' },
            status: { type: 'string', enum: ['verified', 'pending', 'failed'], example: 'pending' },
          },
        },
      },
    },
  },
};

export const aliasSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Alias Schemas ---
  Alias: {
    type: 'object',
    properties: {
      id: { type: 'string', example: 'cmbw6hopp002wru1qk18cowjg', readOnly: true },
      email: { type: 'string', example: '<EMAIL>', description: 'Full email address or catch-all pattern (*@domain.com)' },
      active: { type: 'boolean', example: true },
      domainId: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' },
      webhookId: { type: 'string', example: 'cmbw62nwc002bru1qf7d72akd' },
      createdAt: { type: 'string', format: 'date-time', readOnly: true },
      updatedAt: { type: 'string', format: 'date-time', readOnly: true },
      configuration: {
        type: 'object',
        nullable: true,
        properties: {
          allowAttachments: { type: 'boolean' },
          includeEnvelope: { type: 'boolean' }
        }
      },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' }
        }
      }
    },
    required: ['id', 'email', 'active', 'domainId', 'webhookId'],
  },
  CreateAliasPayload: {
    type: 'object',
    properties: {
      email: {
        type: 'string',
        example: '<EMAIL>',
        description: 'Full email address or catch-all pattern (e.g., <EMAIL> or *@domain.com)'
      },
      domainId: {
        type: 'string',
        example: 'cmbw6hopl002uru1qqyx7b18a',
        description: 'ID of the domain to create alias for'
      },
      webhookId: {
        type: 'string',
        example: 'cmbw62nwc002bru1qf7d72akd',
        description: 'ID of the webhook to use for this alias'
      },
      active: { type: 'boolean', default: true }
    },
    required: ['email', 'domainId', 'webhookId'],
  },
  UpdateAliasPayload: {
    type: 'object',
    properties: {
      email: {
        type: 'string',
        example: '<EMAIL>',
        description: 'Full email address or catch-all pattern (e.g., <EMAIL> or *@domain.com)'
      },
      webhookId: {
        type: 'string',
        example: 'cmbw62nwc002bru1qf7d72akd',
        description: 'ID of the webhook to use for this alias'
      },
      active: { type: 'boolean' },
      allowAttachments: { type: 'boolean', nullable: true },
      includeEnvelope: { type: 'boolean', nullable: true }
    },
    minProperties: 1
  },
};

export const webhookSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Webhook Schemas ---
  Webhook: {
    type: 'object',
    properties: {
      id: { type: 'string', example: 'cmbw62nwc002bru1qf7d72akd' },
      name: { type: 'string', example: 'Main Webhook' },
      url: { type: 'string', format: 'uri', example: 'https://myapp.com/webhook' },
      description: { type: 'string', nullable: true, example: 'Main email processing webhook' },
      active: { type: 'boolean', example: true },
      verified: { type: 'boolean', example: true },
      hasSecret: { type: 'boolean', example: false },
      createdAt: { type: 'string', format: 'date-time', readOnly: true },
      updatedAt: { type: 'string', format: 'date-time', readOnly: true },
      domainCount: { type: 'integer', example: 2, description: 'Number of domains using this webhook' },
      aliasCount: { type: 'integer', example: 5, description: 'Number of aliases using this webhook' }
    },
    required: ['id', 'name', 'url', 'active', 'verified']
  },
  CreateWebhookPayload: {
    type: 'object',
    properties: {
      name: { type: 'string', example: 'My Webhook', description: 'Name for the webhook' },
      url: { type: 'string', format: 'uri', example: 'https://myapp.com/webhook', description: 'Webhook URL' },
      description: { type: 'string', example: 'Handles email notifications', description: 'Optional description' }
    },
    required: ['name', 'url']
  },
  UpdateWebhookPayload: {
    type: 'object',
    properties: {
      name: { type: 'string', example: 'Updated Webhook Name' },
      url: { type: 'string', format: 'uri', example: 'https://myapp.com/webhook/updated' },
      description: { type: 'string', example: 'Updated description' },
      active: { type: 'boolean', example: true }
    },
    minProperties: 1
  },
  // Email Log Schema (what was previously called WebhookLog)
  EmailLog: {
    type: 'object',
    properties: {
      id: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' },
      messageId: { type: 'string', example: 'msg_abc123' },
      fromAddress: { type: 'string', example: '<EMAIL>' },
      toAddresses: {
        type: 'array',
        items: { type: 'string' },
        example: ['<EMAIL>']
      },
      subject: { type: 'string', nullable: true, example: 'Test Email Subject' },
      deliveryStatus: {
        type: 'string',
        enum: ['PENDING', 'DELIVERED', 'FAILED', 'RETRYING', 'EXPIRED'],
        example: 'DELIVERED'
      },
      deliveryAttempts: { type: 'integer', example: 1 },
      lastAttemptAt: { type: 'string', format: 'date-time', nullable: true },
      deliveredAt: { type: 'string', format: 'date-time', nullable: true },
      errorMessage: { type: 'string', nullable: true },
      createdAt: { type: 'string', format: 'date-time' },
      expiresAt: { type: 'string', format: 'date-time' },
      isTestWebhook: { type: 'boolean', example: false },
      webhookPayload: { type: 'object', nullable: true, description: 'Stored webhook payload for test webhooks' },
      domain: {
        type: 'object',
        nullable: true,
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' }
        }
      }
    },
    required: ['id', 'messageId', 'fromAddress', 'toAddresses', 'deliveryStatus', 'deliveryAttempts', 'createdAt', 'expiresAt']
  }
};

export const webhookAliasSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Webhook-Alias Combined Schemas ---
  CreateWebhookAliasRequest: {
    type: 'object',
    required: ['domainId', 'webhookUrl', 'webhookName', 'aliasType'],
    properties: {
      domainId: {
        type: 'string',
        description: 'ID of the domain to create the alias for'
      },
      webhookUrl: {
        type: 'string',
        format: 'uri',
        description: 'URL for the webhook endpoint'
      },
      webhookName: {
        type: 'string',
        minLength: 1,
        maxLength: 100,
        description: 'Name for the webhook'
      },
      webhookDescription: {
        type: 'string',
        maxLength: 500,
        description: 'Optional description for the webhook'
      },
      aliasType: {
        type: 'string',
        enum: ['catchall', 'specific'],
        description: 'Type of alias to create - catchall (*@domain.com) or specific (<EMAIL>)'
      },
      localPart: {
        type: 'string',
        minLength: 1,
        maxLength: 64,
        description: 'Local part for specific aliases (required when aliasType is "specific")'
      },
      syncWithDomain: {
        type: 'boolean',
        default: true,
        description: 'For catch-all aliases, whether to sync webhook with domain webhook (default: true)'
      },
      autoVerify: {
        type: 'boolean',
        default: false,
        description: 'Whether to auto-verify webhook using last 5 characters method'
      }
    }
  },
  CreateWebhookAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          verificationToken: {
            type: 'string',
            nullable: true,
            description: 'Token for manual verification if auto-verify is false'
          },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          active: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      domain: {
        type: 'object',
        nullable: true,
        description: 'Domain information if webhook was synced with domain',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          webhookUpdated: { type: 'boolean' }
        }
      },
      message: {
        type: 'string',
        description: 'Success message with details about the operation'
      }
    }
  },
  WebhookAliasErrorResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean', enum: [false] },
      error: { type: 'string' },
      details: {
        type: 'object',
        nullable: true,
        properties: {
          field: { type: 'string' },
          code: { type: 'string' },
          message: { type: 'string' }
        }
      }
    }
  }
};

export const adminSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- Admin Schemas ---
  AdminDomainView: {
    allOf: [
      { $ref: 'Domain#' },
      {
        type: 'object',
        properties: {
          user: { // Basic user info, expand as needed
            type: 'object',
            properties: {
              id: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' },
              email: { type: 'string', format: 'email' }, // Example user identifier
            },
          },
        },
      },
    ],
  },
  DeliveryStatistic: {
    type: 'object',
    properties: {
      date: { type: 'string', format: 'date' },
      totalReceived: { type: 'integer' },
      totalDelivered: { type: 'integer' },
      totalFailed: { type: 'integer' },
    },
  },
};

export const userSettingsSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  // --- User Settings Schemas ---
  UserSettingsResponse: {
    type: 'object',
    properties: {
      id: { type: 'string', nullable: true },
      maxInlineSize: { type: 'number', minimum: 0.1, maximum: 10 },
      allowMediaFiles: { type: 'boolean' },
      storageProvider: { type: 'string', enum: ['default', 's3-compatible'] },
      dataRetentionHours: { type: 'integer', minimum: 1, maximum: 8760, nullable: true },
      s3Config: {
        type: 'object',
        nullable: true,
        properties: {
          region: { type: 'string' },
          bucket: { type: 'string' },
          accessKey: { type: 'string' },
          secretKey: { type: 'string' },
          endpoint: { type: 'string', nullable: true }
        }
      },
      createdAt: { type: 'string', format: 'date-time', nullable: true },
      updatedAt: { type: 'string', format: 'date-time', nullable: true }
    }
  },
  GetUserSettingsResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      settings: {
        type: 'object',
        properties: {
          id: { type: 'string', nullable: true },
          maxInlineSize: { type: 'number', minimum: 0.1, maximum: 10 },
          allowMediaFiles: { type: 'boolean' },
          storageProvider: { type: 'string', enum: ['default', 's3-compatible'] },
          dataRetentionHours: { type: 'integer', minimum: 1, maximum: 8760, nullable: true },
          s3Config: {
            type: 'object',
            nullable: true,
            properties: {
              region: { type: 'string' },
              bucket: { type: 'string' },
              accessKey: { type: 'string' },
              secretKey: { type: 'string' },
              endpoint: { type: 'string', nullable: true }
            }
          },
          createdAt: { type: 'string', format: 'date-time', nullable: true },
          updatedAt: { type: 'string', format: 'date-time', nullable: true }
        }
      }
    },
    required: ['success', 'settings']
  },
  UpdateUserSettingsResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      settings: {
        type: 'object',
        properties: {
          id: { type: 'string', nullable: true },
          maxInlineSize: { type: 'number', minimum: 0.1, maximum: 10 },
          allowMediaFiles: { type: 'boolean' },
          storageProvider: { type: 'string', enum: ['default', 's3-compatible'] },
          dataRetentionHours: { type: 'integer', minimum: 1, maximum: 8760, nullable: true },
          s3Config: {
            type: 'object',
            nullable: true,
            properties: {
              region: { type: 'string' },
              bucket: { type: 'string' },
              accessKey: { type: 'string' },
              secretKey: { type: 'string' },
              endpoint: { type: 'string', nullable: true }
            }
          },
          createdAt: { type: 'string', format: 'date-time', nullable: true },
          updatedAt: { type: 'string', format: 'date-time', nullable: true }
        }
      }
    },
    required: ['success', 'message', 'settings']
  },
  ResetUserSettingsResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' }
    },
    required: ['success', 'message']
  },
  UpdateUserSettingsRequest: {
    type: 'object',
    properties: {
      maxInlineSize: {
        type: 'number',
        minimum: 0.1,
        maximum: 10,
        description: 'Maximum attachment size in MB (Pro: up to 10MB, Free: up to 1MB)'
      },
      allowMediaFiles: {
        type: 'boolean',
        description: 'Allow processing of media files like images and videos (Pro only)'
      },
      storageProvider: {
        type: 'string',
        enum: ['default', 's3-compatible'],
        description: 'Storage provider to use (s3-compatible requires Pro plan)'
      },
      dataRetentionHours: {
        type: 'integer',
        minimum: 1,
        maximum: 8760,
        nullable: true,
        description: 'Custom data retention period in hours (Pro only, null = use plan default)'
      },
      s3Config: {
        type: 'object',
        nullable: true,
        description: 'S3-compatible storage configuration (Pro only)',
        properties: {
          region: {
            type: 'string',
            minLength: 2,
            description: 'AWS region or S3-compatible region'
          },
          bucket: {
            type: 'string',
            minLength: 3,
            description: 'S3 bucket name'
          },
          accessKey: {
            type: 'string',
            minLength: 10,
            description: 'S3 access key ID'
          },
          secretKey: {
            type: 'string',
            minLength: 10,
            description: 'S3 secret access key'
          },
          endpoint: {
            type: 'string',
            format: 'url',
            nullable: true,
            description: 'Custom S3 endpoint URL (optional, for S3-compatible services)'
          }
        },
        required: ['region', 'bucket', 'accessKey', 'secretKey']
      }
    },
    additionalProperties: false
  }
};

// Common response schemas
export const responseSchemas: Record<string, OpenAPIV3.ResponseObject> = {
  BadRequest: {
    description: 'Invalid request payload or parameters.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  Unauthorized: {
    description: 'Authentication failed or missing API key for user endpoint.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  Forbidden: {
    description: 'User is authenticated but does not have permission to access the resource.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  NotFound: {
    description: 'The requested resource was not found.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  UnauthorizedAdmin: {
    description: 'Authentication failed or missing credentials for admin endpoint.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  },
  ForbiddenAdmin: {
    description: 'Admin is authenticated but does not have permission for this specific admin action.',
    content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
  }
};

// Combine all schemas for easy import
export const allSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  ...commonSchemas,
  ...authSchemas,
  ...domainSchemas,
  ...aliasSchemas,
  ...webhookSchemas,
  ...webhookAliasSchemas,
  ...userSettingsSchemas,
  ...adminSchemas, // Include for route functionality, filter out in Swagger docs
};

// Public schemas for API documentation (excludes admin schemas)
export const publicSchemas: Record<string, OpenAPIV3.SchemaObject> = {
  ...commonSchemas,
  ...authSchemas,
  ...domainSchemas,
  ...aliasSchemas,
  ...webhookSchemas,
  ...webhookAliasSchemas,
  ...userSettingsSchemas,
};

export const allResponses = responseSchemas;
