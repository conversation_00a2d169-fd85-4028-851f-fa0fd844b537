export const aliasSchemas = {
  // Alias response schemas
  AliasResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      email: { type: 'string' },
      active: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      configuration: {
        type: 'object',
        nullable: true,
        properties: {
          allowAttachments: { type: 'boolean' },
          includeEnvelope: { type: 'boolean' },
          attachmentHandling: { type: 'string', enum: ['inline', 'storage'] },
          s3Folder: { type: 'string' }
        }
      },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' }
        }
      }
    }
  },

  AliasDetailResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      email: { type: 'string' },
      active: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      configuration: {
        type: 'object',
        nullable: true,
        properties: {
          allowAttachments: { type: 'boolean' },
          includeEnvelope: { type: 'boolean' },
          attachmentHandling: { type: 'string', enum: ['inline', 'storage'] },
          s3Folder: { type: 'string' }
        }
      },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' }
        }
      }
    }
  },

  // Request body schemas
  CreateAliasRequest: {
    type: 'object',
    properties: {
      email: {
        type: 'string',
        description: 'Full email address or catch-all pattern (e.g., <EMAIL> or *@domain.com)'
      },
      domainId: {
        type: 'string',
        description: 'ID of the domain to create alias for'
      },
      webhookId: {
        type: 'string',
        description: 'ID of the webhook to use for this alias'
      },
      active: { type: 'boolean', default: true },
      allowAttachments: { type: 'boolean', nullable: true, description: 'Enable attachment processing for this alias' },
      includeEnvelope: { type: 'boolean', nullable: true, description: 'Include email envelope data in webhook payload' },
      attachmentHandling: { type: 'string', enum: ['inline', 'storage'], nullable: true, description: 'How to handle attachments: inline (base64) or storage (S3)' },
      s3Folder: { type: 'string', nullable: true, description: 'S3 folder path for storing attachments when using storage handling' }
    },
    required: ['email', 'domainId', 'webhookId']
  },

  UpdateAliasRequest: {
    type: 'object',
    properties: {
      email: {
        type: 'string',
        description: 'Full email address or catch-all pattern (e.g., <EMAIL> or *@domain.com)'
      },
      webhookId: {
        type: 'string',
        description: 'ID of the webhook to use for this alias'
      },
      active: { type: 'boolean' },
      allowAttachments: { type: 'boolean', nullable: true },
      includeEnvelope: { type: 'boolean', nullable: true },
      attachmentHandling: { type: 'string', enum: ['inline', 'storage'], nullable: true },
      s3Folder: { type: 'string', nullable: true }
    },
    minProperties: 1
  },

  UpdateAliasWebhookRequest: {
    type: 'object',
    properties: {
      webhookId: { type: 'string', description: 'New webhook ID' }
    },
    required: ['webhookId']
  },

  // Success response schemas
  CreateAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          active: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          domainId: { type: 'string' },
          webhookId: { type: 'string' }
        }
      }
    }
  },

  UpdateAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          active: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          domainId: { type: 'string' },
          webhookId: { type: 'string' }
        }
      }
    }
  },

  UpdateAliasWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      alias: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          webhookId: { type: 'string' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' }
        }
      },
      message: { type: 'string' }
    }
  },

  AliasListResponse: {
    type: 'object',
    properties: {
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            active: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            configuration: {
              type: 'object',
              nullable: true,
              properties: {
                allowAttachments: { type: 'boolean' },
                includeEnvelope: { type: 'boolean' },
                attachmentHandling: { type: 'string', enum: ['inline', 'storage'] },
                s3Folder: { type: 'string' }
              }
            },
            domain: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                domain: { type: 'string' },
                verified: { type: 'boolean' }
              }
            },
            webhook: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' },
                verified: { type: 'boolean' },
                hasSecret: { type: 'boolean' }
              }
            }
          }
        }
      },
      total: { type: 'integer' }
    }
  },

  DomainAliasListResponse: {
    type: 'object',
    properties: {
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            active: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            configuration: {
              type: 'object',
              nullable: true,
              properties: {
                allowAttachments: { type: 'boolean' },
                includeEnvelope: { type: 'boolean' },
                attachmentHandling: { type: 'string', enum: ['inline', 'storage'] },
                s3Folder: { type: 'string' }
              }
            },
            webhook: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                url: { type: 'string' },
                verified: { type: 'boolean' },
                hasSecret: { type: 'boolean' }
              }
            }
          }
        }
      },
      domain: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          domain: { type: 'string' },
          verified: { type: 'boolean' }
        }
      },
      total: { type: 'integer' }
    }
  },

  DeleteAliasResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' }
    }
  },

  // Parameter schemas
  AliasIdParam: {
    type: 'object',
    properties: {
      aliasId: { type: 'string' }
    },
    required: ['aliasId']
  },

  DomainIdParam: {
    type: 'object',
    properties: {
      domainId: { type: 'string' }
    },
    required: ['domainId']
  }
};
