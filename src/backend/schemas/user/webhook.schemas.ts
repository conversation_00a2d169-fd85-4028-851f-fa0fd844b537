export const webhookSchemas = {
  // Webhook response schemas
  WebhookResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      url: { type: 'string', format: 'url' },
      description: { type: 'string', nullable: true },
      active: { type: 'boolean' },
      verified: { type: 'boolean' },
      hasSecret: { type: 'boolean' },
      customHeaders: {
        type: 'object',
        nullable: true,
        additionalProperties: { type: 'string' },
        description: 'Custom HTTP headers for webhook requests'
      },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      domainCount: { type: 'integer' },
      aliasCount: { type: 'integer' }
    }
  },

  WebhookDetailResponse: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      url: { type: 'string' },
      description: { type: 'string', nullable: true },
      active: { type: 'boolean' },
      verified: { type: 'boolean' },
      hasSecret: { type: 'boolean' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
      domains: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            domain: { type: 'string' }
          }
        }
      },
      aliases: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' }
          }
        }
      }
    }
  },

  // Request body schemas
  CreateWebhookRequest: {
    type: 'object',
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 100 },
      url: { type: 'string', format: 'url' },
      description: { type: 'string', maxLength: 500 },
      active: { type: 'boolean', default: true },
      webhookSecret: {
        type: 'string',
        minLength: 16,
        maxLength: 128,
        description: 'Optional HMAC secret for webhook signature verification'
      },
      generateSecret: {
        type: 'boolean',
        default: false,
        description: 'Auto-generate a secure webhook secret'
      },
      customHeaders: {
        type: 'object',
        additionalProperties: { type: 'string' },
        maxProperties: 10,
        description: 'Custom HTTP headers for webhook requests (Pro feature)'
      }
    },
    required: ['name', 'url']
  },

  UpdateWebhookRequest: {
    type: 'object',
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 100 },
      url: { type: 'string', format: 'url' },
      description: { type: 'string', maxLength: 500 },
      active: { type: 'boolean' },
      webhookSecret: {
        type: 'string',
        minLength: 16,
        maxLength: 128,
        description: 'HMAC secret for webhook signature verification'
      },
      removeSecret: {
        type: 'boolean',
        description: 'Remove the current webhook secret'
      },
      generateSecret: {
        type: 'boolean',
        description: 'Generate a new secure webhook secret'
      },
      customHeaders: {
        type: 'object',
        additionalProperties: { type: 'string' },
        maxProperties: 10,
        description: 'Custom HTTP headers for webhook requests (Pro feature)'
      }
    },
    minProperties: 1
  },

  // Success response schemas
  CreateWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          description: { type: 'string', nullable: true },
          active: { type: 'boolean' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' },
          webhookSecret: { type: 'string', description: 'Only returned if auto-generated' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  },

  UpdateWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          url: { type: 'string' },
          description: { type: 'string', nullable: true },
          active: { type: 'boolean' },
          verified: { type: 'boolean' },
          hasSecret: { type: 'boolean' },
          webhookSecret: { type: 'string', description: 'Only returned if auto-generated' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  },

  WebhookListResponse: {
    type: 'object',
    properties: {
      webhooks: {
        type: 'array',
        items: { $ref: '#/components/schemas/WebhookResponse' }
      },
      total: { type: 'integer' }
    }
  },

  DeleteWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' }
    }
  },

  VerifyWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          verified: { type: 'boolean' }
        }
      }
    }
  },

  TestWebhookResponse: {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      webhook: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          url: { type: 'string' },
          name: { type: 'string' }
        }
      },
      test: {
        type: 'object',
        properties: {
          messageId: { type: 'string' },
          jobId: { type: 'string' },
          sentAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  },

  // Parameter schemas
  WebhookIdParam: {
    type: 'object',
    properties: {
      webhookId: { type: 'string' }
    },
    required: ['webhookId']
  }
};
