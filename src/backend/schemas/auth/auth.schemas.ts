export const authSchemas = {
  // Admin auth schemas
  AdminLoginRequest: {
    type: 'object',
    properties: {
      username: { type: 'string', minLength: 1 },
      password: { type: 'string', minLength: 1 }
    },
    required: ['username', 'password']
  },

  AdminLoginResponse: {
    type: 'object',
    properties: {
      message: { type: 'string' },
      token: { type: 'string' }
    }
  },

  // User auth schemas
  UserRegisterRequest: {
    type: 'object',
    properties: {
      email: { 
        type: 'string', 
        format: 'email',
        description: 'User email address'
      },
      password: { 
        type: 'string', 
        minLength: 8,
        description: 'Password (min 8 chars, must contain uppercase, lowercase, and number)'
      },
      name: { 
        type: 'string', 
        maxLength: 100,
        description: 'Optional display name'
      }
    },
    required: ['email', 'password']
  },

  UserLoginRequest: {
    type: 'object',
    properties: {
      email: { type: 'string', format: 'email' },
      password: { type: 'string', minLength: 1 }
    },
    required: ['email', 'password']
  },

  UserProfile: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      email: { type: 'string' },
      name: { type: 'string', nullable: true },
      monthlyEmailLimit: { type: 'integer' },
      planType: { type: 'string' },
      currentMonthEmails: { type: 'integer' },
      verified: { type: 'boolean' }
    }
  },

  UserLoginResponse: {
    type: 'object',
    properties: {
      message: { type: 'string' },
      user: { $ref: '#/components/schemas/UserProfile' },
      token: { type: 'string' }
    }
  },

  UserRegisterResponse: {
    type: 'object',
    properties: {
      message: { type: 'string' },
      user: { $ref: '#/components/schemas/UserProfile' },
      token: { type: 'string' }
    }
  },

  // Error response schemas
  AuthErrorResponse: {
    type: 'object',
    properties: {
      error: { type: 'string' }
    }
  }
};
