// Common JSON Schema definitions for Fastify
export const ErrorResponseSchema = {
  $id: 'ErrorResponse',
  type: 'object',
  properties: {
    statusCode: { type: 'number' },
    error: { type: 'string' },
    message: { type: 'string' },
    details: { type: 'string', description: 'Additional error details (optional)' },
  },
  required: ['statusCode', 'error', 'message'],
  additionalProperties: false,
};

export const SuccessResponseSchema = {
  $id: 'SuccessResponse',
  type: 'object',
  properties: {
    success: { type: 'boolean' },
    message: { type: 'string' },
    timestamp: { type: 'string', format: 'date-time' },
  },
  required: ['success'],
  additionalProperties: true,
};

// Register all common schemas with Fastify
export const registerCommonSchemas = async (fastify: any) => {
  if (fastify && fastify.log) {
    fastify.log.info('registerCommonSchemas: Schemas are now primarily registered from OpenAPI spec in index.ts.');
  }
};
