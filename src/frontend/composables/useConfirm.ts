import { ref, reactive, readonly } from 'vue'

export interface ConfirmOptions {
  title: string
  message: string
  details?: string
  confirmText?: string
  cancelText?: string
  type?: 'default' | 'danger' | 'warning' | 'info'
}

interface ConfirmState {
  isOpen: boolean
  title: string
  message: string
  details?: string
  confirmText: string
  cancelText: string
  type: 'default' | 'danger' | 'warning' | 'info'
  loading: boolean
  resolver?: (value: boolean) => void
}

// Global confirm state
const confirmState = reactive<ConfirmState>({
  isOpen: false,
  title: '',
  message: '',
  details: undefined,
  confirmText: 'Confirm',
  cancelText: 'Cancel',
  type: 'default',
  loading: false,
  resolver: undefined
})

export function useConfirm() {
  const show = (options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      confirmState.isOpen = true
      confirmState.title = options.title
      confirmState.message = options.message
      confirmState.details = options.details
      confirmState.confirmText = options.confirmText || 'Confirm'
      confirmState.cancelText = options.cancelText || 'Cancel'
      confirmState.type = options.type || 'default'
      confirmState.loading = false
      confirmState.resolver = resolve
    })
  }

  const confirm = async () => {
    if (confirmState.resolver) {
      confirmState.loading = true
      
      // Small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 100))
      
      confirmState.resolver(true)
      close()
    }
  }

  const cancel = () => {
    if (confirmState.resolver) {
      confirmState.resolver(false)
      close()
    }
  }

  const close = () => {
    confirmState.isOpen = false
    confirmState.loading = false
    confirmState.resolver = undefined
  }

  // Convenience methods
  const confirmDelete = (itemName: string, itemType: string = 'item'): Promise<boolean> => {
    return show({
      title: `Delete ${itemType}`,
      message: `Are you sure you want to delete "${itemName}"?`,
      details: `
        <p class="font-medium mb-2">This will permanently delete:</p>
        <ul class="space-y-1">
          <li>• The ${itemType} configuration</li>
          <li>• All associated data</li>
          <li>• All related logs</li>
        </ul>
        <p class="font-medium mt-2 text-red-600">This action cannot be undone.</p>
      `,
      confirmText: `Delete ${itemType}`,
      cancelText: 'Cancel',
      type: 'danger'
    })
  }

  const confirmAction = (
    title: string,
    message: string,
    confirmText: string = 'Confirm'
  ): Promise<boolean> => {
    return show({
      title,
      message,
      confirmText,
      type: 'default'
    })
  }

  const confirmDanger = (
    title: string,
    message: string,
    confirmText: string = 'Confirm'
  ): Promise<boolean> => {
    return show({
      title,
      message,
      confirmText,
      type: 'danger'
    })
  }

  return {
    confirmState: readonly(confirmState),
    show,
    confirm,
    cancel,
    close,
    confirmDelete,
    confirmAction,
    confirmDanger
  }
}

// Global confirm functions for backward compatibility
declare global {
  interface Window {
    confirmDialog: {
      show: (options: ConfirmOptions) => Promise<boolean>
      confirmDelete: (itemName: string, itemType?: string) => Promise<boolean>
    }
  }
}

// Initialize global confirm functions
const { show, confirmDelete } = useConfirm()

window.confirmDialog = {
  show,
  confirmDelete
}
