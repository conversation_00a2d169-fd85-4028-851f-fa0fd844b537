import { ref, computed, reactive } from 'vue'

export interface ValidationRule {
  required?: boolean
  email?: boolean
  url?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

export interface FieldValidation {
  rules: ValidationRule[]
  error: string | null
  touched: boolean
}

export function useFormValidation() {
  const fields = reactive<Record<string, FieldValidation>>({})

  // Add a field to validation
  const addField = (name: string, rules: ValidationRule[] = []) => {
    fields[name] = {
      rules,
      error: null,
      touched: false
    }
  }

  // Validate a single field
  const validateField = (name: string, value: any): boolean => {
    const field = fields[name]
    if (!field) return true

    field.touched = true
    field.error = null

    for (const rule of field.rules) {
      // Required validation
      if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        field.error = 'This field is required'
        return false
      }

      // Skip other validations if value is empty and not required
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        continue
      }

      // Email validation
      if (rule.email && typeof value === 'string') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          field.error = 'Please enter a valid email address'
          return false
        }
      }

      // URL validation
      if (rule.url && typeof value === 'string') {
        try {
          new URL(value)
          if (!value.startsWith('http://') && !value.startsWith('https://')) {
            field.error = 'URL must start with http:// or https://'
            return false
          }
        } catch {
          field.error = 'Please enter a valid URL'
          return false
        }
      }

      // Min length validation
      if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
        field.error = `Must be at least ${rule.minLength} characters`
        return false
      }

      // Max length validation
      if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
        field.error = `Must be no more than ${rule.maxLength} characters`
        return false
      }

      // Pattern validation
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        field.error = 'Invalid format'
        return false
      }

      // Custom validation
      if (rule.custom) {
        const customError = rule.custom(value)
        if (customError) {
          field.error = customError
          return false
        }
      }
    }

    return true
  }

  // Validate all fields
  const validateAll = (formData: Record<string, any>): boolean => {
    let isValid = true
    
    for (const fieldName in fields) {
      const fieldValid = validateField(fieldName, formData[fieldName])
      if (!fieldValid) {
        isValid = false
      }
    }

    return isValid
  }

  // Get field error
  const getFieldError = (name: string): string | null => {
    return fields[name]?.error || null
  }

  // Check if field has error
  const hasFieldError = (name: string): boolean => {
    const field = fields[name]
    return !!(field?.touched && field?.error)
  }

  // Get field validation classes for DaisyUI
  const getFieldClasses = (name: string, baseClasses: string = 'input input-bordered'): string => {
    const field = fields[name]
    if (!field?.touched) return baseClasses

    if (field.error) {
      return `${baseClasses} input-error`
    }

    return `${baseClasses} input-success`
  }

  // Clear field error
  const clearFieldError = (name: string) => {
    const field = fields[name]
    if (field) {
      field.error = null
    }
  }

  // Clear all errors
  const clearAllErrors = () => {
    for (const fieldName in fields) {
      fields[fieldName].error = null
      fields[fieldName].touched = false
    }
  }

  // Check if form is valid (all fields without errors)
  const isFormValid = computed(() => {
    return Object.values(fields).every(field => !field.error)
  })

  // Check if any field has been touched
  const hasAnyTouched = computed(() => {
    return Object.values(fields).some(field => field.touched)
  })

  return {
    addField,
    validateField,
    validateAll,
    getFieldError,
    hasFieldError,
    getFieldClasses,
    clearFieldError,
    clearAllErrors,
    isFormValid,
    hasAnyTouched
  }
}
