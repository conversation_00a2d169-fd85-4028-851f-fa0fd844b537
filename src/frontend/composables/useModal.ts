import { ref, reactive, readonly, type Ref } from 'vue'
import type { ModalData, ModalConfig } from '@types'

interface ModalState {
  isOpen: boolean
  type: string | null
  data: ModalData
  config: ModalConfig
}

const modalState = reactive<ModalState>({
  isOpen: false,
  type: null,
  data: {},
  config: {
    title: '',
    size: 'md',
    closable: true,
    persistent: false
  }
})

export function useModal() {
  const openModal = (
    type: string,
    data: ModalData = {},
    config: Partial<ModalConfig> = {}
  ) => {

    modalState.type = type
    modalState.data = data
    modalState.config = {
      title: getModalTitle(type),
      size: getModalSize(type),
      closable: true,
      persistent: false,
      ...config
    }
    modalState.isOpen = true

    // Use DaisyUI dialog element - wait for Vue to render the modal
    setTimeout(() => {
      const modalId = `modal-${type}`
      const dialogElement = document.getElementById(modalId) as HTMLDialogElement
      if (dialogElement) {
        dialogElement.showModal()
      } else {
        console.error('Vue Modal System: Modal element not found', modalId)
      }
    }, 0)
  }

  const closeModal = () => {
    const currentType = modalState.type
    const shouldReload = modalState.data.shouldReloadAfterClose

    modalState.isOpen = false
    modalState.type = null
    modalState.data = {}

    // Close DaisyUI dialog element
    if (currentType) {
      const modalId = `modal-${currentType}`
      const dialogElement = document.getElementById(modalId) as HTMLDialogElement
      if (dialogElement) {
        dialogElement.close()
      }
    }

    // Check if we should reload after closing
    if (shouldReload) {
      setTimeout(() => {
        window.location.reload()
      }, 300)
    }
  }

  const getModalTitle = (type: string): string => {
    const titles: Record<string, string> = {
      'create-domain': 'Create domain',
      'create-webhook': 'Create webhook',
      'create-alias': 'Create alias',
      'domain-verification': 'Domain verification',
      'webhook-verification': 'Webhook verification',
      'webhook-test': 'Test webhook',
      'edit-domain': 'Edit domain',
      'edit-webhook': 'Edit webhook',
      'edit-alias': 'Edit alias',
      'payload-viewer': 'Webhook Payload'
    }
    return titles[type] || 'Modal'
  }

  const getModalSize = (type: string): ModalConfig['size'] => {
    const sizes: Record<string, ModalConfig['size']> = {
      'create-domain': 'md',
      'create-webhook': 'md',
      'create-alias': 'md',
      'domain-verification': 'lg',
      'webhook-verification': 'md',
      'webhook-test': 'lg',
      'edit-domain': 'md',
      'edit-webhook': 'md',
      'edit-alias': 'md',
      'payload-viewer': 'xl'
    }
    return sizes[type] || 'md'
  }

  return {
    modalState: readonly(modalState),
    openModal,
    closeModal
  }
}

// Global modal functions for backward compatibility with existing code
declare global {
  interface Window {
    openModal: (type: string, data?: ModalData) => void
    closeModal: () => void
  }
}

// Global modal functions will be initialized by App.vue
// This avoids conflicts with read-only properties