import { ref } from 'vue'

interface RefreshOptions {
  delay?: number
  preserveTab?: boolean
}

export function usePageRefresh() {
  const isRefreshing = ref(false)

  const refreshPage = (options: RefreshOptions = {}) => {
    const { delay = 1000, preserveTab = true } = options
    
    isRefreshing.value = true
    
    setTimeout(() => {
      if (preserveTab) {
        // Preserve current tab when refreshing
        const currentUrl = new URL(window.location.href)
        const currentTab = currentUrl.searchParams.get('tab') || 'domains'
        
        // Reload with current tab preserved
        window.location.href = `/dashboard?tab=${currentTab}`
      } else {
        // Simple page reload
        window.location.reload()
      }
    }, delay)
  }

  const refreshWithSuccess = (message: string, options: RefreshOptions = {}) => {
    // Show success message if toast system is available
    if ((window as any).toast?.success) {
      (window as any).toast.success(message)
    }
    
    // Refresh after showing message
    refreshPage(options)
  }

  return {
    isRefreshing,
    refreshPage,
    refreshWithSuccess
  }
}

// Global refresh function for backward compatibility
export const globalRefresh = {
  afterDomainCreated: () => {
    const { refreshWithSuccess } = usePageRefresh()
    refreshWithSuccess('Domain created successfully! Redirecting...', { delay: 1500 })
  },
  
  afterWebhookCreated: () => {
    const { refreshWithSuccess } = usePageRefresh()
    refreshWithSuccess('Webhook created successfully! Redirecting...', { delay: 1500 })
  },
  
  afterAliasCreated: () => {
    const { refreshWithSuccess } = usePageRefresh()
    refreshWithSuccess('Alias created successfully! Redirecting...', { delay: 1500 })
  },

  afterVerification: () => {
    const { refreshWithSuccess } = usePageRefresh()
    refreshWithSuccess('Verification completed! Refreshing...', { delay: 2000 })
  }
}

// Make refresh functions available globally
if (typeof window !== 'undefined') {
  (window as any).vueRefresh = globalRefresh
}
