import { ref, computed, watch } from 'vue'
import { useMetrics } from './useMetrics'

export interface OnboardingStep {
  id: string
  title: string
  description: string
  completed: boolean
  optional?: boolean
}

export interface OnboardingPath {
  id: 'instant' | 'real'
  title: string
  description: string
  icon: string
  steps: OnboardingStep[]
  recommended?: boolean
}

// Global onboarding state
const selectedPath = ref<'instant' | 'real' | null>(null)
const completedSteps = ref<Set<string>>(new Set())
const onboardingDismissed = ref(false)

// Load from localStorage with user-specific key and expiration check
const loadOnboardingState = (userId?: string) => {
  try {
    const storageKey = userId ? `onboarding_state_${userId}` : 'onboarding_state'
    const saved = localStorage.getItem(storageKey)
    if (saved) {
      const state = JSON.parse(saved)

      // Check if state has expired (24 hours)
      const now = Date.now()
      const savedTime = state.timestamp || 0
      const twentyFourHours = 24 * 60 * 60 * 1000

      if (now - savedTime > twentyFourHours) {
        // State has expired, remove it
        localStorage.removeItem(storageKey)
        return
      }

      selectedPath.value = state.selectedPath || null
      completedSteps.value = new Set(state.completedSteps || [])
      onboardingDismissed.value = state.dismissed || false
    }
  } catch (error) {
    console.warn('Failed to load onboarding state:', error)
  }
}

// Save to localStorage with user-specific key and timestamp
const saveOnboardingState = (userId?: string) => {
  try {
    const storageKey = userId ? `onboarding_state_${userId}` : 'onboarding_state'
    const state = {
      selectedPath: selectedPath.value,
      completedSteps: Array.from(completedSteps.value),
      dismissed: onboardingDismissed.value,
      timestamp: Date.now() // Add timestamp for expiration
    }
    localStorage.setItem(storageKey, JSON.stringify(state))
  } catch (error) {
    console.warn('Failed to save onboarding state:', error)
  }
}

export function useOnboarding() {
  const { counts, metricsData } = useMetrics()

  // Get user ID from metrics data
  const userId = computed(() => metricsData.value?.user?.id)

  // Initialize state when user ID is available
  watch(userId, (newUserId) => {
    if (newUserId && selectedPath.value === null && completedSteps.value.size === 0) {
      loadOnboardingState(newUserId)
    }
  }, { immediate: true })

  // Watch for changes and save with user ID
  watch([selectedPath, completedSteps, onboardingDismissed, userId], () => {
    if (userId.value) {
      saveOnboardingState(userId.value)
    }
  }, { deep: true })

  // Define onboarding paths
  const instantPath = computed<OnboardingPath>(() => ({
    id: 'instant',
    title: 'Try it instantly',
    description: 'See how it works with zero setup',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z',
    recommended: true,
    steps: [
      {
        id: 'copy-test-email',
        title: 'Copy your test email address',
        description: 'Use your personal test address to send emails',
        completed: completedSteps.value.has('copy-test-email')
      },
      {
        id: 'send-test-email',
        title: 'Send an email',
        description: 'Send any email to your test address',
        completed: completedSteps.value.has('send-test-email')
      },
      {
        id: 'view-webhook-data',
        title: 'See your webhook in action',
        description: 'View the email in logs and see the JSON payload',
        completed: completedSteps.value.has('view-webhook-data')
      }
    ]
  }))

  const realSetupPath = computed<OnboardingPath>(() => ({
    id: 'real',
    title: 'Jump straight in',
    description: 'Ready to use your own domain?',
    icon: 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9',
    steps: [] // Simple guidance, no complex steps
  }))

  // Computed properties
  const paths = computed(() => [instantPath.value, realSetupPath.value])
  
  const currentPath = computed(() => {
    if (!selectedPath.value) return null
    return paths.value.find(p => p.id === selectedPath.value) || null
  })

  const isNewUser = computed(() => {
    return counts.value.domains === 0 && 
           counts.value.aliases === 0 && 
           counts.value.webhooks === 0
  })

  const shouldShowOnboarding = computed(() => {
    return isNewUser.value && !onboardingDismissed.value
  })

  const instantPathProgress = computed(() => {
    const steps = instantPath.value.steps
    const completed = steps.filter(s => s.completed).length
    return { completed, total: steps.length, percentage: Math.round((completed / steps.length) * 100) }
  })

  const realSetupProgress = computed(() => {
    const steps = realSetupPath.value.steps
    const completed = steps.filter(s => s.completed).length
    return { completed, total: steps.length, percentage: Math.round((completed / steps.length) * 100) }
  })

  const overallProgress = computed(() => {
    if (!currentPath.value) return { completed: 0, total: 0, percentage: 0 }
    const steps = currentPath.value.steps
    const completed = steps.filter(s => s.completed).length
    return { completed, total: steps.length, percentage: Math.round((completed / steps.length) * 100) }
  })

  // Methods
  const selectPath = (pathId: 'instant' | 'real') => {
    selectedPath.value = pathId
  }

  const completeStep = (stepId: string) => {
    completedSteps.value.add(stepId)
  }

  const uncompleteStep = (stepId: string) => {
    completedSteps.value.delete(stepId)
  }

  const dismissOnboarding = () => {
    onboardingDismissed.value = true
  }

  const resetOnboarding = () => {
    selectedPath.value = null
    completedSteps.value.clear()
    onboardingDismissed.value = false

    // Remove both user-specific and legacy storage
    if (userId.value) {
      localStorage.removeItem(`onboarding_state_${userId.value}`)
    }
    localStorage.removeItem('onboarding_state')
    // Clean up any old return context from previous complex implementation
    localStorage.removeItem('onboarding-return-context')
  }

  const getNextStep = () => {
    if (!currentPath.value) return null
    return currentPath.value.steps.find(step => !step.completed) || null
  }

  const isStepCompleted = (stepId: string) => {
    return completedSteps.value.has(stepId)
  }

  // Auto-detect step completion based on metrics (simplified)
  watch(counts, () => {
    // Only auto-complete instant path steps based on actual data
    // No complex webhook management needed
  }, { immediate: true })

  return {
    // State
    selectedPath,
    completedSteps,
    onboardingDismissed,
    
    // Computed
    paths,
    currentPath,
    isNewUser,
    shouldShowOnboarding,
    instantPathProgress,
    realSetupProgress,
    overallProgress,
    
    // Methods
    selectPath,
    completeStep,
    uncompleteStep,
    dismissOnboarding,
    resetOnboarding,
    getNextStep,
    isStepCompleted
  }
}
