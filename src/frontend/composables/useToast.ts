import { ref, reactive, readonly } from 'vue'

export interface ToastItem {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  duration: number
  closable: boolean
  timer?: number
}

interface ToastOptions {
  duration?: number
  closable?: boolean
}

// Global toast state
const toasts = ref<ToastItem[]>([])
let toastIdCounter = 0

export function useToast() {
  const generateId = (): string => {
    return `toast-${++toastIdCounter}-${Date.now()}`
  }

  const addToast = (
    type: ToastItem['type'],
    message: string,
    options: ToastOptions = {}
  ): string => {
    const id = generateId()
    const duration = options.duration ?? getDefaultDuration(type)
    const closable = options.closable ?? true

    const toast: ToastItem = {
      id,
      type,
      message,
      duration,
      closable
    }

    toasts.value.push(toast)

    // Auto-remove after duration (if duration > 0)
    if (duration > 0) {
      toast.timer = window.setTimeout(() => {
        removeToast(id)
      }, duration)
    }

    return id
  }

  const removeToast = (id: string): void => {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      const toast = toasts.value[index]
      
      // Clear timer if exists
      if (toast.timer) {
        clearTimeout(toast.timer)
      }
      
      toasts.value.splice(index, 1)
    }
  }

  const clearAllToasts = (): void => {
    // Clear all timers
    toasts.value.forEach(toast => {
      if (toast.timer) {
        clearTimeout(toast.timer)
      }
    })
    
    toasts.value.length = 0
  }

  const getDefaultDuration = (type: ToastItem['type']): number => {
    switch (type) {
      case 'success':
        return 4000
      case 'info':
        return 4000
      case 'warning':
        return 5000
      case 'error':
        return 6000
      default:
        return 4000
    }
  }

  // Convenience methods
  const success = (message: string, options?: ToastOptions): string => {
    return addToast('success', message, options)
  }

  const error = (message: string, options?: ToastOptions): string => {
    return addToast('error', message, options)
  }

  const warning = (message: string, options?: ToastOptions): string => {
    return addToast('warning', message, options)
  }

  const info = (message: string, options?: ToastOptions): string => {
    return addToast('info', message, options)
  }

  return {
    toasts: readonly(toasts),
    addToast,
    removeToast,
    clearAllToasts,
    success,
    error,
    warning,
    info
  }
}

// Global toast functions for backward compatibility
declare global {
  interface Window {
    toast: {
      success: (message: string, duration?: number) => string
      error: (message: string, duration?: number) => string
      warning: (message: string, duration?: number) => string
      info: (message: string, duration?: number) => string
      show: (message: string, type: ToastItem['type'], duration?: number) => string
      clearAll: () => void
    }
  }
}

// Initialize global toast functions
const { success, error, warning, info, addToast, clearAllToasts } = useToast()

window.toast = {
  success: (message: string, duration?: number) => success(message, { duration }),
  error: (message: string, duration?: number) => error(message, { duration }),
  warning: (message: string, duration?: number) => warning(message, { duration }),
  info: (message: string, duration?: number) => info(message, { duration }),
  show: (message: string, type: ToastItem['type'], duration?: number) => addToast(type, message, { duration }),
  clearAll: clearAllToasts
}
