import { ref, computed, type Ref, type ComputedRef } from 'vue'

export type SortDirection = 'asc' | 'desc'

export function useTableSort<T extends Record<string, any>>(
  data: Ref<T[]> | ComputedRef<T[]> | (() => T[]),
  initialSortColumn: string = '',
  initialSortDirection: SortDirection = 'asc'
) {
  const sortColumn = ref<string>(initialSortColumn)
  const sortDirection = ref<SortDirection>(initialSortDirection)

  const sortedData = computed(() => {
    const dataArray = typeof data === 'function' ? data() : data.value
    
    if (!sortColumn.value || !dataArray.length) {
      return dataArray
    }

    return [...dataArray].sort((a, b) => {
      const aVal = a[sortColumn.value]
      const bVal = b[sortColumn.value]
      
      let comparison = 0
      
      // Handle different data types
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        comparison = aVal.localeCompare(bVal)
      } else if (typeof aVal === 'number' && typeof bVal === 'number') {
        comparison = aVal - bVal
      } else if (aVal instanceof Date && bVal instanceof Date) {
        comparison = aVal.getTime() - bVal.getTime()
      } else {
        // Convert to strings for comparison
        const aStr = String(aVal || '').toLowerCase()
        const bStr = String(bVal || '').toLowerCase()
        comparison = aStr.localeCompare(bStr)
      }
      
      return sortDirection.value === 'desc' ? -comparison : comparison
    })
  })

  const handleSort = (column: string) => {
    if (sortColumn.value === column) {
      // Toggle direction if same column
      sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
    } else {
      // New column, start with ascending
      sortColumn.value = column
      sortDirection.value = 'asc'
    }
  }

  const setSortColumn = (column: string, direction: SortDirection = 'asc') => {
    sortColumn.value = column
    sortDirection.value = direction
  }

  const resetSort = () => {
    sortColumn.value = ''
    sortDirection.value = 'asc'
  }

  return {
    sortedData,
    sortColumn,
    sortDirection,
    handleSort,
    setSortColumn,
    resetSort
  }
}
