import { ref, reactive } from 'vue'
import { useToast } from './useToast'

interface RefreshState {
  domains: boolean
  webhooks: boolean
  aliases: boolean
}

// Global reactive state for data refresh triggers
const refreshState = reactive<RefreshState>({
  domains: false,
  webhooks: false,
  aliases: false
})

// Global data stores
const globalData = reactive({
  domains: [] as any[],
  webhooks: [] as any[],
  aliases: [] as any[]
})

export function useDataRefresh() {
  const { success: showSuccessToast } = useToast()
  const isRefreshing = ref(false)

  // Trigger refresh for specific data type
  const triggerRefresh = (type: keyof RefreshState) => {
    refreshState[type] = !refreshState[type]
  }

  // Refresh all data types
  const refreshAll = () => {
    Object.keys(refreshState).forEach(key => {
      refreshState[key as keyof RefreshState] = !refreshState[key as keyof RefreshState]
    })
  }

  // Update data in global store
  const updateData = (type: keyof RefreshState, data: any[]) => {
    globalData[type] = data
  }

  // Add item to data store
  const addItem = (type: keyof RefreshState, item: any) => {
    globalData[type].push(item)
    triggerRefresh(type)
  }

  // Update item in data store
  const updateItem = (type: keyof RefreshState, itemId: string, updates: any) => {
    const index = globalData[type].findIndex(item => item.id === itemId)
    if (index !== -1) {
      globalData[type][index] = { ...globalData[type][index], ...updates }
      triggerRefresh(type)
    }
  }

  // Remove item from data store
  const removeItem = (type: keyof RefreshState, itemId: string) => {
    const index = globalData[type].findIndex(item => item.id === itemId)
    if (index !== -1) {
      globalData[type].splice(index, 1)
      triggerRefresh(type)
    }
  }

  // Show success message and trigger refresh
  const refreshWithSuccess = (message: string, type?: keyof RefreshState) => {
    showSuccessToast(message)

    if (type) {
      triggerRefresh(type)
    } else {
      refreshAll()
    }
  }

  // Optimistic update - update UI immediately, then sync with server
  const optimisticUpdate = async (
    type: keyof RefreshState,
    operation: 'add' | 'update' | 'remove',
    item: any,
    serverOperation: () => Promise<any>
  ) => {
    try {
      // Apply optimistic update
      switch (operation) {
        case 'add':
          addItem(type, item)
          break
        case 'update':
          updateItem(type, item.id, item)
          break
        case 'remove':
          removeItem(type, item.id)
          break
      }

      // Perform server operation
      const result = await serverOperation()
      
      // If server operation succeeds, we're good
      // If it fails, we'll revert in the catch block
      return result
    } catch (error) {
      // Revert optimistic update on error
      triggerRefresh(type)
      throw error
    }
  }

  return {
    refreshState,
    globalData,
    isRefreshing,
    triggerRefresh,
    refreshAll,
    updateData,
    addItem,
    updateItem,
    removeItem,
    refreshWithSuccess,
    optimisticUpdate
  }
}

// Global refresh functions for backward compatibility
export const globalRefresh = {
  afterDomainCreated: (domain?: any) => {
    const { refreshWithSuccess, addItem } = useDataRefresh()
    if (domain) {
      addItem('domains', domain)
    }
    refreshWithSuccess('Domain created successfully!', 'domains')
  },
  
  afterWebhookCreated: (webhook?: any) => {
    const { refreshWithSuccess, addItem } = useDataRefresh()
    if (webhook) {
      addItem('webhooks', webhook)
    }
    refreshWithSuccess('Webhook created successfully!', 'webhooks')
  },
  
  afterAliasCreated: (alias?: any) => {
    const { refreshWithSuccess, addItem } = useDataRefresh()
    if (alias) {
      addItem('aliases', alias)
    }
    refreshWithSuccess('Alias created successfully!', 'aliases')
  },

  afterVerification: (type: 'domain' | 'webhook', itemId?: string) => {
    const { refreshWithSuccess, updateItem } = useDataRefresh()
    
    if (type === 'domain' && itemId) {
      updateItem('domains', itemId, { verificationStatus: 'VERIFIED' })
    } else if (type === 'webhook' && itemId) {
      updateItem('webhooks', itemId, { verified: true })
    }
    
    refreshWithSuccess('Verification completed!', type === 'domain' ? 'domains' : 'webhooks')
  }
}

// Make refresh functions available globally
if (typeof window !== 'undefined') {
  (window as any).vueRefresh = globalRefresh
}
