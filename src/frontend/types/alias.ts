export interface Alias {
  id: string
  email: string
  userId: string
  domainId: string
  webhookId: string
  active: boolean
  createdAt: string
  updatedAt: string
  domain?: Domain
  webhook?: Webhook
}

export interface CreateAliasRequest {
  email: string
  domainId: string
  webhookId: string
  active?: boolean
}

export interface UpdateAliasRequest {
  active?: boolean
  webhookId?: string
}

// Re-export related types
import type { Domain } from './domain'
import type { Webhook } from './webhook'