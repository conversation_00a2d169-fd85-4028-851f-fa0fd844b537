<script setup lang="ts">
import Logo from '@/components/ui/Logo.vue'
</script>

<template>
  <div class="min-h-screen flex flex-col bg-gradient-to-br from-primary/10 via-base-200 to-secondary/10">
    <!-- Navigation -->
    <nav class="bg-base-100/80 backdrop-blur-sm shadow-sm border-b border-base-300/50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <Logo />
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1 flex items-center justify-center py-8">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-base-100/80 backdrop-blur-sm border-t border-base-300/50">
      <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        <p class="text-center text-sm text-base-content/70">Mail2Webhook.eu Service</p>
      </div>
    </footer>
  </div>
</template>

<!-- Auth layout component - clean layout for login/register pages -->

<style scoped>
/* Auth layout specific styles */
</style>
