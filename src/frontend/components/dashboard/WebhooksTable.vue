<script setup lang="ts">
import { computed } from 'vue'
import DataTable from '@components/ui/DataTable.vue'
import type { TableColumn } from '@components/ui/DataTable.vue'
import type { Webhook } from '@types'
import { formatWebhookUrl } from '../../utils/url'

interface Props {
  webhooks: Webhook[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  verifyWebhook: [webhookId: string]
  testWebhook: [webhookId: string]
  editWebhook: [webhook: Webhook]
  deleteWebhook: [webhookId: string, webhookName: string]
}>()

const columns: TableColumn<Webhook>[] = [
  {
    key: 'name',
    label: 'Name',
    sortable: true,
    render: (value) => {
      return `
        <div class="tooltip" data-tip="${value}">
          <span class="text-sm font-medium text-base-content truncate block max-w-[150px]">${value}</span>
        </div>
      `
    }
  },
  {
    key: 'verified',
    label: 'Status',
    sortable: true,
    render: (value, row) => {
      if (value) {
        return `
          <div class="flex items-center gap-2">
            <div class="status status-success"></div>
            <span class="text-success">Verified</span>
          </div>
        `
      } else {
        return `
          <button onclick="window.openModal('webhook-verification', { webhookId: '${row.id}', webhookUrl: '${row.url}' })"
                  class="flex items-center gap-2 cursor-pointer hover:opacity-80">
            <div class="status status-info animate-bounce"></div>
            <span class="underline text-info">Verify now</span>
          </button>
        `
      }
    }
  },
  {
    key: 'url',
    label: 'URL',
    sortable: true,
    render: (value) => {
      const displayUrl = formatWebhookUrl(value, 40)

      return `
        <div class="tooltip" data-tip="${value}">
          <span class="text-sm text-base-content">${displayUrl}</span>
        </div>
      `
    }
  },
  {
    key: 'description',
    label: 'Description',
    render: (value) => {
      if (!value) return '<span class="text-base-content/60">No description</span>'

      return `
        <div class="tooltip" data-tip="${value}">
          <div class="text-sm text-base-content/80 truncate max-w-[150px]">${value}</div>
        </div>
      `
    }
  },
  {
    key: 'actions',
    label: '',
    width: '180px',
    render: (value, row) => {
      const isInUse = (row.domainCount || 0) > 0 || (row.aliasCount || 0) > 0
      const buttonClass = isInUse
        ? 'btn btn-disabled btn-xs'
        : 'btn btn-outline btn-error btn-xs'

      const deleteAttrs = isInUse ? '' : `data-action="deleteWebhook" data-webhook-id="${row.id}" data-webhook-name="${row.name}"`
      const tooltip = isInUse ? `title="Cannot delete webhook in use by ${row.domainCount || 0} domain(s) and ${row.aliasCount || 0} alias(es)"` : ''

      const testButton = `<button type="button"
                   class="btn btn-outline btn-info btn-xs ${!row.verified ? 'btn-disabled' : ''}"
                   data-action="testWebhook"
                   data-webhook-id="${row.id}"
                   data-webhook-url="${row.url}"
                   data-webhook-name="${row.name}"
                   ${!row.verified ? 'disabled title="Verify webhook first to enable testing"' : ''}>
             <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
             </svg>
             Test
           </button>`

      return `
        <div class="flex items-center justify-end space-x-1">
          <button type="button"
                  class="btn btn-outline btn-secondary btn-xs"
                  data-action="editWebhook"
                  data-webhook-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </button>
          ${testButton}
          <button type="button"
                  class="${buttonClass}"
                  ${deleteAttrs}
                  ${tooltip}
                  ${isInUse ? 'disabled' : ''}>
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      `
    }
  }
]

const handleRowClick = (webhook: Webhook, index: number) => {
  // Handle row click if needed
}

const handleActionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const button = target.closest('button[data-action]') as HTMLButtonElement

  if (!button) return

  const action = button.dataset.action
  const webhookId = button.dataset.webhookId
  const webhookName = button.dataset.webhookName

  if (action === 'editWebhook' && webhookId) {
    const webhook = props.webhooks.find(w => w.id === webhookId)
    if (webhook) {
      emit('editWebhook', webhook)
    }
  } else if (action === 'verifyWebhook' && webhookId) {
    emit('verifyWebhook', webhookId)
  } else if (action === 'testWebhook' && webhookId) {
    // Only emit if webhook is verified (defensive check)
    const webhook = props.webhooks.find(w => w.id === webhookId)
    if (webhook?.verified) {
      const webhookUrl = button.dataset.webhookUrl
      const webhookName = button.dataset.webhookName
      // Open the custom test modal instead of direct API call
      ;(window as any).openModal('webhook-test', {
        webhookId,
        webhookUrl,
        webhookName
      })
    }
  } else if (action === 'deleteWebhook' && webhookId && webhookName) {
    emit('deleteWebhook', webhookId, webhookName)
  }
}
</script>

<template>
  <div class="bg-base-100 border border-base-300 rounded-lg shadow-sm" @click="handleActionClick">
    <DataTable
      :columns="columns"
      :data="webhooks"
      :loading="loading"
      empty-message="No webhooks yet. Create your first webhook to receive email notifications!"
      @row-click="handleRowClick"
    />
  </div>
</template>
