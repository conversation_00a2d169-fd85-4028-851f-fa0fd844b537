<template>
  <div class="min-h-screen flex items-center justify-center bg-base-100">
    <div class="text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-primary">404</h1>
        <div class="text-6xl font-bold text-base-content/20 -mt-4">
          Page Not Found
        </div>
      </div>
      
      <div class="max-w-md mx-auto mb-8">
        <h2 class="text-2xl font-semibold text-base-content mb-4">
          Oops! This page doesn't exist
        </h2>
        <p class="text-base-content/70 mb-6">
          The page you're looking for might have been moved, deleted, or you entered the wrong URL.
        </p>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <router-link 
          to="/" 
          class="btn btn-primary"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          Go Home
        </router-link>
        
        <button 
          @click="goBack" 
          class="btn btn-outline"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Go Back
        </button>
      </div>

      <div class="mt-12 text-base-content/50">
        <p class="text-sm">
          If you believe this is an error, please 
          <a href="mailto:<EMAIL>" class="link link-primary">contact support</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>
