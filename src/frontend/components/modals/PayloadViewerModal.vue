<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Props {
  payloadData: {
    logId: string
    payload: any
    timestamp?: string
    alias?: string
    domain?: string
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// State
const copySuccess = ref(false)
const expandedSections = ref<Set<string>>(new Set(['message', 'envelope']))

// Computed
const formattedPayload = computed(() => {
  try {
    return JSON.stringify(props.payloadData.payload, null, 2)
  } catch (error) {
    return 'Invalid JSON payload'
  }
})

const payloadSize = computed(() => {
  const bytes = new Blob([formattedPayload.value]).size
  if (bytes < 1024) return `${bytes} bytes`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
})

// Methods
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedPayload.value)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

const toggleSection = (section: string) => {
  if (expandedSections.value.has(section)) {
    expandedSections.value.delete(section)
  } else {
    expandedSections.value.add(section)
  }
}

const expandAll = () => {
  const sections = ['message', 'envelope', 'spam', 'attachments']
  sections.forEach(section => {
    if (props.payloadData.payload[section]) {
      expandedSections.value.add(section)
    }
  })
}

const collapseAll = () => {
  expandedSections.value.clear()
}

const renderJsonSection = (obj: any, path: string = '', level: number = 0): string => {
  if (obj === null) return '<span class="text-gray-500">null</span>'
  if (obj === undefined) return '<span class="text-gray-500">undefined</span>'
  
  const indent = '  '.repeat(level)
  
  if (typeof obj === 'string') {
    return `<span class="text-green-600">"${obj}"</span>`
  }
  
  if (typeof obj === 'number') {
    return `<span class="text-blue-600">${obj}</span>`
  }
  
  if (typeof obj === 'boolean') {
    return `<span class="text-purple-600">${obj}</span>`
  }
  
  if (Array.isArray(obj)) {
    if (obj.length === 0) return '[]'
    const items = obj.map((item, index) => 
      `${indent}  ${renderJsonSection(item, `${path}[${index}]`, level + 1)}`
    ).join(',\n')
    return `[\n${items}\n${indent}]`
  }
  
  if (typeof obj === 'object') {
    const keys = Object.keys(obj)
    if (keys.length === 0) return '{}'
    
    const items = keys.map(key => {
      const value = renderJsonSection(obj[key], `${path}.${key}`, level + 1)
      return `${indent}  <span class="text-red-600">"${key}"</span>: ${value}`
    }).join(',\n')
    
    return `{\n${items}\n${indent}}`
  }
  
  return String(obj)
}

onMounted(() => {
  // Auto-expand main sections by default
  if (props.payloadData.payload.message) expandedSections.value.add('message')
  if (props.payloadData.payload.envelope) expandedSections.value.add('envelope')
})
</script>

<template>
  <div class="space-y-4">
    <!-- Header Info -->
    <div class="bg-base-200 rounded-lg p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <span class="font-medium text-base-content/70">Log ID:</span>
          <span class="ml-2 font-mono text-xs">{{ payloadData.logId }}</span>
        </div>
        <div v-if="payloadData.timestamp">
          <span class="font-medium text-base-content/70">Timestamp:</span>
          <span class="ml-2">{{ new Date(payloadData.timestamp).toLocaleString() }}</span>
        </div>
        <div v-if="payloadData.alias">
          <span class="font-medium text-base-content/70">Alias:</span>
          <span class="ml-2">{{ payloadData.alias }}</span>
        </div>
        <div v-if="payloadData.domain">
          <span class="font-medium text-base-content/70">Domain:</span>
          <span class="ml-2">{{ payloadData.domain }}</span>
        </div>
      </div>
    </div>

    <!-- Controls -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <button 
          type="button"
          @click="expandAll"
          class="btn btn-outline btn-xs"
        >
          Expand All
        </button>
        <button 
          type="button"
          @click="collapseAll"
          class="btn btn-outline btn-xs"
        >
          Collapse All
        </button>
      </div>
      
      <div class="flex items-center gap-2">
        <span class="text-xs text-base-content/60">{{ payloadSize }}</span>
        <button 
          type="button"
          @click="copyToClipboard"
          class="btn btn-outline btn-xs"
          :class="{ 'btn-success': copySuccess }"
        >
          <svg v-if="!copySuccess" class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <svg v-else class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ copySuccess ? 'Copied!' : 'Copy' }}
        </button>
      </div>
    </div>

    <!-- JSON Viewer -->
    <div class="bg-base-100 border border-base-300 rounded-lg overflow-hidden">
      <div class="max-h-96 overflow-y-auto">
        <pre class="p-4 text-sm font-mono leading-relaxed whitespace-pre-wrap break-words"><code v-html="renderJsonSection(payloadData.payload)"></code></pre>
      </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-end gap-2 pt-4">
      <button 
        type="button"
        @click="emit('close')"
        class="btn btn-primary"
      >
        Close
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for JSON viewer */
.max-h-96::-webkit-scrollbar {
  width: 8px;
}

.max-h-96::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

.max-h-96::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 4px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.3);
}
</style>
