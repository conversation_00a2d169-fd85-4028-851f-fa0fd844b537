<script setup lang="ts">
import { useDataRefresh } from '@composables/useDataRefresh'
import WebhookVerificationSteps from '@/components/shared/WebhookVerificationSteps.vue'
import type { WebhookVerificationData } from '@types'

interface Props {
  webhookData: WebhookVerificationData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// Methods
const handleVerified = () => {
  // Update the specific webhook item to verified status and trigger refresh
  const { updateItem, triggerRefresh } = useDataRefresh()
  updateItem('webhooks', props.webhookData.webhookId, { verified: true })

  // Force a refresh to ensure all tables update (webhooks, aliases, domains)
  triggerRefresh('webhooks')
  triggerRefresh('aliases')
  triggerRefresh('domains')

  emit('close')
}

const handleError = (message: string) => {
  console.error('Webhook verification error:', message)
}
</script>

<template>
  <div class="space-y-6">
    <!-- Use shared webhook verification component -->
    <WebhookVerificationSteps
      :webhook-id="webhookData.webhookId"
      :webhook-url="webhookData.webhookUrl"
      :webhook-name="webhookData.webhookName"
      @verified="handleVerified"
      @error="handleError"
    />

    <!-- Simplified modal footer - only close button since Complete Verification handles success -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('close')"
        class="btn btn-ghost"
      >
        Close
      </button>
    </div>
  </div>
</template>