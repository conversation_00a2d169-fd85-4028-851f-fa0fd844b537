<template>
  <div id="pricing" class="bg-base-200 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="badge badge-primary mb-4">Pricing</div>
        <h2 class="text-3xl lg:text-4xl font-bold text-base-content mb-4">
          Simple, transparent pricing
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Start free and scale as you grow. No hidden fees, no setup costs.
        </p>
      </div>
      
      <!-- Pricing Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16 max-w-4xl mx-auto">
        <!-- Starter Plan -->
        <div class="card bg-base-100 shadow-lg h-full">
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h3 class="card-title text-2xl justify-center mb-2">Starter</h3>
              <p class="text-base-content/70 mb-6">Perfect for small projects and testing</p>
              <div class="mb-6">
                <span class="text-4xl font-bold">€0</span>
                <span class="text-base-content/70">/month</span>
              </div>
            </div>
            
            <div class="space-y-3 mb-6 flex-grow">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ freePlan.monthlyEmailLimit || 50 }} emails/month</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ freePlan.domains || 1 }} domain + {{ freePlan.aliases || 3 }} aliases and {{ freePlan.webhooks || 3 }} webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>In-line attachments (&lt;128Kb)</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Additional credit purchases at &euro; 1.00/100 emails</span>
              </div>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <router-link to="/register" class="btn btn-primary btn-block">Get started free</router-link>
            </div>
          </div>
        </div>
        
        <!-- Professional Plan -->
        <div class="card bg-base-100 shadow-xl border-2 border-primary relative h-full">
          <!-- <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div class="badge badge-primary badge-lg">Most Popular</div>
          </div> -->
          <div class="card-body flex flex-col h-full">
            <div class="text-center">
              <h3 class="card-title text-2xl justify-center mb-2">Pro</h3>
              <p class="text-base-content/70 mb-6">For growing businesses and applications</p>
              <div class="mb-6">
                <span class="text-4xl font-bold">€{{ proPlan.price?.monthly?.toFixed(2) || '9.95' }}</span>
                <span class="text-base-content/70">/month</span>
              </div>
            </div>
            
            <div class="space-y-3 mb-6 flex-grow">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ proPlan.monthlyEmailLimit?.toLocaleString() || '1,000' }} emails/month</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>{{ proPlan.domains || 5 }} domains + {{ proPlan.aliases || 10 }} aliases and {{ proPlan.webhooks || 10 }} webhooks per domain</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Custom headers in webhooks</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>S3-compatible attachment storage</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>Discounted credit purchases at &euro; 0.80/100 emails</span>
              </div>
            </div>
            
            <div class="card-actions justify-center mt-auto">
              <router-link to="/register" class="btn btn-primary btn-block">Start with Pro</router-link>
            </div>
          </div>
        </div>
        

      </div>
      
      <!-- FAQ Section -->
      <div class="bg-base-100 rounded-2xl p-8 lg:p-12">
        <h3 class="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h4 class="font-semibold mb-2">What happens if I exceed my email limit?</h4>
            <p class="text-base-content/70 text-sm">
              Once you hit 80% of your limit, we'll notify you via email. You can purchase additional
              email credits (€1.00 per 100 for Free users, €0.80 per 100 for Pro users) or upgrade your plan.
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-2">Can I change plans anytime?</h4>
            <p class="text-base-content/70 text-sm">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately with prorated billing.
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-2">Is there a setup fee?</h4>
            <p class="text-base-content/70 text-sm">
              No setup fees, no hidden costs. You only pay the monthly subscription fee for your chosen plan.
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-2">What about GDPR compliance?</h4>
            <p class="text-base-content/70 text-sm">
              All data is processed on EU servers with EU-operated infrastructure (no Cloud Act, no Patriot Act). 
              Automatic expiration, audit logging, and all 3rd party providers are EU-based.
            </p>
          </div>
        </div>
      </div>
      
      <!-- Bottom CTA -->
      <div class="text-center mt-12">
        <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-8 text-primary-content">
          <h3 class="text-2xl font-bold mb-4">Ready to get started?</h3>
          <p class="mb-6 opacity-90">
            Join our growing community of developers who trust EmailConnect for their automation workflows.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link to="/register" class="btn btn-accent btn-lg">
              Get started today
            </router-link>
            <a href="/docs" class="btn btn-outline btn-lg text-primary-content border-primary-content hover:bg-primary-content hover:text-primary">
              View API docs
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { usePlanConfig } from '../../composables/usePlanConfig'

// Load plan configuration
const { freePlan, proPlan, loadPlanConfig } = usePlanConfig()

onMounted(async () => {
  await loadPlanConfig()
})
</script>
