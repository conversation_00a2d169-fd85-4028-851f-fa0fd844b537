<template>
  <div class="bg-base-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="badge badge-info mb-4">Integrations</div>
        <p class="text-base text-base-content/60 mb-2">Automation platform integrations</p>
        <h2 class="text-3xl lg:text-4xl font-bold text-base-content mb-4">
          Plug into Zapier, Make, n8n & more
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Send emails directly into your automations. If it supports webhooks, it works.
        </p>
      </div>
      
      <!-- Integration Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
        <!-- n8n -->
        <div class="card bg-base-200 hover:bg-base-300 transition-colors hover:shadow-lg hover:-translate-y-1 transition duration-200">
          <div class="card-body items-center text-center">
            <div class="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <span class="text-2xl font-bold text-primary">n8n</span>
            </div>
            <h3 class="card-title text-lg">n8n workflows</h3>
            <p class="text-sm text-base-content/70">
              Trigger complex automation workflows from incoming emails
            </p>
          </div>
        </div>
        
        <!-- Zapier -->
        <div class="card bg-base-200 hover:bg-base-300 transition-colors hover:shadow-lg hover:-translate-y-1 transition duration-200">
          <div class="card-body items-center text-center">
            <div class="w-16 h-16 bg-secondary/10 rounded-lg flex items-center justify-center mb-4">
              <span class="text-lg font-bold text-secondary">Zapier</span>
            </div>
            <h3 class="card-title text-lg">Zapier zaps</h3>
            <p class="text-sm text-base-content/70">
              Connect to 5,000+ apps with simple webhook integration
            </p>
          </div>
        </div>
        
        <!-- Make -->
        <div class="card bg-base-200 hover:bg-base-300 transition-colors hover:shadow-lg hover:-translate-y-1 transition duration-200">
          <div class="card-body items-center text-center">
            <div class="w-16 h-16 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
              <span class="text-lg font-bold text-accent">Make</span>
            </div>
            <h3 class="card-title text-lg">Make scenarios</h3>
            <p class="text-sm text-base-content/70">
              Build powerful automation scenarios with visual workflows
            </p>
          </div>
        </div>
        
        <!-- Custom -->
        <div class="card bg-base-200 hover:bg-base-300 transition-colors hover:shadow-lg hover:-translate-y-1 transition duration-200">
          <div class="card-body items-center text-center">
            <div class="w-16 h-16 bg-info/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-8 h-8 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
              </svg>
            </div>
            <h3 class="card-title text-lg">Custom webhooks</h3>
            <p class="text-sm text-base-content/70">
              Direct integration with your own applications and services
            </p>
          </div>
        </div>
      </div>
      
      <!-- Example Use Cases -->
      <div class="bg-base-200 rounded-2xl p-8 lg:p-12">
        <h3 class="text-2xl font-bold text-center mb-8">Popular use cases</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <h4 class="font-semibold text-base-content">Support tickets</h4>
            <p class="text-sm text-base-content/70">
              Auto-create <NAME_EMAIL> emails
            </p>
          </div>
          
          <div class="text-center">
            <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM15 17H9a2 2 0 01-2-2V5a2 2 0 012-2h6a2 2 0 012 2v10z"/>
              </svg>
            </div>
            <h4 class="font-semibold text-base-content">Lead processing</h4>
            <p class="text-sm text-base-content/70">
              Route sales@domain to CRM systems automatically
            </p>
          </div>
          
          <div class="text-center">
            <div class="w-12 h-12 bg-error/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg class="w-6 h-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h4 class="font-semibold text-base-content">Scheduling assistant</h4>
            <p class="text-sm text-base-content/70">
              Have assistant@domain emails schedule meeting requests
            </p>
          </div>
        </div>
        
        <div class="text-center mt-8">
          <p class="text-base-content/70 mb-4">
            Unlimited domains and aliases mean infinite possibilities
          </p>
          <router-link to="/register" class="btn btn-primary">
            Start building workflows
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Integration showcase showing popular platforms and use cases
</script>
