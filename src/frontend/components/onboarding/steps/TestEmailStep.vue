<template>
  <div class="space-y-4">
    <!-- Enhanced Test Email Card -->
    <div class="card bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20">
      <div class="card-body p-4">
        <div class="flex items-center space-x-2 mb-3">
          <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <h4 class="font-medium text-primary">Your unique test address</h4>
        </div>
        
        <p class="text-sm text-base-content/80 mb-3">
          💡 Send any email to this address and watch it trigger your webhook in real time!
        </p>
        
        <div class="bg-base-100 rounded-lg p-3 border border-base-300">
          <div class="flex items-center gap-2">
            <input 
              ref="emailInput"
              type="text" 
              :value="testEmail" 
              readonly 
              class="input input-bordered input-sm flex-1 font-mono text-sm bg-base-200 focus:bg-base-100"
              @focus="selectAll"
            >
            <button 
              @click="copyToClipboard"
              class="btn btn-outline btn-sm"
              :class="{ 'btn-success': copied }"
            >
              <svg v-if="!copied" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{ copied ? 'Copied!' : 'Copy' }}
            </button>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="flex flex-wrap gap-2 mt-3">
          <button
            @click="openGmail"
            class="btn btn-outline btn-xs"
          >
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            Open Gmail
          </button>
          <button
            @click="openOutlook"
            class="btn btn-outline btn-xs"
          >
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            Open Outlook
          </button>
          <button
            @click="copyMailtoLink"
            class="btn btn-outline btn-xs"
          >
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy mailto
          </button>
        </div>
      </div>
    </div>

    <!-- Action Button -->
    <div class="text-center">
      <button
        @click="markAsCompleted"
        class="btn btn-primary btn-sm"
        :disabled="!copied"
      >
        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        I've copied the address
      </button>
    </div>

    <!-- Help Text -->
    <div class="text-center">
      <p class="text-xs text-base-content/60">
        💡 Tip: Send from any email client - Gmail, Outlook, Apple Mail, etc.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from '@composables/useToast'

interface Props {
  userId?: string
  step?: any
}

const props = withDefaults(defineProps<Props>(), {
  userId: ''
})

const emit = defineEmits<{
  complete: []
}>()

// State
const copied = ref(false)
const emailInput = ref<HTMLInputElement>()
const { success } = useToast()

// Computed
const testEmail = computed(() => {
  if (!props.userId) return 'Loading...'
  
  // Get last 8 characters of user ID
  const suffix = props.userId.slice(-8)
  return `${suffix}@test.emailconnect.eu`
})

// Methods
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(testEmail.value)
    copied.value = true
    success('Email address copied to clipboard!')
    
    // Reset copied state after 3 seconds
    setTimeout(() => {
      copied.value = false
    }, 3000)
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    // Fallback for older browsers
    selectAll()
    document.execCommand('copy')
    copied.value = true
    success('Email address copied!')
    setTimeout(() => {
      copied.value = false
    }, 3000)
  }
}

const selectAll = () => {
  if (emailInput.value) {
    emailInput.value.select()
  }
}

const openGmail = () => {
  const subject = encodeURIComponent('Test Email for Webhook')
  const body = encodeURIComponent('This is a test email to see my webhook in action!')
  const url = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(testEmail.value)}&su=${subject}&body=${body}`
  window.open(url, '_blank')
}

const openOutlook = () => {
  const subject = encodeURIComponent('Test Email for Webhook')
  const body = encodeURIComponent('This is a test email to see my webhook in action!')
  const url = `https://outlook.live.com/mail/0/deeplink/compose?to=${encodeURIComponent(testEmail.value)}&subject=${subject}&body=${body}`
  window.open(url, '_blank')
}

const copyMailtoLink = async () => {
  const subject = encodeURIComponent('Test Email for Webhook')
  const body = encodeURIComponent('This is a test email to see my webhook in action!')
  const mailtoLink = `mailto:${testEmail.value}?subject=${subject}&body=${body}`
  
  try {
    await navigator.clipboard.writeText(mailtoLink)
    success('Mailto link copied to clipboard!')
  } catch (error) {
    console.error('Failed to copy mailto link:', error)
  }
}

const markAsCompleted = () => {
  emit('complete')
}

onMounted(() => {
  // Auto-focus and select the email input for easy copying
  if (emailInput.value) {
    emailInput.value.focus()
    emailInput.value.select()
  }
})
</script>

<style scoped>
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.input[readonly] {
  cursor: text;
}

.input[readonly]:focus {
  outline: none;
  border-color: var(--fallback-bc, oklch(var(--bc) / 0.2));
}
</style>
