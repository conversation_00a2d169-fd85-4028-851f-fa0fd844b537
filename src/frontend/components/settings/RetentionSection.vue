<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title">
        <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline">Pro</span>
        Data retention
      </h2>
      <p class="mt-1 mb-6 text-base-content/70">
        Configure how long your email data is stored for GDPR compliance.
      </p>

      <!-- Success/Error messages -->
      <div v-if="showSuccessMessage" class="alert alert-success mt-4 rounded-lg">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Retention settings saved successfully!</span>
      </div>

      <div v-if="showErrorMessage" class="alert alert-error mt-4 rounded-lg">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>{{ errorMessage }}</span>
      </div>

      <div class="bg-base-200/40 rounded-lg p-6">
        <!-- Custom retention setting -->
        <div class="space-y-6" :class="{ 'opacity-60': !isProUser || loading }">
          <div>
            <div class="font-semibold text-lg mb-4 flex items-center gap-2">
              Custom retention period
            </div>
            <!-- Use plan default option -->
            <div class="form-control">
              <label class="label cursor-pointer justify-start">
                <input
                  type="radio"
                  name="retention-mode"
                  class="radio radio-primary mr-3"
                  :checked="settings.dataRetentionHours === null"
                  @change="setRetentionMode('default')"
                  :disabled="!isProUser || loading"
                />
                <span class="label-text">Use plan default ({{ planDefaultHours }} hours)</span>
              </label>
            </div>

            <!-- Custom retention option -->
            <div class="form-control">
              <label class="label cursor-pointer justify-start">
                <input
                  type="radio"
                  name="retention-mode"
                  class="radio radio-primary mr-3"
                  :checked="settings.dataRetentionHours !== null"
                  @change="setRetentionMode('custom')"
                  :disabled="!isProUser || loading"
                />
                <span class="label-text">
                  <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline">Pro</span>
                  Custom
                </span>
              </label>
            </div>

            <!-- Custom hours input -->
            <div v-if="settings.dataRetentionHours !== null" class="mt-4 ml-8">
              <div class="flex items-center space-x-4">
                <input
                  type="number"
                  min="1"
                  max="8760"
                  v-model="customRetentionHours"
                  :disabled="!isProUser || loading"
                  class="input input-bordered w-32"
                  placeholder="Hours"
                />
                <span class="text-sm text-base-content/70">hours</span>
              </div>
              <label class="label">
                <span class="label-text-alt text-xs">
                  Range: 1 hour to 8760 hours (1 year).
                </span>
              </label>
            </div>
          </div>

          <!-- Current effective retention -->
          <div class="bg-base-100 border border-base-300 rounded-lg p-4">
            <h4 class="font-medium mb-2">Current effective retention</h4>
            <p class="text-sm text-base-content/70">
              Your email data will be automatically deleted after: 
              <strong>{{ formatRetentionPeriod(effectiveRetentionHours) }}</strong>
            </p>
          </div>

          <!-- Save button -->
          <div class="flex justify-end pt-4">
            <button
              @click="saveSettings"
              :disabled="!isProUser || loading || saving"
              class="btn btn-primary w-full md:w-2/5"
            >
              <span v-if="saving" class="loading loading-spinner loading-sm"></span>
              {{ saving ? 'Saving...' : 'Save retention settings' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useMetrics } from '../../composables/useMetrics'
import { useUserSettings } from '../../composables/useUserSettings'

// Composables
const { metricsData, loadMetrics } = useMetrics()
const {
  settings,
  loading,
  saving,
  loadSettings,
  saveSettings: saveUserSettings
} = useUserSettings()

// Local state
const customRetentionHours = ref<number>(24)
const showSuccessMessage = ref(false)
const showErrorMessage = ref(false)
const errorMessage = ref('')

// Check if user has Pro plan or higher
const isProUser = computed(() => {
  const planType = metricsData.value?.user?.planType || 'free'
  return planType === 'pro' || planType === 'enterprise'
})

// Plan-based default retention hours
const planDefaultHours = computed(() => {
  const planType = metricsData.value?.user?.planType || 'free'
  switch (planType) {
    case 'free':
      return 2
    case 'pro':
    case 'enterprise':
      return 24
    default:
      return 2
  }
})

// Effective retention hours (custom or plan default)
const effectiveRetentionHours = computed(() => {
  return settings.value.dataRetentionHours ?? planDefaultHours.value
})

// Watch for settings changes to update local custom hours
watch(() => settings.value.dataRetentionHours, (newValue) => {
  if (newValue !== null) {
    customRetentionHours.value = newValue
  }
}, { immediate: true })

// Methods
const setRetentionMode = (mode: 'default' | 'custom') => {
  if (mode === 'default') {
    settings.value.dataRetentionHours = null
  } else {
    settings.value.dataRetentionHours = customRetentionHours.value
  }
}

// Watch custom hours and update settings
watch(customRetentionHours, (newValue) => {
  if (settings.value.dataRetentionHours !== null) {
    settings.value.dataRetentionHours = newValue
  }
})

const formatRetentionPeriod = (hours: number): string => {
  if (hours < 24) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`
  } else if (hours < 168) { // Less than a week
    const days = Math.floor(hours / 24)
    const remainingHours = hours % 24
    if (remainingHours === 0) {
      return `${days} day${days !== 1 ? 's' : ''}`
    } else {
      return `${days} day${days !== 1 ? 's' : ''} and ${remainingHours} hour${remainingHours !== 1 ? 's' : ''}`
    }
  } else if (hours < 720) { // Less than a month
    const weeks = Math.floor(hours / 168)
    const remainingDays = Math.floor((hours % 168) / 24)
    if (remainingDays === 0) {
      return `${weeks} week${weeks !== 1 ? 's' : ''}`
    } else {
      return `${weeks} week${weeks !== 1 ? 's' : ''} and ${remainingDays} day${remainingDays !== 1 ? 's' : ''}`
    }
  } else {
    const months = Math.floor(hours / 720)
    const remainingDays = Math.floor((hours % 720) / 24)
    if (remainingDays === 0) {
      return `${months} month${months !== 1 ? 's' : ''}`
    } else {
      return `${months} month${months !== 1 ? 's' : ''} and ${remainingDays} day${remainingDays !== 1 ? 's' : ''}`
    }
  }
}

const saveSettings = async () => {
  try {
    showSuccessMessage.value = false
    showErrorMessage.value = false

    await saveUserSettings({
      dataRetentionHours: settings.value.dataRetentionHours
    })

    showSuccessMessage.value = true
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 3000)
  } catch (error: any) {
    errorMessage.value = error.message || 'Failed to save retention settings'
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 5000)
  }
}

// Load data on mount
onMounted(async () => {
  await loadMetrics()
  await loadSettings()
})
</script>
