<template>
  <div v-if="grandfathering?.isGrandfathered" class="grandfathering-badge">
    <!-- Early Adopter Badge -->
    <div class="alert alert-success shadow-lg mb-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="w-6 h-6 text-success" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-semibold text-success-content">
                {{ getBadgeTitle() }}
              </h4>
              <p class="text-sm text-success-content/80 mt-1">
                {{ getBadgeDescription() }}
              </p>
            </div>
            <div class="text-right ml-4">
              <div class="text-lg font-bold text-success-content">
                {{ formatCurrency(grandfathering.grandfatheredPrice!) }}
                <span class="text-xs font-normal">/month</span>
              </div>
              <div class="text-xs text-success-content/70 line-through">
                {{ formatCurrency(grandfathering.currentPrice) }}
              </div>
            </div>
          </div>
          
          <!-- Savings Information -->
          <div class="mt-3 p-3 bg-success/10 rounded-lg border border-success/20">
            <div class="flex items-center justify-between text-sm">
              <span class="text-success-content/80">Monthly Savings:</span>
              <span class="font-semibold text-success-content">
                {{ formatCurrency(grandfathering.savings!) }} 
                ({{ grandfathering.savingsPercent }}% off)
              </span>
            </div>
            <div class="flex items-center justify-between text-sm mt-1">
              <span class="text-success-content/80">Annual Savings:</span>
              <span class="font-semibold text-success-content">
                {{ formatCurrency(grandfathering.savings! * 12) }}
              </span>
            </div>
          </div>

          <!-- Applied Date -->
          <div class="mt-2 text-xs text-success-content/60">
            {{ getAppliedText() }}
          </div>
        </div>
      </div>
    </div>

    <!-- Grandfathering Terms -->
    <div class="bg-base-200/40 rounded-lg p-4 mb-4">
      <h5 class="font-medium text-base-content mb-2 flex items-center">
        <svg class="w-4 h-4 mr-2 text-info" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
        Grandfathered Pricing Terms
      </h5>
      <ul class="text-sm text-base-content/70 space-y-1">
        <li>• Your pricing is locked in as long as you maintain your current plan</li>
        <li>• Upgrading to a higher plan will use current pricing</li>
        <li>• Downgrading and re-upgrading will lose grandfathered pricing</li>
        <li>• Canceling and re-subscribing will lose grandfathered pricing</li>
        <li v-if="hasGracePeriod()">• 30-day grace period if you need to reactivate</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface GrandfatheringStatus {
  isGrandfathered: boolean
  grandfatheredPrice?: number
  currentPrice: number
  savings?: number
  savingsPercent?: number
  reason?: string
  appliedAt?: string
  expiresAt?: string
}

interface Props {
  grandfathering: GrandfatheringStatus | null
}

const props = defineProps<Props>()

const getBadgeTitle = () => {
  if (!props.grandfathering?.reason) return 'Grandfathered Pricing'
  
  switch (props.grandfathering.reason) {
    case 'early_adopter':
      return '🌟 Early Adopter'
    case 'price_increase_2024':
      return '🛡️ Price Protection'
    case 'loyalty_reward':
      return '💎 Loyalty Reward'
    default:
      return 'Grandfathered Pricing'
  }
}

const getBadgeDescription = () => {
  if (!props.grandfathering?.reason) return 'You\'re locked into special pricing!'
  
  switch (props.grandfathering.reason) {
    case 'early_adopter':
      return 'Thank you for being an early supporter! You\'re locked into our original pricing.'
    case 'price_increase_2024':
      return 'Your pricing is protected from recent price increases.'
    case 'loyalty_reward':
      return 'Special pricing as a thank you for your continued loyalty.'
    default:
      return 'You\'re locked into special pricing!'
  }
}

const getAppliedText = () => {
  if (!props.grandfathering?.appliedAt) return ''
  
  const date = new Date(props.grandfathering.appliedAt)
  return `Applied on ${date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })}`
}

const hasGracePeriod = () => {
  // In a real implementation, this would check the grandfathering policy
  return props.grandfathering?.reason === 'early_adopter' || 
         props.grandfathering?.reason === 'price_increase_2024'
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}
</script>

<style scoped>
.grandfathering-badge {
  /* Custom styles for grandfathering badge */
}

.alert-success {
  background: linear-gradient(135deg, rgb(34 197 94 / 0.1) 0%, rgb(34 197 94 / 0.05) 100%);
  border: 1px solid rgb(34 197 94 / 0.2);
}

.dark .alert-success {
  background: linear-gradient(135deg, rgb(34 197 94 / 0.15) 0%, rgb(34 197 94 / 0.08) 100%);
  border: 1px solid rgb(34 197 94 / 0.3);
}
</style>
