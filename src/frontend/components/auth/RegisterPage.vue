<template>
  <div class="w-full max-w-md mx-auto px-4">
    <!-- Main Card -->
    <div class="card bg-base-100 shadow-2xl border border-base-300/50 backdrop-blur-sm">
      <div class="card-body p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-base-content mb-2">
            Join today
          </h2>
          <p class="text-base-content/70">
            Create your account to get started
          </p>
        </div>

        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Success Alert -->
        <div v-if="success" class="alert alert-success rounded-lg mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ success }}</span>
        </div>

        <!-- Registration Form -->
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-5">
            <div class="form-control">
              <label for="name" class="label">
                <span class="label-text font-medium">Full name <span class="text-base-content/50">(optional)</span></span>
              </label>
              <input
                id="name"
                v-model="form.name"
                name="name"
                type="text"
                autocomplete="name"
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter your full name"
              >
            </div>

            <div class="form-control">
              <label for="email" class="label">
                <span class="label-text font-medium">Email address</span>
              </label>
              <input
                id="email"
                v-model="form.email"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Enter your email address"
              >
            </div>

            <div class="form-control">
              <label for="password" class="label">
                <span class="label-text font-medium">Password</span>
              </label>
              <input
                id="password"
                v-model="form.password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                class="input input-bordered w-full focus:input-primary transition-colors"
                placeholder="Create a password (min. 8 characters)"
                minlength="8"
              >
              <div class="label">
                <span class="label-text-alt text-base-content/60">Must be at least 8 characters long</span>
              </div>
            </div>
          </div>

          <div class="form-control py-2">
            <label class="label cursor-pointer justify-start p-0">
              <input
                id="terms"
                v-model="form.acceptTerms"
                name="terms"
                type="checkbox"
                required
                class="checkbox checkbox-primary checkbox-sm"
              >
              <span class="label-text ml-3 text-sm">
                I agree to the
                <router-link to="/terms-of-service" class="link link-primary hover:link-primary/80 transition-colors">Terms of Service</router-link>
                and
                <router-link to="/privacy-policy" class="link link-primary hover:link-primary/80 transition-colors">Privacy Policy</router-link>
              </span>
            </label>
          </div>

          <div class="pt-2">
            <button
              type="submit"
              :disabled="loading || !form.acceptTerms"
              class="btn btn-primary w-full h-12 text-base font-medium transition-all hover:scale-[1.02] active:scale-[0.98]"
              :class="{ 'loading': loading }"
            >
              <span v-if="loading" class="loading loading-spinner loading-sm"></span>
              {{ loading ? 'Creating account...' : 'Create account' }}
            </button>
          </div>
        </form>

        <!-- Sign in link -->
        <div class="divider text-base-content/50">or</div>
        <div class="text-center">
          <p class="text-base-content/70">
            Already have an account?
            <router-link to="/login" class="link link-primary font-medium hover:link-primary/80 transition-colors">
              Sign in here
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// State
const loading = ref(false)
const error = ref('')
const success = ref('')

const form = reactive({
  name: '',
  email: '',
  password: '',
  acceptTerms: false
})

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  error.value = ''
  success.value = ''

  try {
    const response = await fetch('/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies to receive user_token
      body: JSON.stringify({
        name: form.name || undefined,
        email: form.email,
        password: form.password
      }),
    })
    
    const result = await response.json()
    
    if (response.ok) {
      // Success - redirect to dashboard
      // Authentication token is securely stored in httpOnly cookie
      success.value = 'Account created successfully! Redirecting...'
      setTimeout(() => {
        window.location.href = '/domains'
      }, 1500)
    } else {
      // Show error
      error.value = result.error || 'Registration failed. Please try again.'
    }
  } catch (err) {
    console.error('Registration error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Register page specific styles */
.card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth focus transitions */
.input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Button hover effects */
.btn:hover:not(:disabled) {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Icon animation */
.card-body svg {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Disabled button styling */
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
