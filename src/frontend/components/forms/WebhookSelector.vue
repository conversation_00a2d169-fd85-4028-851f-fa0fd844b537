<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useWebhookApi } from '@composables/useApi'
import type { Webhook } from '@types'
import { formatWebhookUrl } from '../../utils/url'

interface Props {
  modelValue: string
  context?: string
  disabled?: boolean
  required?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  context: 'default',
  disabled: false,
  required: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'webhook-created': [webhook: Webhook]
}>()

// State
const webhooks = ref<Webhook[]>([])
const loading = ref(false)
const showCreateForm = ref(false)
const showDropdown = ref(false)
const newWebhook = ref({
  name: '',
  url: '',
  description: ''
})
const creating = ref(false)

// API
const { getWebhooks, createWebhook } = useWebhookApi()

// Computed
const selectedWebhook = computed(() => 
  webhooks.value.find(w => w.id === props.modelValue)
)

const isCreateFormValid = computed(() =>
  newWebhook.value.name.trim() && newWebhook.value.url.trim()
)



// Methods
const loadWebhooks = async () => {
  loading.value = true
  try {
    const response = await getWebhooks() as any
    webhooks.value = response.webhooks || []
  } catch (error) {
    console.error('Failed to load webhooks:', error)
  } finally {
    loading.value = false
  }
}

const selectWebhook = (webhookId: string) => {
  emit('update:modelValue', webhookId)
  showDropdown.value = false
}

const toggleDropdown = () => {
  if (!props.disabled && !loading.value) {
    showDropdown.value = !showDropdown.value
  }
}

const toggleCreateForm = () => {
  showCreateForm.value = !showCreateForm.value
  if (!showCreateForm.value) {
    // Reset form when closing
    newWebhook.value = { name: '', url: '', description: '' }
  }
}

const createNewWebhook = async () => {
  if (!isCreateFormValid.value) return

  creating.value = true
  try {
    const result = await createWebhook(newWebhook.value) as any
    const webhook = result.webhook

    // Add to local list
    webhooks.value.unshift(webhook)

    // Select the new webhook
    emit('update:modelValue', webhook.id)
    emit('webhook-created', webhook)

    // Reset form and close
    newWebhook.value = { name: '', url: '', description: '' }
    showCreateForm.value = false

  } catch (error) {
    console.error('Failed to create webhook:', error)
  } finally {
    creating.value = false
  }
}

// Click outside handler
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Element
  if (!target.closest('.webhook-dropdown')) {
    showDropdown.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadWebhooks()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="form-control">
    <label class="label">
      <span class="label-text">Select webhook</span>
    </label>
    
    <!-- Custom Webhook Selection Dropdown -->
    <div class="relative space-y-3 webhook-dropdown">
      <!-- Dropdown Button -->
      <button
        type="button"
        @click="toggleDropdown"
        class="flex items-center justify-between w-full px-3 py-2 text-left bg-base-100 border border-base-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
        :class="{ 'cursor-not-allowed opacity-50': disabled || loading }"
        :disabled="disabled || loading"
      >
        <span class="flex items-center flex-1 min-w-0">
          <span v-if="loading" class="text-base-content/70">Loading webhooks...</span>
          <span v-else-if="!selectedWebhook" class="text-base-content/70">Choose a webhook...</span>
          <span v-else class="flex items-center gap-2 min-w-0">
            <span class="truncate">{{ formatWebhookUrl(selectedWebhook.url) }}</span>
            <span v-if="selectedWebhook.verified" class="badge bg-gray-100 text-gray-700 border border-gray-300 badge-sm">
              <div class="status status-success mr-1"></div>Verified
            </span>
            <span v-else class="badge bg-gray-100 text-gray-700 border border-gray-300 badge-sm">
              <div class="status status-info mr-1"></div>Unverified
            </span>
          </span>
        </span>
        <svg class="w-4 h-4 ml-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <!-- Dropdown Menu -->
      <div
        v-if="showDropdown"
        class="absolute z-10 w-full mt-1 bg-base-100 border border-base-300 rounded-lg shadow-lg max-h-60 overflow-auto"
      >
        <div
          v-if="!loading && webhooks.length === 0"
          class="px-3 py-2 text-base-content/70"
        >
          No webhooks available
        </div>
        <button
          v-for="webhook in webhooks"
          :key="webhook.id"
          type="button"
          @click="selectWebhook(webhook.id)"
          class="flex items-center justify-between w-full px-3 py-2 text-left hover:bg-base-200 focus:outline-none focus:bg-base-200"
          :class="{ 'bg-primary/10': webhook.id === modelValue }"
        >
          <span class="flex items-center gap-2 min-w-0 flex-1">
            <span class="truncate text-sm">{{ formatWebhookUrl(webhook.url) }}</span>
            <span v-if="webhook.verified" class="badge bg-gray-100 text-gray-700 border border-gray-300 badge-sm">
              <div class="status status-success mr-1"></div>Verified
            </span>
            <span v-else class="badge bg-gray-100 text-gray-700 border border-gray-300 badge-sm">
              <div class="status status-info mr-1"></div>Unverified
            </span>
          </span>
        </button>
      </div>

      <!-- Create New Webhook Button -->
      <button
        type="button"
        @click="toggleCreateForm"
        class="w-full btn btn-outline btn-sm"
        :disabled="disabled"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        {{ showCreateForm ? 'Cancel' : 'Create new webhook' }}
      </button>
    </div>
    
    <!-- Inline Create Form -->
    <div v-if="showCreateForm" class="p-4 mt-4 border border-base-300 rounded-lg bg-base-200/40">
      <h4 class="mb-3 font-medium text-base-content">Create new webhook</h4>
      
      <div class="space-y-3">
        <div class="form-control">
          <label class="label">
            <span class="text-sm label-text">Webhook name</span>
          </label>
          <input
            v-model="newWebhook.name"
            type="text"
            placeholder="My webhook"
            class="w-full input input-bordered input-sm"
            :disabled="creating"
            required
          />
        </div>
        
        <div class="form-control">
          <label class="label">
            <span class="text-sm label-text">Webhook URL</span>
          </label>
          <input
            v-model="newWebhook.url"
            type="url"
            placeholder="https://your-app.com/webhook"
            class="w-full input input-bordered input-sm"
            :disabled="creating"
            required
          />
        </div>
        
        <div class="flex gap-2 pt-2">
          <button
            type="button"
            @click="createNewWebhook"
            class="flex-1 btn btn-primary btn-sm"
            :disabled="!isCreateFormValid || creating"
            :class="{ 'loading': creating }"
          >
            {{ creating ? 'Creating...' : 'Create webhook' }}
          </button>
          <button
            type="button"
            @click="toggleCreateForm"
            class="btn btn-ghost btn-sm"
            :disabled="creating"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
