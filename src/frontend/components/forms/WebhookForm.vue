<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useForm } from '../../composables/useForm'
import { useWebhookApi } from '../../composables/useApi'
import { useDataRefresh } from '../../composables/useDataRefresh'
import { useMetrics } from '../../composables/useMetrics'
import { usePermissions } from '../../composables/usePermissions'

import type { CreateWebhookRequest } from '../../types'

interface Props {
  initialData?: Partial<CreateWebhookRequest & {
    context?: string
    domainForm?: any
    id?: string
    customHeaders?: Record<string, string>
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [webhook: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateWebhookRequest>({
  name: props.initialData.name || '',
  url: props.initialData.url || '',
  description: props.initialData.description || ''
}, [
  { name: 'name', label: 'Webhook name', type: 'text', required: true, validation: { minLength: 2, maxLength: 100 } },
  { name: 'url', label: 'Webhook URL', type: 'url', required: true },
  { name: 'description', label: 'Description', type: 'text', validation: { maxLength: 500 } }
])

// API setup
const { createWebhook, updateWebhook } = useWebhookApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()
const { metricsData } = useMetrics()
const { hasPermission, loadPermissions } = usePermissions()

// Custom headers state
const customHeaders = ref<Array<{ key: string; value: string }>>([])
const isLoadingPlan = ref(true)

const hasCustomHeaders = computed(() => {
  return customHeaders.value.some(header => header.key.trim() && header.value.trim())
})

// Check if user can use custom headers based on proper permissions
const canUseCustomHeaders = computed(() => {
  // Use the proper permission system that checks for 'custom_headers' permission
  return hasPermission('custom_headers')
})

// Methods
const loadUserPlan = async () => {
  try {
    isLoadingPlan.value = true

    // Load permissions to ensure we have the latest permission data
    await loadPermissions()
  } catch (error) {
    console.error('Failed to load user plan:', error)
  } finally {
    isLoadingPlan.value = false
  }
}

const addCustomHeader = () => {
  customHeaders.value.push({ key: '', value: '' })
}

const removeCustomHeader = (index: number) => {
  customHeaders.value.splice(index, 1)
}

// Validate custom header key format
const validateHeaderKey = (key: string): boolean => {
  // HTTP header names should not contain spaces, and should be valid header names
  const headerKeyRegex = /^[a-zA-Z0-9\-_]+$/
  return headerKeyRegex.test(key)
}

// Get validation errors for custom headers
const customHeaderErrors = computed(() => {
  const errors: string[] = []
  customHeaders.value.forEach((header, index) => {
    if (header.key && !validateHeaderKey(header.key)) {
      errors.push(`Header ${index + 1}: Invalid header name. Use only letters, numbers, hyphens, and underscores.`)
    }
    if (header.key && header.key.toLowerCase().startsWith('content-')) {
      errors.push(`Header ${index + 1}: Content-* headers are automatically managed.`)
    }
  })
  return errors
})

const initializeCustomHeaders = () => {
  // Clear existing headers first
  customHeaders.value = []

  // Load headers from props if they exist
  if (props.initialData?.customHeaders && typeof props.initialData.customHeaders === 'object') {
    customHeaders.value = Object.entries(props.initialData.customHeaders).map(([key, value]) => ({
      key,
      value: String(value)
    }))
  }

  // Always have at least one empty header for Pro users if no headers exist
  if (canUseCustomHeaders.value && customHeaders.value.length === 0) {
    addCustomHeader()
  }
}

const getCustomHeadersObject = () => {
  const headers: Record<string, string> = {}
  customHeaders.value.forEach(header => {
    if (header.key.trim() && header.value.trim()) {
      headers[header.key.trim()] = header.value.trim()
    }
  })
  return Object.keys(headers).length > 0 ? headers : undefined
}

const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    let result: any
    let webhook: any

    // Add custom headers if user has Pro plan and headers are defined
    const submitData = {
      ...formData,
      ...(canUseCustomHeaders.value && hasCustomHeaders.value && {
        customHeaders: getCustomHeadersObject()
      })
    }

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing webhook
      result = await updateWebhook(props.initialData.id, submitData)
      webhook = result.webhook
      emit('success', webhook)
      refreshWithSuccess('Webhook updated successfully!', 'webhooks')

      // If URL changed, webhook needs re-verification
      if (formData.url !== props.initialData.url) {
        setTimeout(() => {
          window.openModal('webhook-verification', {
            webhookId: webhook.id,
            webhookUrl: webhook.url,
            webhookName: webhook.name
          });
        }, 700)
      }
    } else {
      // Create mode - create new webhook
      result = await createWebhook(submitData) as any
      webhook = result.webhook
      emit('success', webhook)
      refreshWithSuccess('Webhook created successfully!', 'webhooks')

      // Auto-open webhook verification modal for new unverified webhooks
      setTimeout(() => {
        window.openModal('webhook-verification', {
          webhookId: webhook.id,
          webhookUrl: webhook.url,
          webhookName: webhook.name
        });
      }, 700)
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after webhook operation:', error)
      }
    }, 500)
  })
}

// Watch for changes in initialData to reinitialize headers
watch(() => props.initialData, () => {
  if (canUseCustomHeaders.value) {
    initializeCustomHeaders()
  }
}, { deep: true, immediate: false })

// Initialize component
onMounted(async () => {
  await loadUserPlan()
  // Initialize custom headers after plan is loaded
  setTimeout(() => {
    initializeCustomHeaders()
  }, 100)
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Webhook Name -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook name</span>
      </label>
      <input
        :value="values.name"
        @input="setFieldValue('name', ($event.target as HTMLInputElement).value)"
        type="text"
        placeholder="My webhook"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.name }"
        required
      />
      <div v-if="errors.name" class="label">
        <span class="label-text-alt text-error">{{ errors.name }}</span>
      </div>
    </div>

    <!-- Webhook URL -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook URL</span>
      </label>
      <input
        :value="values.url"
        @input="setFieldValue('url', ($event.target as HTMLInputElement).value)"
        type="url"
        placeholder="https://your-app.com/webhook"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.url }"
        required
      />
      <div v-if="errors.url" class="label">
        <span class="label-text-alt text-error">{{ errors.url }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">The endpoint where emails will be sent</span>
      </div>
    </div>

    <!-- Description -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Description (optional)</span>
      </label>
      <textarea
        :value="values.description"
        @input="setFieldValue('description', ($event.target as HTMLTextAreaElement).value)"
        placeholder="Brief description of this webhook..."
        rows="3"
        class="w-full textarea textarea-bordered"
        :class="{ 'textarea-error': errors.description }"
      />
      <div v-if="errors.description" class="label">
        <span class="label-text-alt text-error">{{ errors.description }}</span>
      </div>
    </div>

    <!-- Custom Headers (Pro Feature) -->
    <div v-if="!isLoadingPlan" class="form-control bg-base-200/40 p-4 rounded-lg border border-base-300 space-y-4">
      <div class="flex-1">
        <div class="font-medium">
          <span class="badge badge-primary badge-outline badge-sm">Pro</span>
          Custom headers
        </div>
        <div class="text-sm text-base-content/60">
          Add up to 5 custom HTTP headers to your webhook requests
        </div>
      </div>

      <div class="pt-4 border-t border-base-300">
        <div v-if="!canUseCustomHeaders" class="flex items-center gap-2 text-base-content/70">
          <span>Custom headers are available with Pro plan.</span>
          <router-link to="/settings#billing" class="link link-primary">Upgrade now</router-link>
        </div>

        <div v-else class="space-y-3 mt-1">
          <div v-if="customHeaders.length === 0" class="text-sm text-base-content/70">
            Add custom HTTP headers to be sent with webhook requests.
          </div>

          <div v-for="(header, index) in customHeaders" :key="index" class="flex gap-2 items-start">
            <div class="flex-1">
              <input
                v-model="header.key"
                type="text"
                placeholder="Header name (e.g., X-Custom-Token)"
                class="w-full input input-bordered input-sm"
                :class="{ 'input-error': header.key && !validateHeaderKey(header.key) }"
              />
            </div>
            <div class="flex-1">
              <input
                v-model="header.value"
                type="text"
                placeholder="Header value"
                class="w-full input input-bordered input-sm"
              />
            </div>
            <button
              type="button"
              @click="removeCustomHeader(index)"
              class="btn btn-ghost btn-sm btn-square text-error"
              :disabled="customHeaders.length === 1"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Validation errors -->
          <div v-if="customHeaderErrors.length > 0" class="space-y-1">
            <div v-for="error in customHeaderErrors" :key="error" class="text-xs text-error">
              {{ error }}
            </div>
          </div>

          <button
            type="button"
            @click="addCustomHeader"
            class="btn btn-outline btn-sm"
            :disabled="customHeaders.length >= 5"
          >
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add header
          </button>

          <div v-if="customHeaders.length >= 5" class="text-xs text-base-content/50">
            Maximum 5 custom headers. Common headers like Content-Type are already included.
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update webhook' : 'Create webhook')
        }}
      </button>
    </div>
  </form>
</template>