<template>
  <div v-if="loading" class="min-h-screen flex items-center justify-center">
    <div class="loading loading-spinner loading-lg text-primary"></div>
  </div>
  
  <StaticPageLayout v-else-if="staticPage" :key="route.path" />
  
  <NotFoundPage v-else />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import StaticPageLayout from './StaticPageLayout.vue'
import NotFoundPage from '../error/NotFoundPage.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const staticPage = ref(false)

const checkStaticPage = async (path: string) => {
  try {
    loading.value = true
    staticPage.value = false
    
    // Remove leading slash and check if it's a static page
    const slug = path.startsWith('/') ? path.slice(1) : path
    
    // Skip if it's clearly an app route
    const appRoutes = ['domains', 'aliases', 'webhooks', 'logs', 'settings', 'dashboard', 'login', 'register']
    if (appRoutes.includes(slug)) {
      return
    }
    
    // Check if this slug exists as a static page
    const response = await fetch(`/api/public/static/${slug}`)
    
    if (response.ok) {
      const data = await response.json()
      if (data.success && data.page) {
        staticPage.value = true
        // Update route params for StaticPageLayout
        route.params.slug = slug
      }
    }
  } catch (error) {
    console.error('Error checking static page:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  checkStaticPage(route.path)
})

// Watch for route changes
watch(() => route.path, (newPath) => {
  checkStaticPage(newPath)
})
</script>
