<template>
  <div class="max-w-4xl mx-auto px-4 py-12">
    <div class="prose prose-lg max-w-none text-base-content">
      <!-- Page Header -->
      <h1 class="text-4xl font-bold text-base-content mb-8">{{ page.title }}</h1>
      
      <!-- Last Updated Info -->
      <p v-if="page.lastUpdated" class="text-base-content/70 mb-8">
        <strong>Last updated:</strong> {{ page.lastUpdated }}<br>
        <strong>Effective date:</strong> {{ page.lastUpdated }}
      </p>

      <!-- Page Content (HTML) -->
      <div v-html="page.content" class="static-page-content"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

interface StaticPage {
  title: string;
  slug: string;
  lastUpdated: string;
  description: string;
  content: string;
}

const route = useRoute()
const page = ref<StaticPage>({
  title: '',
  slug: '',
  lastUpdated: '',
  description: '',
  content: ''
})
const loading = ref(true)
const error = ref('')

const fetchStaticPage = async (slug: string) => {
  try {
    loading.value = true
    error.value = ''
    
    const response = await fetch(`/api/public/static/${slug}`)
    
    if (!response.ok) {
      if (response.status === 404) {
        error.value = 'Page not found'
      } else {
        error.value = 'Failed to load page'
      }
      return
    }
    
    const data = await response.json()
    
    if (data.success && data.page) {
      page.value = data.page
    } else {
      error.value = 'Invalid page data'
    }
  } catch (err) {
    console.error('Error fetching static page:', err)
    error.value = 'Failed to load page'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  const slug = route.params.slug as string
  if (slug) {
    fetchStaticPage(slug)
  }
})

// Watch for route changes
import { watch } from 'vue'
watch(() => route.params.slug, (newSlug) => {
  if (newSlug) {
    fetchStaticPage(newSlug as string)
  }
})
</script>

<style>
/* Global styles for static page content */
.static-page-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.static-page-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.static-page-content p {
  margin-bottom: 1rem;
  line-height: 1.625;
}

.static-page-content ul {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: 1rem;
}

.static-page-content ul li {
  margin-bottom: 0.5rem;
}

.static-page-content strong {
  font-weight: 600;
}

/* Loading spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border-width: 2px;
  border-style: solid;
  border-color: transparent;
  border-bottom-color: currentColor;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
