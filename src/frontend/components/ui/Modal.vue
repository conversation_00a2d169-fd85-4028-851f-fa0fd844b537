<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'

interface Props {
  title: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  persistent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  persistent: false
})

const emit = defineEmits<{
  close: []
}>()

const sizeClasses = {
  sm: 'max-w-sm',
  md: 'max-w-xl',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
  full: 'max-w-full mx-4'
}

const handleOverlayClick = (event: MouseEvent) => {
  if (!props.persistent && props.closable && event.target === event.currentTarget) {
    emit('close')
  }
}

const handleEscapeKey = (event: KeyboardEvent) => {
  if (!props.persistent && props.closable && event.key === 'Escape') {
    emit('close')
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscapeKey)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscapeKey)
})
</script>

<template>
  <Transition
    name="modal"
    enter-active-class="transition-opacity duration-300"
    leave-active-class="transition-opacity duration-300"
    enter-from-class="opacity-0"
    leave-to-class="opacity-0"
  >
    <div
      class="fixed z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
      @click="handleOverlayClick"
    >
      <Transition
        name="modal-content"
        enter-active-class="transition-all duration-300"
        leave-active-class="transition-all duration-300"
        enter-from-class="scale-95 opacity-0"
        leave-to-class="scale-95 opacity-0"
      >
        <div
          :class="[
            'bg-white rounded-lg shadow-xl w-full max-h-[90vh] overflow-hidden flex flex-col break-words',
            sizeClasses[size]
          ]"
          @click.stop
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">{{ title }}</h2>
            <button
              v-if="closable"
              @click="emit('close')"
              class="text-gray-400 transition-colors hover:text-gray-600"
              type="button"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="flex-1 p-6 overflow-y-auto break-words">
            <slot />
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<style scoped>
/* Additional modal-specific styles if needed */
</style>