<script setup lang="ts">
import { useModal } from '@composables/useModal'

const { modalState, closeModal } = useModal()

// Define slots for different modal types
defineSlots<{
  'create-domain'?: (props: { data: any; close: () => void }) => any
  'create-webhook'?: (props: { data: any; close: () => void }) => any
  'create-alias'?: (props: { data: any; close: () => void }) => any
  'domain-verification'?: (props: { data: any; close: () => void }) => any
  'webhook-verification'?: (props: { data: any; close: () => void }) => any
  'webhook-test'?: (props: { data: any; close: () => void }) => any
  'edit-domain'?: (props: { data: any; close: () => void }) => any
  'edit-webhook'?: (props: { data: any; close: () => void }) => any
  'edit-alias'?: (props: { data: any; close: () => void }) => any
  'payload-viewer'?: (props: { data: any; close: () => void }) => any
}>()

// Get modal size classes for DaisyUI
const getModalSizeClass = (size: string) => {
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-xl',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full'
  }
  return sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.md
}

// Modal types that need to be rendered
const modalTypes = [
  'create-domain',
  'create-webhook',
  'create-alias',
  'domain-verification',
  'webhook-verification',
  'webhook-test',
  'edit-domain',
  'edit-webhook',
  'edit-alias',
  'payload-viewer'
]
</script>

<template>
  <!-- Render only the active modal as DaisyUI dialog element -->
  <dialog
    v-if="modalState.type"
    :id="`modal-${modalState.type}`"
    class="modal"
    :key="modalState.type"
  >
    <div class="modal-box" :class="getModalSizeClass(modalState.config.size)">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold">{{ modalState.config.title }}</h3>
        <form method="dialog" v-if="modalState.config.closable">
          <button class="btn btn-sm btn-circle btn-ghost">✕</button>
        </form>
      </div>

      <!-- Modal Content -->
      <div class="modal-content">
        <slot
          :name="modalState.type"
          :data="modalState.data"
          :close="closeModal"
        >
          <!-- Fallback content if no slot is provided -->
          <div class="p-4">
            <p class="text-gray-600">No content available for modal type: {{ modalState.type }}</p>
          </div>
        </slot>
      </div>
    </div>

    <!-- Modal backdrop - closes modal when clicked -->
    <form method="dialog" class="modal-backdrop" v-if="!modalState.config.persistent">
      <button>close</button>
    </form>
  </dialog>
</template>