<script setup lang="ts">
import { useToast } from '@composables/useToast'
import Toast from './Toast.vue'

const { toasts, removeToast } = useToast()
</script>

<template>
  <Teleport to="body">
    <!-- DaisyUI Toast Container -->
    <div class="toast toast-top toast-end z-50">
      <TransitionGroup
        name="toast-list"
        tag="div"
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-300 ease-in"
        enter-from-class="opacity-0 translate-y-[-100px]"
        enter-to-class="opacity-100 translate-y-0"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 translate-x-[400px]"
      >
        <Toast
          v-for="toast in toasts"
          :key="toast.id"
          :type="toast.type"
          :message="toast.message"
          :duration="toast.duration"
          :closable="toast.closable"
          @close="removeToast(toast.id)"
        />
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<style scoped>
/* Additional toast-specific animations if needed */
.toast-list-move,
.toast-list-enter-active,
.toast-list-leave-active {
  transition: all 0.3s ease;
}

.toast-list-enter-from,
.toast-list-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.toast-list-leave-active {
  position: absolute;
  right: 0;
}
</style>
