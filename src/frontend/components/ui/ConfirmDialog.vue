<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  isOpen: boolean
  title: string
  message: string
  details?: string
  confirmText?: string
  cancelText?: string
  type?: 'default' | 'danger' | 'warning' | 'info'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  confirmText: 'Confirm',
  cancelText: 'Cancel',
  type: 'default',
  loading: false
})

const emit = defineEmits<{
  confirm: []
  cancel: []
  close: []
}>()

const confirmButtonClasses = computed(() => {
  const baseClasses = 'btn'
  
  switch (props.type) {
    case 'danger':
      return `${baseClasses} btn-error`
    case 'warning':
      return `${baseClasses} btn-warning`
    case 'info':
      return `${baseClasses} btn-info`
    default:
      return `${baseClasses} btn-primary`
  }
})

const iconPath = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
    case 'warning':
      return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
    case 'info':
      return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    default:
      return 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
  }
})

const iconColorClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'text-error'
    case 'warning':
      return 'text-warning'
    case 'info':
      return 'text-info'
    default:
      return 'text-primary'
  }
})

const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('close')
  }
}
</script>

<template>
  <Teleport to="body">
    <Transition
      name="modal"
      enter-active-class="transition-opacity duration-300"
      leave-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isOpen"
        class="modal modal-open"
        @click="handleOverlayClick"
        @keydown.escape="handleEscapeKey"
      >
        <Transition
          name="modal-content"
          enter-active-class="transition-all duration-300"
          leave-active-class="transition-all duration-300"
          enter-from-class="opacity-0 scale-95"
          leave-to-class="opacity-0 scale-95"
        >
          <div class="modal-box max-w-md" @click.stop>
            <!-- Icon and Title -->
            <div class="flex items-center gap-4 mb-4">
              <div :class="['w-12 h-12 rounded-full flex items-center justify-center', iconColorClass, 'bg-opacity-10']">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="iconPath" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-base-content">{{ title }}</h3>
            </div>

            <!-- Message -->
            <div class="mb-6">
              <p class="text-base-content/70 mb-3">{{ message }}</p>
              
              <!-- Details (HTML content) -->
              <div
                v-if="details"
                class="text-sm bg-base-200 text-base-content/70 p-3 rounded-lg"
                v-html="details"
              />
            </div>

            <!-- Actions -->
            <div class="modal-action">
              <button
                type="button"
                class="btn btn-ghost"
                @click="emit('cancel')"
                :disabled="loading"
              >
                {{ cancelText }}
              </button>
              <button
                type="button"
                :class="[confirmButtonClasses, { 'loading': loading }]"
                @click="emit('confirm')"
                :disabled="loading"
              >
                <span v-if="!loading">{{ confirmText }}</span>
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>
