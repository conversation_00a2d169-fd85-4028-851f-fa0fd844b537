<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  duration?: number
  closable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  duration: 4000,
  closable: true
})

const emit = defineEmits<{
  close: []
}>()

const toastClasses = computed(() => {
  const baseClasses = 'alert flex items-center gap-3 min-w-80 max-w-md shadow-lg'
  
  switch (props.type) {
    case 'success':
      return `${baseClasses} alert-success`
    case 'error':
      return `${baseClasses} alert-error`
    case 'warning':
      return `${baseClasses} alert-warning`
    case 'info':
      return `${baseClasses} alert-info`
    default:
      return `${baseClasses} alert-info`
  }
})

const iconPath = computed(() => {
  switch (props.type) {
    case 'success':
      return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
    case 'error':
      return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
    case 'warning':
      return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
    case 'info':
      return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    default:
      return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
  }
})
</script>

<template>
  <Transition
    name="toast"
    enter-active-class="transition-all duration-300 ease-out"
    leave-active-class="transition-all duration-300 ease-in"
    enter-from-class="opacity-0 translate-y-[-100px]"
    enter-to-class="opacity-100 translate-y-0"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-x-[400px]"
  >
    <div :class="toastClasses">
      <!-- Icon -->
      <svg class="w-6 h-6 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="iconPath" />
      </svg>
      
      <!-- Message -->
      <span class="flex-1">{{ message }}</span>
      
      <!-- Close button using DaisyUI btn -->
      <button
        v-if="closable"
        @click="emit('close')"
        class="btn btn-sm btn-circle btn-ghost"
        type="button"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  </Transition>
</template>

<style scoped>
.alert-success {
  color: #fff !important;
}
.alert-warning {
  background: rgba(251, 191, 36, 1) !important; 
  color: #222 !important; 
}
.alert-info {
  background: rgba(147, 197, 253, 1) !important;
  color: #222 !important; 
}
</style>
