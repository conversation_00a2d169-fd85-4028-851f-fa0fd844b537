// URL utility functions

export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export function getDomainFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return null
  }
}

/**
 * Smart URL truncation - show beginning and end
 * Example: https://webhook.site/6edc5018-0a80-44cf-b31d-7a4274eb8637 
 * becomes: https://webhook.site/...eb8637
 */
export function formatWebhookUrl(url: string, maxLength: number = 50): string {
  if (url.length <= maxLength) return url

  try {
    // Try to preserve the domain and the end of the path
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    const path = urlObj.pathname + urlObj.search

    // If just domain + path is still too long, truncate smartly
    const domainAndPath = `${urlObj.protocol}//${domain}${path}`
    if (domainAndPath.length <= maxLength) return domainAndPath

    // Calculate how much space we have for the path after domain
    const domainPart = `${urlObj.protocol}//${domain}`
    const availableForPath = maxLength - domainPart.length - 3 // 3 for "..."

    if (availableForPath <= 0) {
      // If domain itself is too long, just truncate normally
      return url.substring(0, maxLength - 3) + '...'
    }

    // Show beginning of domain and end of path
    const pathLength = path.length
    if (pathLength <= availableForPath) {
      return domainAndPath
    }

    // Take the last part of the path that fits
    const endPath = path.substring(pathLength - availableForPath)
    return `${domainPart}/...${endPath}`
  } catch {
    // Fallback to simple truncation if URL parsing fails
    return url.length > maxLength ? url.substring(0, maxLength - 3) + '...' : url
  }
}
