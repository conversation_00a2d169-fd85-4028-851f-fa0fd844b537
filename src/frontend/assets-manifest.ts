// Asset manifest for HTML meta tags
// This ensures all assets are processed by Vite and available via /assets/ path

import favicon from '@/assets/images/favicon.ico'
import favicon16 from '@/assets/images/favicon.ico' // Using same favicon for 16px
import favicon32 from '@/assets/images/favicon-96x96.png' // Using 96x96 for 32px
import appleTouchIcon from '@/assets/images/apple-touch-icon.png'
import siteWebmanifest from '@/assets/images/site.webmanifest'
import ogImage from '@/assets/images/og-image.png'
import androidChrome192 from '@/assets/images/web-app-manifest-192x192.png'
import androidChrome512 from '@/assets/images/web-app-manifest-512x512.png'
import logoLg from '@/assets/images/logo-light-lg.png'
import logoDarkLg from '@/assets/images/logo-dark-lg.png'

export const assets = {
  favicon,
  favicon16,
  favicon32,
  appleTouchIcon,
  siteWebmanifest,
  ogImage,
  androidChrome192,
  androidChrome512,
  logoLg,
  logoDarkLg,
}

// Make assets available globally for HTML meta tag updates
if (typeof window !== 'undefined') {
  (window as Window & { __ASSETS__: typeof assets }).__ASSETS__ = assets
}
