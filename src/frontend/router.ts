import { createRouter, createWebHistory } from 'vue-router'

// Use dynamic imports for better code splitting
const DomainsView = () => import('./components/dashboard/DomainsView.vue')
const AliasesView = () => import('./components/dashboard/AliasesView.vue')
const WebhooksView = () => import('./components/dashboard/WebhooksView.vue')
const LogsView = () => import('./components/dashboard/LogsView.vue')
const SettingsPage = () => import('./components/settings/SettingsPage.vue')
const LoginPage = () => import('./components/auth/LoginPage.vue')
const RegisterPage = () => import('./components/auth/RegisterPage.vue')
const LandingPage = () => import('./components/landing/LandingPage.vue')
const StaticPageLayout = () => import('./components/content/StaticPageLayout.vue')
const DynamicPageResolver = () => import('./components/content/DynamicPageResolver.vue')
const ChangelogPage = () => import('./components/content/ChangelogPage.vue')
const HelpIndexPage = () => import('./components/content/HelpIndexPage.vue')
const HelpArticlePage = () => import('./components/content/HelpArticlePage.vue')

const routes = [
  // Landing page (public)
  {
    path: '/',
    name: 'home',
    component: LandingPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Static pages (legal documents, etc.) - public
  // These are handled by the dynamic resolver, but we keep explicit routes for better SEO/navigation
  {
    path: '/terms-of-service',
    name: 'terms-of-service',
    component: StaticPageLayout,
    meta: { requiresAuth: false, layout: 'guest' },
    beforeEnter: (to, from, next) => {
      to.params.slug = 'terms-of-service'
      next()
    }
  },
  {
    path: '/privacy-policy',
    name: 'privacy-policy',
    component: StaticPageLayout,
    meta: { requiresAuth: false, layout: 'guest' },
    beforeEnter: (to, from, next) => {
      to.params.slug = 'privacy-policy'
      next()
    }
  },
  // Content pages (public)
  {
    path: '/changelog',
    name: 'changelog',
    component: ChangelogPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  {
    path: '/help',
    name: 'help',
    component: HelpIndexPage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  {
    path: '/help/:slug',
    name: 'help-article',
    component: HelpArticlePage,
    meta: { requiresAuth: false, layout: 'guest' }
  },
  // Auth routes (public)
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { requiresAuth: false, layout: 'auth' }
  },
  // Dashboard routes (protected) - clean URLs
  {
    path: '/dashboard',
    redirect: '/domains'
  },
  {
    path: '/domains',
    name: 'domains',
    component: DomainsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/aliases',
    name: 'aliases',
    component: AliasesView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/webhooks',
    name: 'webhooks',
    component: WebhooksView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/logs',
    name: 'logs',
    component: LogsView,
    meta: { requiresAuth: true, layout: 'user' }
  },
  {
    path: '/settings',
    name: 'settings',
    component: SettingsPage,
    meta: { requiresAuth: true, layout: 'user' }
  },
  // Catch-all route for dynamic page resolution (static pages vs 404)
  // This must be last to avoid catching legitimate routes
  {
    path: '/:pathMatch(.*)*',
    name: 'dynamic-resolver',
    component: DynamicPageResolver,
    meta: { requiresAuth: false, layout: 'guest' }
  }
] as const

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Plausible Analytics: Track pageviews on every route change (SPA support)
declare global {
  interface Window {
    plausible?: (...args: any[]) => void;
  }
}

router.afterEach(() => {
  if (window.plausible) {
    window.plausible('pageview');
  }
});

// Route guard to check authentication
router.beforeEach(async (to, from, next) => {
  const requiresAuth = to.meta.requiresAuth

  if (requiresAuth) {
    // For protected routes, check authentication via API call
    // since HttpOnly cookies can't be read by JavaScript
    try {
      const response = await fetch('/api/auth/check', {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        // User is authenticated
        next()
      } else {
        // User is not authenticated, redirect to login
        next('/login')
      }
    } catch (error) {
      // Network error or server down, redirect to login
      // eslint-disable-next-line no-console
      console.warn('Auth check failed:', error)
      next('/login')
    }
  } else if (!requiresAuth && (to.path === '/login' || to.path === '/register')) {
    // For login/register pages, check if user is already authenticated
    try {
      const response = await fetch('/api/auth/check', {
        method: 'GET',
        credentials: 'include'
      })

      if (response.ok) {
        // User is already authenticated, redirect to dashboard
        next('/domains')
      } else {
        // User is not authenticated, allow access to login/register
        next()
      }
    } catch (error) {
      // Network error, allow access to login/register
      // eslint-disable-next-line no-console
      console.warn('Network error:', error)
      next()
    }
  } else {
    // Public routes or other cases
    next()
  }
})

export default router
