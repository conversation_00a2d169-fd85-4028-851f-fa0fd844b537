# Dependencies
node_modules
npm-debug.log*

# Build outputs (will be generated during build)
dist

# Environment files
.env
.env.local
.env.production
.env.prod

# Sqlite storage location
data

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode
.idea
*.swp
*.swo

# Logs
logs
*.log

# Test files
coverage
*.lcov

# Temporary files
.tmp
.temp

# Git
.git
.gitignore

# Docker files
Dockerfile*
docker-compose*

# Documentation
README.md
CHANGELOG.md

# Deployment scripts (not needed in container)
deploy/

# Development tools
.eslintrc*
.prettierrc*
