# Postfix SQLite Query Configuration
# This file defines how Postfix queries the SQLite database for virtual domains and aliases

# Query for virtual domain lookup
# Used by Postfix to determine if a domain should be handled locally
SELECT CASE WHEN spam_filtering = 1 THEN '<EMAIL>' ELSE '<EMAIL>' END FROM virtual_domains WHERE domain='%s' AND active=1

# Alternative query for debugging (returns domain name)
# query = SELECT domain FROM virtual_domains WHERE domain='%s' AND active=1

# Database connection settings
hosts = /opt/eu-email-webhook/data/postfix.db
dbpath = /opt/eu-email-webhook/data/postfix.db

# Optional: Connection settings for better performance
# result_format = %s
# expansion_limit = 0

# Security settings
# domain = 
# hosts = localhost
# user = 
# password = 

# Debugging (set to yes for troubleshooting)
# debuglevel = 0
