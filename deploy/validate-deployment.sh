#!/bin/bash

# 🧪 EU Email Webhook - Deployment Validation Script
# Updated for /opt/eu-email-webhook and Docker-based deployment
# Run this after deployment to verify everything is working

set -e

DOMAIN="${DOMAIN:-emailconnect.eu}"
TEST_WEBHOOK="${WEBHOOK_URL:-https://webhook.site/9f60d9f5-277e-439d-89b7-6793d7f6b6ee}"
APP_DIR="/opt/eu-email-webhook"

echo "🧪 EU Email Webhook - Deployment Validation"
echo "🌐 Testing domain: $DOMAIN"
echo "📁 Application directory: $APP_DIR"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test function
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    echo -n "Testing $name... "
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/response "$url" --max-time 10); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            echo -e "${GREEN}✅ OK${NC} (HTTP $status_code)"
            return 0
        else
            echo -e "${RED}❌ FAILED${NC} (HTTP $status_code, expected $expected_status)"
            return 1
        fi
    else
        echo -e "${RED}❌ FAILED${NC} (Connection error)"
        return 1
    fi
}

# Service status function
check_service() {
    local service="$1"
    echo -n "Checking $service service... "
    
    if systemctl is-active --quiet "$service"; then
        echo -e "${GREEN}✅ RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        return 1
    fi
}

# Docker container check
check_container() {
    local container="$1"
    echo -n "Checking Docker container $container... "
    
    if docker ps --format "{{.Names}}" | grep -q "$container"; then
        echo -e "${GREEN}✅ RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        return 1
    fi
}

echo "🔍 Phase 1: Basic Service Health Checks"
echo "============================================"

# Test basic endpoints
test_endpoint "Main App Health" "http://localhost:3000/health"
test_endpoint "Postfix Manager Health" "http://localhost:3001/health"

# Check system services
check_service "nginx"
check_service "docker"
check_service "postfix"

echo ""
echo "🐳 Phase 2: Docker Container Checks"
echo "==================================="

cd "$APP_DIR"

# Check Docker containers
echo "Docker container status:"
docker compose -f docker-compose.prod.yml --env-file .env.prod ps

# Check individual containers
check_container "app"
check_container "postfix-manager"
check_container "postgres"
check_container "redis"

echo ""
echo "🗄️ Phase 3: Database & Storage Checks"
echo "====================================="

# Check SQLite database
echo -n "Checking SQLite database... "
if [ -f "/opt/eu-email-webhook/data/postfix.db" ]; then
    echo -e "${GREEN}✅ EXISTS${NC}"
    
    # Test SQLite database integrity
    echo -n "Testing SQLite database integrity... "
    if sqlite3 /opt/eu-email-webhook/data/postfix.db "PRAGMA integrity_check;" | grep -q "ok"; then
        echo -e "${GREEN}✅ VALID${NC}"
    else
        echo -e "${RED}❌ CORRUPTED${NC}"
    fi
    
    # Check tables
    echo -n "Checking database tables... "
    table_count=$(sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT COUNT(*) FROM sqlite_master WHERE type='table';")
    if [ "$table_count" -ge 2 ]; then
        echo -e "${GREEN}✅ TABLES EXIST${NC} ($table_count tables)"
    else
        echo -e "${RED}❌ MISSING TABLES${NC}"
    fi
else
    echo -e "${RED}❌ MISSING${NC}"
fi

# Check PostgreSQL connectivity
echo -n "Testing PostgreSQL connectivity... "
if docker compose -f docker-compose.prod.yml --env-file .env.prod exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ DISCONNECTED${NC}"
fi

# Check Redis connectivity
echo -n "Testing Redis connectivity... "
if docker compose -f docker-compose.prod.yml --env-file .env.prod exec -T redis redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ DISCONNECTED${NC}"
fi

echo ""
echo "📧 Phase 4: Email Server Configuration"
echo "===================================="

# Check MX record
echo -n "Checking MX record for $DOMAIN... "
if dig +short MX "$DOMAIN" | grep -q "$DOMAIN"; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${YELLOW}⚠️ NOT CONFIGURED${NC}"
    echo "   Configure MX record: $DOMAIN IN MX 10 $DOMAIN"
fi

# Check email aliases
echo -n "Checking email aliases... "
if grep -q "process-email:" /etc/aliases; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ MISSING${NC}"
fi

# Check Postfix SQLite configuration
echo -n "Checking Postfix SQLite configuration... "
if postconf virtual_alias_domains | grep -q "sqlite:"; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${RED}❌ FILE-BASED${NC}"
fi

# Check SQLite query files
echo -n "Checking SQLite query files... "
if [ -f "/opt/eu-email-webhook/data/virtual_domains.cf" ] && [ -f "/opt/eu-email-webhook/data/virtual_aliases.cf" ]; then
    echo -e "${GREEN}✅ EXISTS${NC}"
else
    echo -e "${RED}❌ MISSING${NC}"
fi

echo ""
echo "🔒 Phase 5: SSL & Security"
echo "========================="

# Check SSL certificate
echo -n "Checking SSL certificate... "
if echo | timeout 5 openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" 2>/dev/null | grep -q "Verify return code: 0"; then
    echo -e "${GREEN}✅ VALID${NC}"
else
    echo -e "${YELLOW}⚠️ INVALID/MISSING${NC}"
    echo "   Run: sudo certbot --nginx -d $DOMAIN"
fi

# Check security headers
echo -n "Checking security headers... "
if curl -I "https://$DOMAIN" 2>/dev/null | grep -q "X-"; then
    echo -e "${GREEN}✅ CONFIGURED${NC}"
else
    echo -e "${YELLOW}⚠️ BASIC${NC}"
fi

echo ""
echo "🧪 Phase 6: API Functionality Test"
echo "=================================="

if [ "$TEST_WEBHOOK" != "https://webhook.site/unique-uuid" ]; then
    echo "Testing API with webhook URL: $TEST_WEBHOOK"
    
    # Test domain addition
    echo -n "Testing domain addition API... "
    
    TEST_DOMAIN="test-$(date +%s).example.com"
    echo "Using test domain: $TEST_DOMAIN"

    status_code=$(curl -s -o /tmp/response_body -w "%{http_code}" -X POST "http://localhost:3000/api/domains" \
        -H "Content-Type: application/json" \
        -d "{\"domain\":\"$TEST_DOMAIN\",\"webhookUrl\":\"$TEST_WEBHOOK\"}" \
        --max-time 15)

    if [ "$status_code" = "201" ]; then
        echo -e "${GREEN}✅ SUCCESS${NC}"
        
        # Check if domain was added to SQLite
        echo -n "Verifying domain in SQLite... "
        if sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT domain FROM virtual_domains WHERE domain='$TEST_DOMAIN';" | grep -q "$TEST_DOMAIN"; then
            echo -e "${GREEN}✅ STORED${NC}"
        else
            echo -e "${RED}❌ NOT STORED${NC}"
        fi
    else
        echo -e "${RED}❌ FAILED${NC} (HTTP $status_code)"
        echo "Response:"
        cat /tmp/response_body
    fi
else
    echo -e "${YELLOW}ℹ️  Skipping API test - provide TEST_WEBHOOK environment variable${NC}"
    echo "   Example: TEST_WEBHOOK=https://webhook.site/your-uuid ./validate-deployment.sh"
fi

echo ""
echo "📊 Phase 7: Resource Usage"
echo "========================="

# Check disk usage
echo "Disk usage:"
df -h /opt/eu-email-webhook | awk 'NR==2 {print "  Application: " $3 "/" $2 " (" $5 " used)"}'
df -h / | awk 'NR==2 {print "  System: " $3 "/" $2 " (" $5 " used)"}'

# Check memory usage
echo "Memory usage:"
free -h | awk 'NR==2{printf "  System: %.1fG/%.1fG (%.1f%% used)\n", $3/1024, $2/1024, $3*100/$2}'

# Check Docker resource usage
echo "Docker containers resource usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -6

echo ""
echo "🎯 Validation Complete!"
echo "======================"

echo ""
echo -e "${GREEN}✅ Your EU Email Webhook service appears to be running correctly!${NC}"
echo ""
echo "🔗 Next Steps:"
echo "  1. Configure DNS MX record if not done: $DOMAIN IN MX 10 $DOMAIN"
echo "  2. Set up SSL certificate: sudo certbot --nginx -d $DOMAIN"
echo "  3. Test email flow by adding a domain via API"
echo "  4. Send a test email and verify webhook delivery"
echo ""
echo "📚 Useful Commands:"
echo "  - View logs: docker compose -f $APP_DIR/docker-compose.prod.yml logs -f"
echo "  - Check SQLite: sqlite3 /opt/eu-email-webhook/data/postfix.db"
echo "  - Health check: curl http://localhost:3000/health"
echo "  - Postfix status: systemctl status postfix"
