#!/bin/bash

# Test script for Postfix Manager Service
set -e

BASE_URL="http://localhost:3001"

echo "🧪 Testing Postfix Manager Service"
echo ""

# Test 1: Health check
echo "1️⃣  Testing health endpoint..."
curl -s "$BASE_URL/health" | jq '.'
echo ""

# Test 2: Status check
echo "2️⃣  Testing status endpoint..."
curl -s "$BASE_URL/status" | jq '.'
echo ""

# Test 3: Setup
echo "3️⃣  Running initial setup..."
curl -s -X POST "$BASE_URL/setup" | jq '.'
echo ""

# Test 4: Add domains
echo "4️⃣  Adding test domains..."
curl -s -X POST "$BASE_URL/domains" \
  -H "Content-Type: application/json" \
  -d '{"domain": "test1.example.com"}' | jq '.'

curl -s -X POST "$BASE_URL/domains" \
  -H "Content-Type: application/json" \
  -d '{"domain": "test2.example.com"}' | jq '.'
echo ""

# Test 5: List domains
echo "5️⃣  Listing configured domains..."
curl -s "$BASE_URL/domains" | jq '.'
echo ""

# Test 6: Final status
echo "6️⃣  Final status check..."
curl -s "$BASE_URL/status" | jq '.'
echo ""

# Test 7: Remove a domain
echo "7️⃣  Removing test domain..."
curl -s -X DELETE "$BASE_URL/domains/test1.example.com" | jq '.'
echo ""

# Test 8: Final domain list
echo "8️⃣  Final domain list..."
curl -s "$BASE_URL/domains" | jq '.'
echo ""

echo "✅ Testing completed!"
