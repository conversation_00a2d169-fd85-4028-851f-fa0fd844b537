#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.
set -u # Treat unset variables as an error when substituting.

echo "--- Host Environment Initialization Script for postfix-manager ---"
echo ""

# 1. Check for root privileges
echo "TASK: Checking for root privileges..."
if [[ "$(id -u)" -ne 0 ]]; then
  echo "  STATUS: FAILED"
  echo "  ERROR: This script must be run as root or with sudo."
  echo "  ACTION: Please try again using 'sudo $0'"
  exit 1
fi
echo "  STATUS: OK (Running as root)"
echo ""

# 2. Verify Postfix is installed
echo "TASK: Checking for Postfix installation..."
if ! command -v postfix &> /dev/null; then
  echo "  STATUS: FAILED"
  echo "  ERROR: Postfix command-line utility not found. Postfix appears to be not installed or not in PATH."
  echo "  ACTION: Please install Postfix on the host system (e.g., 'sudo apt update && sudo apt install postfix' on Debian/Ubuntu)."
  exit 1
fi
echo "  STATUS: OK (Postfix command found)"
echo ""

# 3. Verify postmap command is available
echo "TASK: Checking for 'postmap' command..."
if ! command -v postmap &> /dev/null; then
  echo "  STATUS: FAILED"
  echo "  ERROR: 'postmap' command not found."
  echo "  INFO: This command is crucial for managing Postfix lookup tables."
  echo "  ACTION: Ensure your Postfix installation is complete and 'postmap' is in the system's PATH. It's usually part of the core Postfix package."
  exit 1
fi
echo "  STATUS: OK ('postmap' command found and executable)"
echo ""

# 4. Verify systemctl (if used for Postfix management)
echo "TASK: Checking for 'systemctl' command (common Postfix service manager)..."
if command -v systemctl &> /dev/null; then
  echo "  STATUS: FOUND ('systemctl' command is available)"
  echo "  INFO: If postfix-manager uses systemctl to manage the Postfix service, this is a good sign."
  
  echo "  TASK: Checking if 'postfix.service' is recognized by systemctl..."
  # Use 'systemctl cat' to check for unit existence without relying on grep or active state
  if systemctl cat postfix.service &>/dev/null; then
    echo "    STATUS: 'postfix.service' unit found by systemctl."
    if systemctl is-active --quiet postfix.service; then
      echo "    INFO: Postfix service (postfix.service) is currently active."
    else
      echo "    INFO: Postfix service (postfix.service) is currently inactive or in another state according to systemctl."
    fi
  else
    echo "    STATUS: INFO - 'postfix.service' unit not found by 'systemctl cat'. This could mean:"
    echo "            1. Postfix is not yet (or not traditionally) set up as a systemd service named 'postfix.service'."
    echo "            2. The service has a different name on this system (e.g., <EMAIL> for multi-instance)."
    echo "    ACTION: If postfix-manager relies on 'systemctl' for a service specifically named 'postfix.service', ensure it is correctly set up."
  fi
else
  echo "  STATUS: WARNING - 'systemctl' command not found."
  echo "  INFO: 'systemctl' is a common tool for managing services on modern Linux systems."
  echo "  ACTION: If postfix-manager is intended to use 'systemctl' to manage the Postfix service, 'systemctl' must be installed."
  echo "          If Postfix is managed differently (e.g., using the 'postfix' command directly or via init scripts), ensure that method is functional and postfix-manager is configured accordingly."
fi
echo ""

# 5. Check for necessary Postfix configuration files
POSTFIX_CONFIG_DIR="/etc/postfix"
POSTFIX_MAIN_CF="${POSTFIX_CONFIG_DIR}/main.cf"

echo "TASK: Checking for Postfix configuration directory (${POSTFIX_CONFIG_DIR})..."
if [ ! -d "${POSTFIX_CONFIG_DIR}" ]; then
  echo "  STATUS: FAILED"
  echo "  ERROR: Postfix configuration directory '${POSTFIX_CONFIG_DIR}' not found."
  echo "  ACTION: Please ensure Postfix is properly installed. This directory is essential for Postfix operation."
  exit 1
fi
echo "  STATUS: OK (Directory ${POSTFIX_CONFIG_DIR} found)"
echo ""

echo "TASK: Checking for main Postfix configuration file (${POSTFIX_MAIN_CF})..."
if [ ! -f "${POSTFIX_MAIN_CF}" ]; then
  echo "  STATUS: FAILED"
  echo "  ERROR: Main Postfix configuration file '${POSTFIX_MAIN_CF}' not found."
  echo "  ACTION: Postfix may not be correctly installed or configured. This file is critical for Postfix and postfix-manager interaction."
  exit 1
fi
echo "  STATUS: OK (File ${POSTFIX_MAIN_CF} found)"
echo ""

echo "--- Host Environment Initialization Checks Completed ---"
echo "All critical prerequisite checks passed. The environment appears to be suitably prepared for basic postfix-manager operation."
echo "Please review any WARNINGS or INFO messages above and ensure they are acceptable for your specific setup and postfix-manager's requirements."
echo ""
echo "---------------------------------------------------------------------"
echo "IMPORTANT: POSTFIX CONFIGURATION BACKUP RECOMMENDED"
echo "---------------------------------------------------------------------"
echo "It is highly recommended to set up regular backups for your Postfix"
echo "configuration using the 'deploy/backup_postfix.sh' script."
echo "This script helps protect against accidental data loss or misconfiguration."
echo ""
echo "Before making significant changes to Postfix or after completing your"
echo "initial server setup, consider running a backup."
echo ""
echo "For setup instructions (including cron job automation), please refer to:"
echo "  - deploy/README.md (section 'Postfix Configuration Backup')"
echo "  - docs/deployment-best-practices.md"
echo "---------------------------------------------------------------------"
exit 0