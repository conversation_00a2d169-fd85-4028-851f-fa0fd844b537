-- Add support for test webhooks and webhook payload storage
-- This migration adds fields to support the test webhook functionality

-- Add webhook payload storage and test webhook flag to emails table
ALTER TABLE "emails" ADD COLUMN "webhook_payload" JSONB;
ALTER TABLE "emails" ADD COLUMN "is_test_webhook" BOOLEAN NOT NULL DEFAULT false;

-- Make domainId optional for test webhooks (they don't belong to a user domain)
ALTER TABLE "emails" ALTER COLUMN "domainId" DROP NOT NULL;

-- Add index for test webhook queries
CREATE INDEX "emails_is_test_webhook_idx" ON "emails"("is_test_webhook");

-- Add index for user ID suffix lookups (for performance)
CREATE INDEX "users_id_suffix_idx" ON "users"(RIGHT("id", 8));

-- Update the foreign key constraint to allow NULL domainId
ALTER TABLE "emails" DROP CONSTRAINT "emails_domainId_fkey";
ALTER TABLE "emails" ADD CONSTRAINT "emails_domainId_fkey" 
  FOREIGN KEY ("domainId") REFERENCES "domains"("id") ON DELETE CASCADE ON UPDATE CASCADE;
