-- Remove domain webhook reference and sync table
-- This migration removes the webhook sync complexity entirely

-- First, drop the webhook sync table (no longer needed)
DROP TABLE IF EXISTS "webhook_syncs";

-- Remove the foreign key constraint from domains to webhooks
ALTER TABLE "domains" DROP CONSTRAINT IF EXISTS "domains_webhookId_fkey";

-- Remove the webhookId column from domains
ALTER TABLE "domains" DROP COLUMN IF EXISTS "webhookId";

-- Note: We keep the webhook table and alias.webhookId as they're still needed
-- Domains will get their webhook info from their catch-all alias
