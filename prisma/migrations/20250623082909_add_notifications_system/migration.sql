-- Create<PERSON><PERSON>
CREATE TYPE "NotificationType" AS ENUM ('<PERSON><PERSON><PERSON>_LIMIT_REACHED', 'PLA<PERSON>_LIMIT_WARNING', 'EMAIL_QUOTA_EXHAUSTED', 'SYSTEM_ALERT', 'FEATURE_ANNOUNCEMENT', 'SECURITY_ALERT', 'PAYMENT_REMINDER', 'PAYMENT_FAILED', 'PAYMENT_SUCCESS', 'DOMAIN_VERIFIED', 'DOMAIN_FAILED', 'WEBHOOK_FAILED');

-- CreateEnum
CREATE TYPE "NotificationCategory" AS ENUM ('BILLING', 'SYSTEM', 'SECURITY', 'FEATURE', 'DOMAIN', 'WEBHOOK', 'PAYMENT');

-- CreateEnum
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- CreateTable
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "category" "NotificationCategory" NOT NULL,
    "priority" "NotificationPriority" NOT NULL DEFAULT 'MEDIUM',
    "data" JSONB,
    "actionUrl" TEXT,
    "actionText" TEXT,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "notifications_userId_isRead_idx" ON "notifications"("userId", "isRead");

-- CreateIndex
CREATE INDEX "notifications_userId_createdAt_idx" ON "notifications"("userId", "createdAt");

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
