-- CreateTable
CREATE TABLE "webhook_syncs" (
    "id" TEXT NOT NULL,
    "domainId" TEXT NOT NULL,
    "catchAllAliasId" TEXT NOT NULL,
    "webhookId" TEXT NOT NULL,
    "syncEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "webhook_syncs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "webhook_syncs_domainId_catchAllAliasId_key" ON "webhook_syncs"("domainId", "catchAllAliasId");

-- AddForeignKey
ALTER TABLE "webhook_syncs" ADD CONSTRAINT "webhook_syncs_domainId_fkey" FOREIGN KEY ("domainId") REFERENCES "domains"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "webhook_syncs" ADD CONSTRAINT "webhook_syncs_catchAllAliasId_fkey" FOREIGN KEY ("catchAllAliasId") REFERENCES "aliases"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "webhook_syncs" ADD CONSTRAINT "webhook_syncs_webhookId_fkey" FOREIGN KEY ("webhookId") REFERENCES "webhooks"("id") ON DELETE CASCADE ON UPDATE CASCADE;
