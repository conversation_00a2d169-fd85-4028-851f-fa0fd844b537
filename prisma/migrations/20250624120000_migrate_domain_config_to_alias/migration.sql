-- Migration: Move domain-level configuration to catch-all aliases
-- This migration moves allowAttachments and includeEnvelope settings from domains to their catch-all aliases

-- Update catch-all aliases to inherit configuration from their domains
UPDATE "aliases" 
SET "configuration" = COALESCE("aliases"."configuration", '{}'::jsonb) || 
    COALESCE("domains"."configuration", '{}'::jsonb)
FROM "domains"
WHERE "aliases"."domainId" = "domains"."id"
  AND "aliases"."email" LIKE '*@%'  -- catch-all aliases
  AND "domains"."configuration" IS NOT NULL
  AND "domains"."configuration" != '{}'::jsonb;

-- Clear domain-level configuration after migration
UPDATE "domains" 
SET "configuration" = NULL 
WHERE "configuration" IS NOT NULL;
