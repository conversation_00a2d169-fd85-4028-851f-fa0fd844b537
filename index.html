<!DOCTYPE html>
<html lang="en" data-theme="emerald">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/src/frontend/assets/images/favicon.ico" />
    <link rel="manifest" href="/src/frontend/assets/images/site.webmanifest" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="The easiest way to turn incoming emails into webhooks — 100% hosted in the EU, fully GDPR-compliant. Works with Zapier, Make, n8n, and your own stack." />
    <meta name="keywords" content="email, webhook, GDPR, EU, compliance, developer, API" />
    <title>EmailConnect.eu – Turn emails into webhooks, EU-based.</title>
    <meta property="og:title" content="EmailConnect.eu – Turn emails into webhooks, EU-based." />
    <meta property="og:description" content="The easiest way to turn incoming emails into webhooks — 100% hosted in the EU, fully GDPR-compliant. Works with Zapier, Make, n8n, and your own stack." />
    <meta property="og:image" content="https://emailconnect.eu/assets/og-image.png" />
    <meta property="og:url" content="https://emailconnect.eu" />
    <meta name="twitter:card" content="summary_large_image" />
    <link rel="apple-touch-icon" sizes="180x180" href="/src/frontend/assets/images/apple-touch-icon.png" />
    <link rel="icon" type="image/svg+xml" href="/src/frontend/assets/images/favicon.svg" />
    <link rel="icon" type="image/png" sizes="96x96" href="/src/frontend/assets/images/favicon-96x96.png" />
  
    <!-- Plausible Analytics -->
    <script defer data-domain="emailconnect.eu" src="https://plausible.io/js/script.hash.outbound-links.tagged-events.js"></script>
    <script>window.plausible = window.plausible || function() { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/frontend/main.ts"></script>
  </body>
</html>
