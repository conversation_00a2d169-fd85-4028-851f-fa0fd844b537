# Security Architecture

## Authentication System

### 🔐 **Secure Token Management**

Our application uses a **single, secure authentication method** to prevent security vulnerabilities:

#### **Primary Authentication: httpOnly Cookies**
- **Token Storage**: JWT tokens stored in `user_token` httpOnly cookie
- **Security Benefits**:
  - ✅ **XSS Protection**: JavaScript cannot access httpOnly cookies
  - ✅ **CSRF Protection**: SameSite=lax prevents cross-site requests
  - ✅ **Secure Transport**: Cookies only sent over HTTPS in production
  - ✅ **Automatic Expiry**: 7-day expiration with automatic cleanup

#### **Cookie Configuration**
```typescript
{
  path: '/',
  httpOnly: true,           // Prevents XSS access
  secure: NODE_ENV === 'production',  // HTTPS only in production
  sameSite: 'lax',         // CSRF protection
  maxAge: 7 * 24 * 60 * 60 * 1000  // 7 days
}
```

### 🚫 **What We DON'T Do (Security Anti-Patterns)**

- ❌ **No localStorage tokens**: Vulnerable to XSS attacks
- ❌ **No token exposure**: Tokens never returned in API responses
- ❌ **No mixed auth methods**: Single, consistent authentication approach
- ❌ **No client-side token handling**: All token management server-side

## Authentication Flow

### **Login/Register Process**
1. User submits credentials
2. Server validates credentials
3. Server generates JWT token
4. Server sets httpOnly cookie with token
5. Server returns success response (NO token in JSON)
6. Client redirects to dashboard

### **Authenticated Requests**
1. Browser automatically sends httpOnly cookie
2. Server extracts token from cookie
3. Server validates JWT token
4. Server processes request with user context

### **WebSocket Authentication**
1. WebSocket connection sends cookies automatically (`withCredentials: true`)
2. Server extracts `user_token` from cookie header
3. Server validates JWT token
4. WebSocket connection authenticated

### **Logout Process**
1. Client sends logout request
2. Server clears httpOnly cookie
3. Client redirects to login page

## API Authentication Methods

### **For Web Application**
- **Method**: httpOnly cookies
- **Usage**: All web UI interactions
- **Security**: Highest (XSS + CSRF protection)

### **For API Clients**
- **Method**: API Keys (`X-API-Key` header)
- **Usage**: External integrations, webhooks
- **Security**: High (no browser vulnerabilities)

### **For Development/Testing**
- **Method**: Bearer tokens (`Authorization: Bearer <token>`)
- **Usage**: API testing, development tools
- **Security**: Medium (requires secure handling)

## Security Headers

### **CORS Configuration**
```typescript
{
  origin: process.env.NODE_ENV === 'development' ? 
    ["http://localhost:3000", "http://localhost:5173"] : false,
  credentials: true  // Required for httpOnly cookies
}
```

### **Content Security Policy**
- Prevents XSS attacks
- Restricts script sources
- Blocks inline scripts

## Environment Security

### **Production**
- ✅ HTTPS enforced
- ✅ Secure cookies only
- ✅ CORS restricted to production domain
- ✅ Environment variables for secrets

### **Development**
- ✅ HTTP allowed for localhost
- ✅ Multiple localhost ports for dev servers
- ✅ Debug logging for troubleshooting
- ✅ Same security patterns as production

## Token Security

### **JWT Configuration**
- **Algorithm**: HS256 (HMAC with SHA-256)
- **Secret**: Strong random secret from environment
- **Expiry**: 7 days (configurable)
- **Payload**: Minimal (userId, email only)

### **Token Validation**
- ✅ Signature verification
- ✅ Expiration checking
- ✅ Issuer validation
- ✅ Payload structure validation

## Security Best Practices Implemented

1. **Defense in Depth**: Multiple security layers
2. **Principle of Least Privilege**: Minimal token payload
3. **Secure by Default**: httpOnly cookies as primary method
4. **Fail Securely**: Authentication failures redirect to login
5. **Input Validation**: All user inputs validated
6. **Error Handling**: No sensitive information in error messages
7. **Logging**: Security events logged for monitoring

## Monitoring & Alerting

### **Security Events Logged**
- Failed authentication attempts
- Token validation failures
- Suspicious request patterns
- CORS violations

### **Metrics Tracked**
- Authentication success/failure rates
- Token expiration patterns
- API usage by authentication method

## Compliance

This security architecture follows:
- ✅ **OWASP Top 10** security guidelines
- ✅ **JWT Best Practices** (RFC 8725)
- ✅ **Cookie Security** standards
- ✅ **CORS** security policies
