# Spam Filtering Setup Guide

This guide covers how to enable spam filtering functionality in your EU Email Webhook installation.

## Overview

The spam filtering feature uses SpamAssassin + <PERSON>avis to provide advanced spam detection for Pro and Enterprise users. Free users are unaffected and continue to receive all emails normally.

## Architecture

- **SpamAssassin**: Analyzes email content and assigns spam scores
- **Amavis**: Content filter that integrates SpamAssassin with Postfix
- **Transport Maps**: SQLite-based routing that conditionally sends emails through spam filtering
- **User Control**: Pro/Enterprise users can configure spam thresholds via web interface

## Installation Methods

### For NEW Installations

If you're setting up a fresh server, spam filtering is automatically included:

```bash
# Clone and run the setup script
<NAME_EMAIL>:xadi-hq/eu-email-webhook.git
cd eu-email-webhook
sudo ./deploy/setup-server.sh
```

The setup script automatically:
- Installs SpamAssassin and Amavis
- Configures all necessary services
- Sets up transport maps
- Enables spam filtering infrastructure

### For EXISTING Installations (Migration)

If you have an existing installation that needs spam filtering added:

#### Step 1: Backup Your System
```bash
# Create backups (automatic in migration script, but good practice)
sudo cp /etc/postfix/main.cf /etc/postfix/main.cf.backup
sudo cp /opt/eu-email-webhook/data/postfix.db /opt/eu-email-webhook/data/postfix.db.backup
```

#### Step 2: Run Migration Script
```bash
cd /opt/eu-email-webhook
sudo chmod +x scripts/migrations/migrate_spam_filtering.sh
sudo ./scripts/migrations/migrate_spam_filtering.sh
```

#### Step 3: Verify Installation
```bash
# Check service status
sudo systemctl status spamd
sudo systemctl status amavis
sudo systemctl status postfix

# Check database schema
sqlite3 /opt/eu-email-webhook/data/postfix.db ".schema virtual_domains"

# Test API endpoint
curl -X GET "http://localhost:3001/health"
```

#### Step 4: Restart Application
```bash
cd /opt/eu-email-webhook
docker compose -f docker-compose.prod.yml --env-file .env.prod restart
```

## What the Migration Does

The migration script performs these operations:

1. **System Packages**: Installs SpamAssassin and Amavis
2. **Service Configuration**: Configures SpamAssassin and Amavis with optimal settings
3. **Database Schema**: Adds spam filtering columns to virtual_domains table
4. **Transport Maps**: Creates SQLite query file for conditional email routing
5. **Postfix Configuration**: Updates main.cf and master.cf for Amavis integration
6. **Service Management**: Enables and starts all required services

## Database Changes

The migration adds these columns to the `virtual_domains` table:

```sql
ALTER TABLE virtual_domains ADD COLUMN spam_filtering BOOLEAN DEFAULT 0;
ALTER TABLE virtual_domains ADD COLUMN spam_threshold_green REAL DEFAULT 2.0;
ALTER TABLE virtual_domains ADD COLUMN spam_threshold_red REAL DEFAULT 5.0;
```

## Configuration Files Created/Modified

- `/etc/default/spamd` - SpamAssassin daemon configuration
- `/etc/amavis/conf.d/15-content_filter_mode` - Amavis spam checking settings
- `/etc/amavis/conf.d/50-user` - Amavis port configuration
- `/opt/eu-email-webhook/data/transport.cf` - Transport map queries
- `/etc/postfix/main.cf` - Transport maps configuration
- `/etc/postfix/master.cf` - Amavis integration

## Email Flow

### Without Spam Filtering (Free Users + Disabled Domains)
```
Internet → Postfix → /etc/aliases → process-email.js → Backend API
```

### With Spam Filtering (Pro/Enterprise Users)
```
Internet → Postfix → Transport Map Check → Amavis (127.0.0.1:10024) 
→ SpamAssassin → Amavis → Postfix (127.0.0.1:10025) 
→ /etc/aliases → process-email.js → Backend API
```

## User Interface

After installation, Pro and Enterprise users can:

1. **Enable/Disable**: Toggle spam filtering per domain
2. **Configure Thresholds**: Set green (pass) and red (spam) score levels
3. **View Spam Headers**: Webhook payloads include spam analysis when available

## API Endpoints

New endpoints for spam filtering management:

```bash
# Get spam filter settings for a domain
GET /api/domains/{domainId}/spam-filter

# Update spam filter settings
PUT /api/domains/{domainId}/spam-filter
{
  "enabled": true,
  "thresholdGreen": 2.0,
  "thresholdRed": 5.0
}
```

## Troubleshooting

### Check Service Status
```bash
sudo systemctl status spamd amavis postfix
```

### View Logs
```bash
# Application logs
docker compose -f /opt/eu-email-webhook/docker-compose.prod.yml logs -f

# System service logs
journalctl -u spamd -f
journalctl -u amavis -f
journalctl -u postfix -f
```

### Test Transport Maps
```bash
# Test transport map query
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT CASE WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024' ELSE NULL END 
   FROM virtual_domains WHERE domain='your-domain.com' AND active=1"
```

### Verify Database Schema
```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db ".schema virtual_domains"
```

## Rollback Instructions

If you need to disable spam filtering:

1. **Disable Transport Maps**:
   ```bash
   sudo postconf -e "transport_maps = "
   sudo systemctl reload postfix
   ```

2. **Stop Services**:
   ```bash
   sudo systemctl stop amavis spamd
   sudo systemctl disable amavis spamd
   ```

3. **Restore Database** (if needed):
   ```bash
   sudo cp /opt/eu-email-webhook/data/postfix.db.backup /opt/eu-email-webhook/data/postfix.db
   ```

## Testing

After completing the migration, test the spam filtering functionality:

### 1. **Check service status:**
```bash
sudo systemctl status spamd amavis postfix
```

### 2. **Enable spam filtering for a test domain:**
```bash
# Enable spam filtering for a specific domain
sqlite3 /opt/eu-email-webhook/data/postfix.db "UPDATE virtual_domains SET spam_filtering=1 WHERE domain='your-test-domain.com';"

# Verify the change
sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT domain, spam_filtering FROM virtual_domains WHERE domain='your-test-domain.com';"
```

### 3. **Test email processing:**
- Send a test email to the domain with spam filtering enabled
- Check webhook payload for spam headers:
  - `X-Spam-Status: No, score=-0.1`
  - `X-Spam-Score: -0.1`
  - `X-Spam-Level: `
  - `X-Spam-Checker-Version: SpamAssassin`

### 4. **Verify email routing:**
```bash
# Check if emails are being routed through Amavis
sudo tail -f /var/log/mail.log | grep -E "(amavis|spam)"
```

### 5. **Monitor logs:**
```bash
sudo journalctl -f -u postfix -u amavis -u spamd
```

### 6. **Test with spam content:**
Send an email with spam-like content (e.g., subject "URGENT: You've won $1,000,000!") to see higher spam scores.

**Note:** Spam filtering is disabled by default for all domains. Enable it per domain through the web interface (coming in Phase 3) or manually via database.

## Performance Considerations

- SpamAssassin is configured with max 5 child processes
- Transport maps only route emails for domains with spam filtering enabled
- Free users experience zero performance impact
- Database queries are optimized with proper indexing

## Security Notes

- Amavis runs on localhost ports only (10024/10025)
- SpamAssassin rules are automatically updated via cron
- All spam filtering is optional and user-controlled
- No emails are blocked by default - only headers are added

## Implementation Status

### ✅ **Phase 1 & 2: Backend Infrastructure (COMPLETE)**
- [x] SpamAssassin and Amavis installation and configuration
- [x] Database schema updates with `spam_filtering` column
- [x] Postfix transport maps for conditional routing
- [x] Migration scripts for existing installations
- [x] Updated deployment scripts for new installations
- [x] Comprehensive testing procedures

### ✅ **Phase 3: Frontend Implementation (COMPLETE)**
- [x] Vue.js components for spam filtering configuration (SpamSection.vue)
- [x] Domain management UI updates (integrated into domain forms)
- [x] User settings for spam thresholds (green/red threshold controls)
- [x] Spam filtering toggle per domain (working in domain edit modal)
- [x] API endpoints for spam filtering management (GET/PUT /domains/:id/spam)
- [x] API response schema validation (fixed configuration data flow)

### 📋 **Phase 4: Integration & Polish (PLANNED)**
- [ ] Webhook payload enhancements (spam score headers)
- [ ] Advanced spam filtering options (whitelist/blacklist)
- [ ] User documentation and help guides
- [ ] Spam filtering analytics and reporting

**Current Status:** Full spam filtering implementation is complete and functional. Users can configure spam filtering settings through the domain management UI, with plan-based access control enforced.
