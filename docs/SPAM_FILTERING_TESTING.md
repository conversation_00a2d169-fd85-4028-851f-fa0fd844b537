# Spam Filtering Testing Guide

This guide provides comprehensive testing procedures for the spam filtering functionality.

## Prerequisites

- Development environment running
- Test user accounts with different plan types (Free, Pro, Enterprise)
- Test domains configured
- Postman or curl for API testing

## Unit Tests

Run the automated test suite:

```bash
# Run spam filtering specific tests
npm test -- spam-filtering.test.ts

# Run all tests
npm test
```

## API Endpoint Testing

### 1. Test Spam Filter Settings Endpoints

#### Get Spam Filter Settings
```bash
# Replace {domainId} with actual domain ID
# Replace {authToken} with valid JWT token

curl -X GET "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {authToken}" \
  -H "Content-Type: application/json"

# Expected response for new domain:
{
  "enabled": false,
  "thresholdGreen": 2.0,
  "thresholdRed": 5.0
}
```

#### Update Spam Filter Settings (Pro/Enterprise only)
```bash
curl -X PUT "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {authToken}" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "thresholdGreen": 3.0,
    "thresholdRed": 6.0
  }'

# Expected response:
{
  "enabled": true,
  "thresholdGreen": 3.0,
  "thresholdRed": 6.0
}
```

#### Test Permission Validation
```bash
# Test with Free user (should fail)
curl -X PUT "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {freeUserToken}" \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}'

# Expected response: 403 Forbidden
{
  "error": "Insufficient permissions",
  "message": "This feature requires a Pro or Enterprise plan"
}
```

### 2. Test Input Validation

#### Invalid Threshold Values
```bash
# Test negative threshold
curl -X PUT "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {proUserToken}" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "thresholdGreen": -1,
    "thresholdRed": 5.0
  }'

# Expected: 400 Bad Request

# Test threshold too high
curl -X PUT "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {proUserToken}" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "thresholdGreen": 2.0,
    "thresholdRed": 25
  }'

# Expected: 400 Bad Request

# Test green > red
curl -X PUT "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {proUserToken}" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "thresholdGreen": 8.0,
    "thresholdRed": 5.0
  }'

# Expected: 400 Bad Request
```

## Go Service Testing

### 1. Test Postfix Manager Endpoints

#### Health Check
```bash
curl -X GET "http://localhost:3001/health"

# Expected response:
{
  "status": "healthy",
  "timestamp": "2025-01-XX...",
  "service": "Postfix Manager (SQLite)"
}
```

#### Update Domain Spam Filtering
```bash
curl -X PUT "http://localhost:3001/domains/test.example.com/spam-filter" \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}'

# Expected response:
{
  "success": true,
  "message": "Spam filtering enabled for domain test.example.com",
  "timestamp": "2025-01-XX..."
}
```

### 2. Test Database Operations

#### Verify Schema Changes
```bash
# Connect to SQLite database
sqlite3 /opt/eu-email-webhook/data/postfix.db

# Check table schema
.schema virtual_domains

# Should show spam filtering columns:
# spam_filtering BOOLEAN DEFAULT 0
# spam_threshold_green REAL DEFAULT 2.0  
# spam_threshold_red REAL DEFAULT 5.0

# Check indexes
.indexes virtual_domains

# Should include:
# idx_virtual_domains_spam_filtering
```

#### Test Transport Map Queries
```bash
# Test transport map query for enabled domain
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT CASE 
     WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024'
     ELSE NULL
   END FROM virtual_domains 
   WHERE domain='test.example.com' AND active=1"

# Should return: amavis:[127.0.0.1]:10024 (if enabled)
# Should return: (empty) (if disabled)
```

## Email Parser Testing

### 1. Test Spam Header Parsing

Create a test script to verify spam header parsing:

```javascript
// test-spam-parser.js
const { parseSpamHeaders } = require('./src/backend/services/email-parser');

// Test spam email headers
const spamHeaders = {
  'x-spam-status': 'Yes, score=7.5 required=5.0 tests=BAYES_99,HTML_MESSAGE',
  'x-spam-score': '7.5',
  'x-spam-level': '*******',
  'x-spam-report': 'Spam detection software identified this as spam',
  'x-spam-version': 'SpamAssassin 3.4.6'
};

console.log('Spam headers result:', parseSpamHeaders(spamHeaders));

// Test clean email headers
const cleanHeaders = {
  'x-spam-status': 'No, score=1.2 required=5.0 tests=BAYES_00',
  'x-spam-score': '1.2',
  'x-spam-level': '*'
};

console.log('Clean headers result:', parseSpamHeaders(cleanHeaders));

// Test no spam headers
const normalHeaders = {
  'from': '<EMAIL>',
  'to': '<EMAIL>'
};

console.log('Normal headers result:', parseSpamHeaders(normalHeaders));
```

Run the test:
```bash
node test-spam-parser.js
```

## Integration Testing

### 1. End-to-End Workflow Test

1. **Create Test Domain** (Pro user):
   ```bash
   curl -X POST "http://localhost:3000/api/domains" \
     -H "Authorization: Bearer {proUserToken}" \
     -d '{"domain": "spam-test.example.com"}'
   ```

2. **Enable Spam Filtering**:
   ```bash
   curl -X PUT "http://localhost:3000/api/domains/{domainId}/spam-filter" \
     -H "Authorization: Bearer {proUserToken}" \
     -d '{"enabled": true, "thresholdGreen": 2.0, "thresholdRed": 5.0}'
   ```

3. **Verify Database Update**:
   ```bash
   sqlite3 /opt/eu-email-webhook/data/postfix.db \
     "SELECT domain, spam_filtering, spam_threshold_green, spam_threshold_red 
      FROM virtual_domains WHERE domain='spam-test.example.com'"
   ```

4. **Test Transport Map**:
   ```bash
   sqlite3 /opt/eu-email-webhook/data/postfix.db \
     "SELECT CASE WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024' ELSE NULL END 
      FROM virtual_domains WHERE domain='spam-test.example.com' AND active=1"
   ```

### 2. Permission Testing Matrix

| User Plan  | Endpoint Access | Expected Result |
|------------|----------------|-----------------|
| Free       | GET spam-filter | ✅ 200 (read-only) |
| Free       | PUT spam-filter | ❌ 403 Forbidden |
| Pro        | GET spam-filter | ✅ 200 |
| Pro        | PUT spam-filter | ✅ 200 |
| Enterprise | GET spam-filter | ✅ 200 |
| Enterprise | PUT spam-filter | ✅ 200 |

## Performance Testing

### 1. Database Query Performance
```bash
# Test transport map query performance
time sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT CASE WHEN spam_filtering = 1 THEN 'amavis:[127.0.0.1]:10024' ELSE NULL END 
   FROM virtual_domains WHERE domain='test.example.com' AND active=1"

# Should complete in < 1ms with proper indexing
```

### 2. API Response Times
```bash
# Test API endpoint performance
time curl -X GET "http://localhost:3000/api/domains/{domainId}/spam-filter" \
  -H "Authorization: Bearer {token}"

# Should complete in < 100ms
```

## Troubleshooting Tests

### 1. Service Status Verification
```bash
# Check if services are running (after migration)
systemctl status spamassassin
systemctl status amavis
systemctl status postfix

# Check service logs
journalctl -u spamassassin --since "1 hour ago"
journalctl -u amavis --since "1 hour ago"
```

### 2. Configuration File Verification
```bash
# Check transport configuration
cat /opt/eu-email-webhook/data/transport.cf

# Test Postfix configuration
postfix check

# Test transport map lookup
postmap -q "test.example.com" sqlite:/opt/eu-email-webhook/data/transport.cf
```

## Expected Test Results

### Successful Test Indicators:
- ✅ All unit tests pass
- ✅ API endpoints respond correctly based on user permissions
- ✅ Database schema includes spam filtering columns
- ✅ Transport maps return correct routing decisions
- ✅ Email parser correctly identifies and parses spam headers
- ✅ Go service endpoints function properly
- ✅ Permission validation works as expected

### Common Issues and Solutions:
- **403 Forbidden**: Check user plan and permissions
- **Database errors**: Verify migration ran successfully
- **Transport map issues**: Check SQLite file permissions and query syntax
- **Service failures**: Verify SpamAssassin/Amavis installation and configuration
