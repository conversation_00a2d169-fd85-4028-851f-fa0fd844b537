# API Key Scoping Implementation

This document outlines the implementation of scoped API keys for the EmailConnect application.

## Overview

API key scoping restricts the operations that can be performed with each API key, providing granular access control for different use cases.

## Available Scopes

### Domain Scopes
- `domains:read` - Read domain information (GET /api/domains, GET /api/domains/{id})
- `domains:write` - Create and delete domains (POST /api/domains, DELETE /api/domains/{id})
- `domains:config` - Update domain configuration only (PUT /api/domains/{id} for allowAttachments/includeEnvelopeData)
- `domains:status` - Check domain verification status (GET /api/domains/{id}/status)
- `domains:*` - Full access to domain endpoints

### Alias Scopes
- `aliases:read` - Read alias information
- `aliases:write` - Create, update, and delete aliases
- `aliases:*` - Full access to alias endpoints

### Webhook Scopes
- `webhooks:read` - Read webhook information
- `webhooks:write` - Create, update, and delete webhooks
- `webhooks:*` - Full access to webhook endpoints

### Special Scopes
- `*` - Full access to all API endpoints

## Preset Configurations

### Full Access
- Scopes: `["*"]`
- Description: Complete access to all API endpoints

### Read Only
- Scopes: `["domains:read", "domains:status", "aliases:read", "webhooks:read"]`
- Description: Read-only access to all resources

### API User (Limited)
- Scopes: `["domains:read", "domains:status", "domains:config", "aliases:*", "webhooks:*"]`
- Description: Limited domain access (read/config only) + full alias/webhook access

## Implementation Details

### Database Changes
- Added `scopes` JSONB field to `api_keys` table
- Added optional `description` field to `api_keys` table

### Backend Changes
1. **ScopeValidatorService** - Validates API requests against scopes
2. **Enhanced ApiKeyService** - Handles scope validation during key generation
3. **Updated Auth Middleware** - Validates scopes for each request
4. **New API Endpoints** - Get available scopes and presets

### Frontend Changes
- Updated ApiKeysSection.vue with scope selection UI
- Preset and custom scope configuration options
- Visual scope indicators with color-coded badges

## Usage Examples

### Creating a Read-Only API Key
```bash
curl -X POST /api/api-keys \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your-existing-key" \
  -d '{
    "name": "Read Only Key",
    "scopes": ["domains:read", "domains:status", "aliases:read", "webhooks:read"],
    "description": "For monitoring and reporting"
  }'
```

### Creating an API User Key (Your Requested Scope)
```bash
curl -X POST /api/api-keys \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: your-existing-key" \
  -d '{
    "name": "API User Key",
    "scopes": ["domains:read", "domains:status", "domains:config", "aliases:*", "webhooks:*"],
    "description": "Limited domain access + full alias/webhook management"
  }'
```

## Migration

Run the following SQL to add scopes to existing API keys:

```sql
-- Add columns to api_keys table
ALTER TABLE "api_keys" 
ADD COLUMN "description" TEXT,
ADD COLUMN "scopes" JSONB NOT NULL DEFAULT '["*"]';
```

Existing API keys will automatically get full access (`["*"]`) scope.

## Testing

To test the scoping system:

1. Create an API key with limited scopes
2. Try accessing endpoints outside the allowed scopes
3. Verify you receive 403 Forbidden responses

## Security Notes

- Scopes are validated on every API request
- The `domains:config` scope only allows updating `allowAttachments` and `includeEnvelopeData` fields
- Invalid scope attempts are logged for security monitoring
- API keys cannot escalate their own permissions

## Next Steps

1. Run the migration: `npx prisma migrate dev`
2. Restart the application
3. Test the new scoping functionality in the UI
4. Create API keys with appropriate scopes for your use case
