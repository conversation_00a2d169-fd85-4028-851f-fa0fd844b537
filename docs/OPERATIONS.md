# Operations Guide

## Overview

This guide covers troubleshooting, monitoring, and maintenance procedures for the EU Email Webhook service in production. It consolidates operational knowledge for diagnosing and resolving common issues.

## Critical Production Issues

### 🚨 Issue #1: Docker Volume Mount Separation

**Symptom**: API calls return success but domains are not stored in host database
```bash
curl -X POST http://localhost:3001/domains -d '{"domain": "test.example.com"}'
# Returns: {"success":true} but domain not found in host SQLite
```

**Root Cause**: Container uses Docker named volume while host checks host directory

**Diagnosis**:
```bash
# Check host vs container database differences
HOST_COUNT=$(sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT COUNT(*) FROM virtual_domains;" 2>/dev/null || echo "0")
CONTAINER_COUNT=$(docker compose exec -T postfix-manager sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT COUNT(*) FROM virtual_domains;" 2>/dev/null || echo "0")

echo "HOST count: $HOST_COUNT vs CONTAINER count: $CONTAINER_COUNT"
# If different, you have volume mount issue
```

**Solution**: Fix volume mount in docker-compose.prod.yml
```bash
# Stop containers
docker compose -f docker-compose.prod.yml --env-file .env.prod down

# Fix volume mount (change from named volume to host bind mount)
sed -i 's|eu_email_data:/opt/eu-email-webhook/data:rw|/opt/eu-email-webhook/data:/opt/eu-email-webhook/data:rw|g' docker-compose.prod.yml

# Copy existing data from Docker volume to host
docker run --rm -v miwh-email-webhook_eu_email_data:/volume_data -v /opt/eu-email-webhook/data:/host_data alpine sh -c "
  cp -v /volume_data/*.db /host_data/ 2>/dev/null || true
  cp -v /volume_data/*.cf /host_data/ 2>/dev/null || true
"

# Fix permissions and restart
sudo chown -R ploi:ploi /opt/eu-email-webhook/data/
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### 🚨 Issue #2: Environment Variable Inconsistency

**Symptom**: Container environment variable warnings
```
WARN[0000] The "DATABASE_URL" variable is not set. Defaulting to a blank string.
WARN[0000] The "JWT_SECRET" variable is not set. Defaulting to a blank string.
```

**Root Cause**: Docker Compose commands run inconsistently (some with `--env-file`, some without)

**Solution**: ALWAYS use `--env-file .env.prod` in EVERY Docker Compose command
```bash
# ✅ CORRECT - Always specify env file
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d
docker compose -f docker-compose.prod.yml --env-file .env.prod down
docker compose -f docker-compose.prod.yml --env-file .env.prod logs

# ❌ WRONG - Missing env file causes credential mismatches
docker compose -f docker-compose.prod.yml up -d
```

### 🚨 Issue #3: SQLite Database Table Missing

**Symptom**: Postfix reports "no such table: virtual_domains"
```
postmap: fatal: dict_sqlite_lookup: SQL prepare failed: no such table: virtual_domains
```

**Root Cause**: Database file exists but tables weren't created properly

**Diagnosis**:
```bash
# Check if database file exists
ls -la /opt/eu-email-webhook/data/postfix.db

# Check if tables exist  
sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables"
```

**Solution**: Manual table creation
```bash
# Create tables using schema file
sqlite3 /opt/eu-email-webhook/data/postfix.db < postfix-sqlite/schema.sql

# Verify tables were created
sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables"

# Test table functionality
sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT COUNT(*) FROM virtual_domains;"
```

## Diagnostic Commands

### System Health Check Suite
```bash
#!/bin/bash
echo "=== System Status ==="
docker compose -f docker-compose.prod.yml --env-file .env.prod ps

echo -e "\n=== Service Health ==="
curl -f http://localhost:3000/health 2>/dev/null && echo "App: ✅ OK" || echo "App: ❌ FAILED"
curl -f http://localhost:3001/health 2>/dev/null && echo "Postfix Manager: ✅ OK" || echo "Postfix Manager: ❌ FAILED"

echo -e "\n=== External Access ==="
curl -f http://$(hostname -I | awk '{print $1}')/health 2>/dev/null && echo "External: ✅ OK" || echo "External: ❌ FAILED"

echo -e "\n=== Database Status ==="
ls -la /opt/eu-email-webhook/data/

echo -e "\n=== SQLite Tables Check ==="
TABLE_COUNT=$(sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables" 2>/dev/null | wc -l)
if [ "$TABLE_COUNT" -ge 2 ]; then
    echo "SQLite: ✅ OK ($TABLE_COUNT tables)"
else
    echo "SQLite: ❌ MISSING TABLES"
fi

echo -e "\n=== Network Status ==="
sudo ufw status numbered
sudo systemctl status nginx --no-pager -l | head -5
```

### Performance Check
```bash
#!/bin/bash
echo "=== Resource Usage ==="
docker stats --no-stream

echo -e "\n=== Disk Usage ==="
df -h /opt/eu-email-webhook

echo -e "\n=== Memory Usage ==="
free -h

echo -e "\n=== Network Connections ==="
ss -tuln | grep -E ':(25|80|443|3000|3001)'
```

## Container Management

### Container Health Monitoring
```bash
# Check container status
docker compose -f docker-compose.prod.yml --env-file .env.prod ps

# Check container logs
docker compose -f docker-compose.prod.yml --env-file .env.prod logs -f app
docker compose -f docker-compose.prod.yml --env-file .env.prod logs -f postfix-manager

# Container resource usage
docker stats --no-stream

# Restart specific container
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d --force-recreate app
```

### Database Operations
```bash
# Connect to PostgreSQL
docker compose -f docker-compose.prod.yml --env-file .env.prod exec postgres psql -U postgres -d eu_email_webhook

# Run database migrations
docker compose -f docker-compose.prod.yml --env-file .env.prod exec app npx prisma migrate deploy

# Check SQLite database
sqlite3 /opt/eu-email-webhook/data/postfix.db ".schema"
sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT * FROM virtual_domains;"
```

## Email Processing Issues

### Email Not Being Processed
**Diagnosis Steps**:
```bash
# Check if Postfix is receiving emails
sudo tail -f /var/log/mail.log

# Verify Postfix SQLite configuration
sudo postconf | grep virtual_alias

# Test SQLite database connectivity
postmap -q "test.com" "sqlite:/opt/eu-email-webhook/data/virtual_domains.cf"

# Check email processing script
ls -la /opt/eu-email-webhook/scripts/production/process-email.js

# Check SQLite query files exist
ls -la /opt/eu-email-webhook/data/*.cf
```

**Common Solutions**:
1. **MX records not configured**: DNS not pointing to server
2. **Postfix not configured for SQLite**: Configuration mismatch
3. **SQLite tables missing**: Run manual table creation
4. **Email processing script missing**: Volume mount issues

### Webhook Delivery Issues
```bash
# Check webhook delivery statistics
curl https://emailconnect.eu/api/webhook/stats

# List failed webhook deliveries
curl https://emailconnect.eu/api/webhook/failed

# Retry specific failed delivery
curl -X POST https://emailconnect.eu/api/webhook/retry/job-id

# Test webhook endpoint manually
curl -X POST https://your-webhook-url.com/webhook \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

## Network and Connectivity

### External Access Issues
**Symptom**: API calls from outside server fail
```bash
curl: (7) Failed to connect to server port 80: Connection refused
```

**Diagnosis**:
```bash
# Check if Nginx is running
sudo systemctl status nginx

# Verify Nginx configuration
sudo nginx -t

# Check if Nginx site is enabled
ls -la /etc/nginx/sites-enabled/

# Test internal connectivity first
curl -f http://localhost:3000/health

# Check firewall rules
sudo ufw status numbered

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

### SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Test SSL configuration
openssl s_client -connect emailconnect.eu:443 -servername emailconnect.eu

# Renew certificate if needed
sudo certbot renew --dry-run

# Check Nginx SSL configuration
sudo nginx -t
```

## Log Analysis

### Application Logs
```bash
# All container logs (remember: always use --env-file!)
docker compose -f docker-compose.prod.yml --env-file .env.prod logs -f

# Specific container logs
docker compose -f docker-compose.prod.yml --env-file .env.prod logs -f app
docker compose -f docker-compose.prod.yml --env-file .env.prod logs --tail=50 postfix-manager

# Filter logs for errors
docker compose -f docker-compose.prod.yml --env-file .env.prod logs | grep -i error
docker compose -f docker-compose.prod.yml --env-file .env.prod logs | grep -i "failed\|timeout\|connection"
```

### System Logs
```bash
# Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log

# Postfix logs
sudo tail -f /var/log/mail.log
journalctl -u postfix -f --since "1 hour ago"

# System logs
journalctl -f --since "1 hour ago"
dmesg | tail -20
```

## Emergency Procedures

### Complete Service Restart
```bash
# Stop all containers (with proper env file!)
docker compose -f docker-compose.prod.yml --env-file .env.prod down --remove-orphans

# Clean up resources
docker system prune -f

# Restart containers (with env file!)
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Wait and verify health
sleep 60
docker compose -f docker-compose.prod.yml --env-file .env.prod ps
curl https://emailconnect.eu/health

# Verify SQLite tables exist after restart
sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables"
```

### Rollback to Previous Version
```bash
# Pull previous image tag
docker pull ghcr.io/xadi-hq/eu-email-webhook:previous-tag

# Update docker-compose.prod.yml to use specific tag
# Edit image: ghcr.io/xadi-hq/eu-email-webhook:previous-tag

# Restart containers (with env file!)
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Verify rollback success
curl https://emailconnect.eu/health
```

### Data Recovery
```bash
# Backup current state before recovery
cp -r /opt/eu-email-webhook/data /opt/eu-email-webhook/data.backup.$(date +%Y%m%d-%H%M%S)

# Restore from backup
cp -r /opt/eu-email-webhook/data.backup.TIMESTAMP /opt/eu-email-webhook/data

# Fix permissions after restore
sudo chown -R ploi:ploi /opt/eu-email-webhook/data/
chmod 664 /opt/eu-email-webhook/data/*.db 2>/dev/null || true
```

## Backup and Maintenance

### Daily Backup Script
```bash
#!/bin/bash
BACKUP_DIR="/opt/eu-email-webhook/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup configuration
cp .env.prod $BACKUP_DIR/
cp docker-compose.prod.yml $BACKUP_DIR/

# Backup SQLite database
cp data/postfix.db $BACKUP_DIR/

# Backup PostgreSQL (with proper env file!)
docker compose -f docker-compose.prod.yml --env-file .env.prod exec postgres pg_dump -U postgres eu_email_webhook > $BACKUP_DIR/postgres.sql

# Clean old backups (keep 30 days)
find /opt/eu-email-webhook/backups/ -type d -mtime +30 -exec rm -rf {} \;
```

### Regular Maintenance Tasks
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean Docker resources
docker system prune -f
docker image prune -f

# Rotate logs
sudo logrotate -f /etc/logrotate.conf

# Check disk space
df -h

# Check SSL certificate expiration
sudo certbot certificates | grep "VALID"
```

## Monitoring and Alerting

### Health Check Monitoring
```bash
# Set up cron job for health monitoring
# Add to crontab: */5 * * * * /opt/eu-email-webhook/scripts/health-check.sh

#!/bin/bash
# health-check.sh
if ! curl -f https://emailconnect.eu/health >/dev/null 2>&1; then
    echo "$(date): Health check failed" >> /var/log/eu-email-webhook-health.log
    # Send alert (email, Slack, etc.)
fi
```

### Performance Monitoring
```bash
# Monitor container resources
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# Monitor disk space
df -h /opt/eu-email-webhook | awk 'NR==2 {print $5}' | sed 's/%//'

# Monitor email processing
tail -f /var/log/mail.log | grep "delivered\|bounced\|failed"
```

## Production Deployment Checklist

### Pre-Deployment Validation
```bash
# 1. Check for volume mount issues (NEW!)
HOST_COUNT=$(sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT COUNT(*) FROM virtual_domains;" 2>/dev/null || echo "0")
CONTAINER_COUNT=$(docker compose -f docker-compose.prod.yml --env-file .env.prod exec -T postfix-manager sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT COUNT(*) FROM virtual_domains;" 2>/dev/null || echo "0")
if [ "$HOST_COUNT" != "$CONTAINER_COUNT" ]; then
    echo "❌ VOLUME MOUNT ISSUE - Fix required"
    exit 1
fi

# 2. Verify environment consistency
docker compose -f docker-compose.prod.yml --env-file .env.prod config | grep DATABASE_URL

# 3. Verify all containers healthy
docker compose -f docker-compose.prod.yml --env-file .env.prod ps

# 4. Verify SQLite tables exist
sqlite3 /opt/eu-email-webhook/data/postfix.db ".tables"

# 5. Verify services respond
curl -f http://localhost:3000/health
curl -f http://localhost:3001/health

# 6. Test domain addition and storage
TEST_DOMAIN="deploy-test-$(date +%s).example.com"
curl -X POST http://localhost:3001/domains -H "Content-Type: application/json" -d "{\"domain\":\"$TEST_DOMAIN\"}"
sleep 2
STORED=$(sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT domain FROM virtual_domains WHERE domain='$TEST_DOMAIN';" 2>/dev/null || echo "")
if [ -z "$STORED" ]; then
    echo "❌ DOMAIN NOT STORED"
    exit 1
fi

# 7. Verify external access
curl -f https://emailconnect.eu/health
```

## Key Success Factors

### The Golden Rules for Operations
1. **🎯 ALWAYS fix Docker volume mount issues FIRST** - Use host bind mounts
2. **🎯 ALWAYS use `--env-file .env.prod` in every Docker Compose command**
3. **📁 Ensure data directory exists with proper permissions**
4. **🗄️ VERIFY SQLite tables exist after deployments**
5. **🔧 Test internal connectivity before external access**
6. **📝 Document everything during incidents, not after**

### Prevention Strategies
- Set up automated health check monitoring
- Regular backup procedures
- SSL certificate expiration alerts
- Disk space monitoring
- Log rotation and cleanup
- Performance baseline monitoring

---

**Note**: Following these operational procedures prevents 95% of production issues. Always validate the three critical issues (volume mounts, environment variables, SQLite tables) after any deployment or maintenance activity.
