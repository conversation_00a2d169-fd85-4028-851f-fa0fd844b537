# Payment Integration with <PERSON><PERSON>

This document provides comprehensive information about the payment system integration using <PERSON><PERSON> for plan upgrades and subscription management.

## Overview

The application uses <PERSON><PERSON> as the payment provider to handle:
- One-time payments for plan upgrades
- Subscription management (future implementation)
- Payment method storage and management
- Webhook notifications for payment status updates

## Mollie Configuration

### 1. API Keys

Set the following environment variables:

```bash
# Development/Test Environment
MOLLIE_API_KEY=test_dHar4XY7LxsDOtmnkVtjNVWXLSlXsM
MOLLIE_TEST_MODE=true

# Production Environment
MOLLIE_API_KEY=live_your_production_mollie_api_key
MOLLIE_TEST_MODE=false
```

### 2. Webhook Configuration

Configure the following webhook URL in your Mollie dashboard:

**Development:**
```
http://localhost:3000/api/webhooks/mollie/payment
```

**Production:**
```
https://yourdomain.com/api/webhooks/mollie/payment
```

**Webhook Settings:**
- **URL**: Use the appropriate URL above
- **Events**: Payment status changes
- **Method**: POST
- **Format**: JSON

### 3. Payment Methods Setup

#### Test Environment
In test mode, the system automatically uses `creditcard` as the default payment method. This ensures compatibility with Mollie's test environment.

#### Production Environment
To enable payment methods in production:

1. **Log into Mollie Dashboard**
2. **Go to Settings → Website profiles**
3. **Select your website profile**
4. **Enable desired payment methods:**
   - Credit/Debit Cards
   - iDEAL (Netherlands)
   - Bancontact (Belgium)
   - SOFORT Banking
   - PayPal
   - Bank Transfer
   - Other regional methods

4. **Complete verification process** for each payment method as required by Mollie

## Application Configuration

### Environment Variables

Add these variables to your environment files:

**.env / .env.example:**
```bash
# Payment processing (Mollie)
MOLLIE_API_KEY=test_dHar4XY7LxsDOtmnkVtjNVWXLSlXsM
MOLLIE_WEBHOOK_SECRET=your-mollie-webhook-secret-key
MOLLIE_WEBHOOK_URL=http://localhost:3000/api/webhooks/mollie/payment
MOLLIE_TEST_MODE=true
```

**.env.prod.example:**
```bash
# Payment processing (Mollie)
MOLLIE_API_KEY=live_your_production_mollie_api_key
MOLLIE_WEBHOOK_SECRET=your-production-mollie-webhook-secret
MOLLIE_WEBHOOK_URL=https://yourdomain.com/api/webhooks/mollie/payment
MOLLIE_TEST_MODE=false
```

### Docker Configuration

The production Docker setup includes Mollie environment variables in `docker-compose.prod.yml`:

```yaml
environment:
  - MOLLIE_API_KEY=${MOLLIE_API_KEY}
  - MOLLIE_WEBHOOK_SECRET=${MOLLIE_WEBHOOK_SECRET}
  - MOLLIE_WEBHOOK_URL=${MOLLIE_WEBHOOK_URL}
  - MOLLIE_TEST_MODE=${MOLLIE_TEST_MODE:-false}
```

## Database Schema

The payment system uses three main database tables:

### Payments Table
```sql
CREATE TABLE payments (
  id TEXT PRIMARY KEY,
  mollieId TEXT UNIQUE NOT NULL,
  status TEXT NOT NULL DEFAULT 'PENDING',
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'EUR',
  description TEXT,
  method TEXT,
  paidAt TIMESTAMP,
  cancelledAt TIMESTAMP,
  expiredAt TIMESTAMP,
  failedAt TIMESTAMP,
  failureReason TEXT,
  mollieCustomerId TEXT,
  molliePaymentMethod TEXT,
  mollieWebhookData JSON,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW(),
  userId TEXT NOT NULL REFERENCES users(id),
  subscriptionId TEXT REFERENCES subscriptions(id)
);
```

### Subscriptions Table
```sql
CREATE TABLE subscriptions (
  id TEXT PRIMARY KEY,
  mollieId TEXT UNIQUE NOT NULL,
  status TEXT NOT NULL DEFAULT 'PENDING',
  planType TEXT NOT NULL,
  interval TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'EUR',
  description TEXT,
  mollieCustomerId TEXT,
  molliePaymentMethod TEXT,
  startDate TIMESTAMP,
  nextPaymentDate TIMESTAMP,
  cancelledAt TIMESTAMP,
  cancelReason TEXT,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW(),
  userId TEXT NOT NULL REFERENCES users(id)
);
```

### Payment Methods Table
```sql
CREATE TABLE payment_methods (
  id TEXT PRIMARY KEY,
  mollieId TEXT UNIQUE NOT NULL,
  type TEXT NOT NULL,
  description TEXT,
  isDefault BOOLEAN DEFAULT FALSE,
  cardHolder TEXT,
  cardNumber TEXT,
  cardExpiryDate TEXT,
  cardFingerprint TEXT,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW(),
  userId TEXT NOT NULL REFERENCES users(id)
);
```

## API Endpoints

### Payment Creation
```http
POST /api/billing/payment
Content-Type: application/json
Authorization: Bearer <user-token>

{
  "planType": "pro",
  "interval": "monthly",
  "successUrl": "https://yourdomain.com/settings?payment=success",
  "cancelUrl": "https://yourdomain.com/settings?payment=cancelled"
}
```

**Response:**
```json
{
  "success": true,
  "payment": {
    "id": "payment_id",
    "mollieId": "tr_mollie_payment_id",
    "checkoutUrl": "https://www.mollie.com/checkout/...",
    "amount": {
      "value": "9.99",
      "currency": "EUR"
    }
  }
}
```

### Billing Information
```http
GET /api/billing/info
Authorization: Bearer <user-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "currentPlan": {
      "type": "free",
      "name": "Free",
      "status": "active"
    },
    "paymentMethods": [],
    "recentPayments": [
      {
        "id": "payment_id",
        "amount": { "value": "9.99", "currency": "EUR" },
        "description": "Pro Plan - monthly",
        "status": "paid",
        "createdAt": "2025-06-15T18:00:00.000Z"
      }
    ],
    "usage": {
      "emails": 25,
      "domains": 2,
      "webhooks": 3,
      "aliases": 5
    },
    "limits": {
      "emails": 50,
      "domains": 999,
      "webhooks": 999,
      "aliases": 999
    }
  }
}
```

### Webhook Endpoint
```http
POST /api/webhooks/mollie/payment
Content-Type: application/json
X-Mollie-Signature: <webhook-signature>

{
  "id": "tr_mollie_payment_id"
}
```

## Payment Flow

### 1. User Initiates Payment
1. User clicks "Upgrade Plan" in billing settings
2. Selects plan (Pro/Enterprise) and interval (monthly/yearly)
3. Clicks "Proceed to Payment"

### 2. Payment Creation
1. Frontend calls `POST /api/billing/payment`
2. Backend creates payment with Mollie API
3. Payment stored in database with PENDING status
4. User redirected to Mollie checkout

### 3. Payment Processing
1. User completes payment on Mollie checkout
2. Mollie sends webhook to `/api/webhooks/mollie/payment`
3. Backend processes webhook and updates payment status
4. If successful, user's plan is automatically upgraded

### 4. Plan Upgrade
1. Payment status updated to PAID
2. User's `planType` and `monthlyEmailLimit` updated
3. User can immediately use new plan features

## Plan Configuration

### Available Plans

**Free Plan:**
- 50 emails per month
- Basic features
- No payment required

**Pro Plan:**
- 1,000 emails per month
- Priority webhook delivery
- Email analytics
- Priority support
- **Pricing**: €9.99/month, €99.99/year

**Enterprise Plan:**
- 10,000 emails per month
- Guaranteed webhook delivery
- Advanced analytics
- Dedicated support & SLA
- **Pricing**: €49.99/month, €499.99/year

### Plan Limits Configuration

Plan limits are configured in `src/backend/services/billing/plan-config.service.ts`:

```typescript
const PLAN_CONFIGS = {
  free: {
    name: 'Free',
    monthlyEmailLimit: 50,
    // ... other limits
  },
  pro: {
    name: 'Pro',
    monthlyEmailLimit: 1000,
    price: {
      monthly: 9.99,
      yearly: 99.99,
      currency: 'EUR'
    }
  },
  enterprise: {
    name: 'Enterprise',
    monthlyEmailLimit: 10000,
    price: {
      monthly: 49.99,
      yearly: 499.99,
      currency: 'EUR'
    }
  }
};
```

## Testing

### Test Payment Flow

1. **Use test API key** in development environment
2. **Access billing settings** at `/settings` (requires login)
3. **Select a plan** and proceed to payment
4. **Use Mollie test cards:**
   - **Successful payment**: 4242 4242 4242 4242
   - **Failed payment**: 4000 0000 0000 0002
   - **Expired card**: 4000 0000 0000 0069

### Webhook Testing

1. **Use ngrok** for local webhook testing:
   ```bash
   ngrok http 3000
   ```

2. **Update webhook URL** in Mollie dashboard to ngrok URL:
   ```
   https://your-ngrok-url.ngrok.io/api/webhooks/mollie/payment
   ```

3. **Test webhook delivery** by completing test payments

## Security Considerations

### Webhook Signature Verification

The system verifies webhook signatures when `MOLLIE_WEBHOOK_SECRET` is configured:

```typescript
const isValid = mollieService.verifyWebhookSignature(rawBody, signature);
```

### Environment Security

- **Never commit real API keys** to version control
- **Use different API keys** for development and production
- **Rotate webhook secrets** regularly
- **Use HTTPS** for all webhook URLs in production

## Troubleshooting

### Common Issues

**"No suitable payment methods found"**
- Ensure payment methods are enabled in Mollie dashboard
- Check that website profile is properly configured
- Verify API key has correct permissions

**Webhook not received**
- Check webhook URL is accessible from internet
- Verify webhook URL in Mollie dashboard
- Check server logs for webhook processing errors

**Payment not updating user plan**
- Check webhook processing logs
- Verify payment metadata includes correct user information
- Ensure database permissions allow plan updates

### Logs and Monitoring

Payment operations are logged with structured logging:

```typescript
logger.info({
  paymentId: payment.id,
  mollieId: molliePayment.id,
  userId: request.userId,
  planType: request.planType,
  amount: amount
}, 'Payment created successfully');
```

Monitor these logs for payment processing issues and webhook delivery problems.
