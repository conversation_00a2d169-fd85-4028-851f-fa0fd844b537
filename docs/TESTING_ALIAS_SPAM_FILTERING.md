# Testing Alias-Based Spam Filtering

This document describes how to test and validate the new alias-based spam filtering implementation.

## Overview

The new alias-based spam filtering approach routes emails based on user plan types:
- **Free users**: `process-email` (direct processing, no spam filtering)
- **Pro+ users**: `spam-process-email` (SpamAssassin processing via spamc)

## Testing Methods

### 1. Quick Validation

For basic configuration checks:

```bash
cd /opt/eu-email-webhook
sudo ./scripts/test-alias-spam-filtering.sh --quick
```

This performs basic checks:
- ✅ spam-process-email script exists and is executable
- ✅ spam-process-email alias is configured in /etc/aliases
- ✅ virtual_aliases.cf has correct query
- ✅ SQLite database is accessible
- ✅ SpamAssassin service is running

### 2. Comprehensive Testing

For full functional testing:

```bash
cd /opt/eu-email-webhook
sudo ./scripts/test-alias-spam-filtering.sh
```

This performs comprehensive tests:
- Database connectivity and schema validation
- Postfix-manager service communication
- Email routing for free vs pro users
- Plan change synchronization
- SpamAssassin integration
- Cross-contamination prevention

## Manual Testing

### Test Free User Email Flow

1. **Create a test domain for free user**:
```bash
curl -X POST http://localhost:3001/domains \
  -H "Content-Type: application/json" \
  -d '{"domain": "test-free.example.com", "planType": "free"}'
```

2. **Verify routing**:
```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT email, destination FROM virtual_aliases WHERE domain='test-free.example.com';"
```
Expected: `destination` should be `process-email`

3. **Test Postfix lookup**:
```bash
postmap -q "@test-free.example.com" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf
```
Expected output: `process-email`

### Test Pro User Email Flow

1. **Create a test domain for pro user**:
```bash
curl -X POST http://localhost:3001/domains \
  -H "Content-Type: application/json" \
  -d '{"domain": "test-pro.example.com", "planType": "pro"}'
```

2. **Verify routing**:
```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT email, destination FROM virtual_aliases WHERE domain='test-pro.example.com';"
```
Expected: `destination` should be `spam-process-email`

3. **Test Postfix lookup**:
```bash
postmap -q "@test-pro.example.com" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf
```
Expected output: `spam-process-email`

### Test Plan Changes

1. **Upgrade free user to pro**:
```bash
curl -X PUT http://localhost:3001/domains/test-free.example.com/plan \
  -H "Content-Type: application/json" \
  -d '{"planType": "pro"}'
```

2. **Verify routing updated**:
```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT email, destination FROM virtual_aliases WHERE domain='test-free.example.com';"
```
Expected: `destination` should now be `spam-process-email`

## Monitoring and Debugging

### Check Current Routing Distribution

```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT destination, COUNT(*) as count FROM virtual_aliases WHERE active=1 GROUP BY destination;"
```

### View Spam Filtering Stats

```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT * FROM spam_filtering_stats;"
```

### Check Domain Summary

```bash
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  "SELECT * FROM domain_summary LIMIT 10;"
```

### Monitor Email Processing

**Free user emails (process-email)**:
```bash
journalctl -f | grep "process-email"
```

**Pro user emails (spam-process-email)**:
```bash
journalctl -f | grep "spam-process-email"
```

**SpamAssassin processing**:
```bash
journalctl -u spamd.service -f
```

### Test SpamAssassin Integration

```bash
# Test spamc directly
echo "Test email content" | spamc -c

# Test with spam content (should get high score)
echo "VIAGRA CHEAP PILLS BUY NOW!!!" | spamc -c
```

## Common Issues and Solutions

### Issue: Emails not being processed

**Diagnosis**:
```bash
# Check Postfix logs
journalctl -u postfix.service -f

# Check virtual alias lookup
postmap -q "@yourdomain.com" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf

# Check database
sqlite3 /opt/eu-email-webhook/data/postfix.db "SELECT * FROM virtual_aliases WHERE active=1 LIMIT 5;"
```

### Issue: SpamAssassin not working for Pro users

**Diagnosis**:
```bash
# Check SpamAssassin service
systemctl status spamd

# Test spamc
echo "test" | spamc -c

# Check spam-process-email script
ls -la /opt/eu-email-webhook/scripts/production/spam-process-email.js
```

### Issue: Plan changes not updating routing

**Diagnosis**:
```bash
# Check postfix-manager logs
docker compose -f docker-compose.prod.yml logs -f postfix-manager

# Check main app logs for plan sync
docker compose -f docker-compose.prod.yml logs -f app | grep -i "plan\|domain"

# Test plan update API
curl -X PUT http://localhost:3001/domains/test.example.com/plan \
  -H "Content-Type: application/json" \
  -d '{"planType": "pro"}'
```

## Performance Testing

### Load Testing Email Processing

```bash
# Test free user processing speed
time echo "test email" | /opt/eu-email-webhook/scripts/production/process-email.js

# Test pro user processing speed (includes SpamAssassin)
time echo "test email" | /opt/eu-email-webhook/scripts/production/spam-process-email.js
```

### Database Performance

```bash
# Test virtual alias lookup performance
time postmap -q "@test.example.com" sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf

# Check database query performance
sqlite3 /opt/eu-email-webhook/data/postfix.db \
  ".timer on" \
  "SELECT destination FROM virtual_aliases WHERE email='@test.example.com' AND active=1;"
```

## Validation Checklist

Before deploying to production, ensure:

- [ ] All automated tests pass
- [ ] Free user emails route to `process-email`
- [ ] Pro+ user emails route to `spam-process-email`
- [ ] Plan changes update routing correctly
- [ ] SpamAssassin headers are added for Pro+ users only
- [ ] No spam headers are added for free users
- [ ] Database queries are performant
- [ ] No Amavis references remain in configuration
- [ ] Email delivery continues to work normally
- [ ] Postfix configuration is valid (`postfix check`)

## Rollback Testing

Test the rollback procedure:

1. Backup current configuration
2. Apply rollback steps from migration documentation
3. Verify old Amavis approach works
4. Re-apply new approach
5. Verify new approach works again

This ensures you can safely rollback if issues are discovered in production.

## Continuous Monitoring

After deployment, monitor:

- Email processing latency (free vs pro)
- SpamAssassin resource usage
- Database query performance
- Plan change synchronization accuracy
- Error rates in email processing

Set up alerts for:
- High email processing failures
- SpamAssassin service downtime
- Database connectivity issues
- Plan sync failures
