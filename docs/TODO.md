# EmailConnect App - TODO List

This document organizes all identified improvements and fixes by priority level.

## ✅ Completed High Priority Tasks

### ✅ Data Retention Implementation (COMPLETED)
- **Status**: ✅ COMPLETE - Implemented automatic data retention system
- **Delivered**:
  - 2 hours retention for free users, 24 hours for Pro users
  - User-configurable via RetentionSection.vue in SettingsPage.vue
  - Background cleanup service running every 30 minutes
  - User-level settings in user_settings.dataRetentionHours
- **Impact**: GDPR compliance achieved, database bloat prevented

### ✅ Email Payload Viewer (COMPLETED)
- **Status**: ✅ COMPLETE - Modal with JSON viewer implemented
- **Delivered**:
  - PayloadViewer modal component with syntax highlighting
  - Accessible from LogsView.vue for entries with webhookPayload
  - Clean, formatted JSON display with copy functionality
- **Impact**: Debugging and user transparency improved

### ✅ Spam Filtering UI Simplification (COMPLETED)
- **Status**: ✅ COMPLETE - Simplified to single block threshold
- **Delivered**:
  - BlockThresholdSlider component with single threshold (default: 10.0)
  - Removed dual-handle slider and pass/review zone UI
  - Enforced minimum threshold of 1.0 to prevent blocking all email
  - Backend configuration unchanged (maintains thresholds.green/red)
- **Impact**: User confusion reduced, focused on actionable setting

### ✅ Notification System Fix (COMPLETED)
- **Status**: ✅ COMPLETE - Notification system working
- **Delivered**:
  - Fixed notification creation and delivery
  - WebSocket real-time updates functional
  - Toast notifications integrated with bell updates
- **Impact**: User communication system restored

## High Priority (h) - Current Focus Areas

### Domain Verification Process
- **Status**: ✅ COMPLETE - Domain verification is working correctly
- **Note**: User confirmed this is already fixed and functional

### Webhook Testing Feature
- **Status**: ✅ COMPLETE - Webhook testing functionality implemented
- **Note**: User confirmed this is already implemented and working

### Email Search and Filtering
- **Status**: 📋 DESCOPED - Removed from current roadmap
- **Note**: User descoped this feature for now

## Medium Priority (m) - Important Improvements

### EC Storage Folder Structure
- **Issue**: Need to define folder structure for EmailConnect's S3 storage
- **Requirements**:
  - Decide on folder naming: userId/aliasId pattern
  - Implement folder creation and validation
  - Handle AWS SDK errors for permissions/naming issues
- **Impact**: Required for Pro storage feature

### Database Cleanup
- **Issue**: Unused columns and tokens in database
- **Requirements**:
  - ✅ DONE: Removed domains.dataRetentionDays (now using user-level retention)
  - Clean up postfix.db unused columns (domains.destination, aliases fields)
  - Review verificationToken usage in domains table
  - Remove if no longer needed
- **Impact**: Reduces technical debt and confusion

### Payment Implementation
- **Issue**: Missing core payment functionality
- **Requirements**:
  - Implement subscription creation flow
  - Implement credit purchasing system
  - Integration with existing Mollie setup
- **Impact**: Required for monetization

### Plan Configuration Centralization
- **Issue**: Hardcoded plan logic in main.go
- **Requirements**:
  - Move to environment variable like PLANS_WITH_SPAM_FILTERING=pro,enterprise
  - Centralize plan configuration logic
  - Maintain consistency with plan-config.service.ts
- **Impact**: Improves maintainability

## Low Priority (l) - Nice to Have

### User Feedback System
- **Status**: 📋 LOWERED PRIORITY - Moved from medium to low priority
- **Note**: User indicated this is lower priority than originally planned

### LogsView Create CTA
- **Issue**: Add Create Domain CTA to LogsView TabNavigation
- **Requirements**:
  - Show Create CTA in TabNavigation component
  - Set default action to create domain
- **Impact**: Minor UX improvement

### Schema Validation Tooling
- **Issue**: Need better API schema validation
- **Requirements**:
  - Automated tests for API schemas vs actual data structures
  - Schema validation warnings for stripped properties
  - Schema drift detection in CI/CD
- **Impact**: Improves API reliability

### S3 Storage Error Handling
- **Issue**: Need proper error handling for user's S3-compatible storage
- **Requirements**:
  - Catch AWS SDK errors for bucket creation
  - Validate folder names (no symbols, duplicates, etc.)
  - Return proper error messages to user
- **Impact**: Better user experience for storage configuration

### Environment Variable Cleanup
- **Issue**: Hardcoded plan logic should use environment variables
- **Requirements**:
  - Create PLANS_WITH_SPAM_FILTERING environment variable
  - Update main.go to use environment configuration
  - Document new environment variables
- **Impact**: Improves configuration management

---

## Implementation Notes

### Database Schema Changes Needed
- Add user_settings table column for retention settings
- Review and potentially remove unused columns in postfix.db
- Verify verificationToken usage in domains table

### Frontend Components to Create/Modify
- RetentionSection.vue for SettingsPage.vue
- JSON viewer modal for webhook payloads
- Simplified spam filtering UI in DomainForm.vue

### Backend Services to Implement
- Data retention cleanup service
- Enhanced notification creation and delivery
- Payment processing endpoints

### Testing Requirements
- Test data retention cleanup
- Test notification system end-to-end
- Test simplified spam filtering configuration
- Test payment flows when implemented
