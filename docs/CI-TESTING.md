# CI Testing Guide

## Overview

The GitHub Actions workflow now includes comprehensive test execution on every push to `main` and `develop` branches, as well as on pull requests to `main`.

## Test Execution

### Automatic Test Running

By default, the following test suites are executed in CI:

1. **Unit Tests** (`npm run test:unit`)
   - DNS verifier tests
   - Email parser tests
   - Queue processing tests
   - Service layer tests

2. **Integration Tests** (`npm run test:integration`)
   - Authentication tests
   - Domain management tests
   - Webhook processing tests
   - Billing functionality tests
   - User isolation tests

3. **Webhook Tests** (`npm run test:webhook`)
   - Webhook flow tests
   - Usage tracking exclusion tests
   - User journey tests

4. **Frontend Tests** (`npm run test tests/frontend`)
   - Vue component tests
   - Frontend integration tests

5. **Coverage Report** (`npm run test:coverage`)
   - Generates comprehensive test coverage reports
   - Uploads coverage artifacts for review

### Test Environment Setup

The CI automatically sets up:
- PostgreSQL test database (`eu_email_webhook_test`)
- Redis test instance (database 1)
- Test-specific environment variables
- Required services (PostgreSQL, Redis)

## Skipping Tests

### Using [NOTEST] Flag

You can skip test execution by including `[NOTEST]` anywhere in your commit message:

```bash
# Examples of commits that will skip tests
git commit -m "Update documentation [NOTEST]"
git commit -m "[NOTEST] Fix typo in README"
git commit -m "Refactor comments and add [NOTEST] flag"
```

### When to Skip Tests

Use `[NOTEST]` for:
- Documentation-only changes
- README updates
- Comment updates
- Configuration file changes that don't affect functionality
- Minor formatting/style changes

### When NOT to Skip Tests

Always run tests for:
- Code changes in `src/` directory
- Database schema changes
- API endpoint modifications
- Frontend component changes
- Configuration changes that affect runtime behavior
- Dependency updates

## Test Results and Artifacts

### Coverage Reports

Test coverage reports are automatically generated and uploaded as GitHub Actions artifacts:
- HTML coverage report
- LCOV format for external tools
- JSON summary for programmatic access
- JUnit XML for test result parsing

### Accessing Test Results

1. **In GitHub Actions**: View test output directly in the workflow logs
2. **Coverage Artifacts**: Download from the Actions run page
3. **Test Summary**: Available in the workflow summary

### Coverage Thresholds

Current coverage thresholds (will fail if not met):
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## Local Testing

### Running Tests Locally

Before pushing, always run tests locally:

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:webhook
npm run test tests/frontend

# Run with coverage
npm run test:coverage

# Run in watch mode during development
npm run test:watch
```

### Test Environment Setup

Ensure your local environment has:
- PostgreSQL running with test database
- Redis running
- All environment variables set in `.env`

## Troubleshooting

### Common Test Failures

1. **Database Connection Issues**
   - Ensure PostgreSQL is running
   - Check database credentials
   - Verify test database exists

2. **Redis Connection Issues**
   - Ensure Redis is running
   - Check Redis URL configuration

3. **Environment Variable Issues**
   - Verify all required env vars are set
   - Check `.env.test` file exists

4. **Timeout Issues**
   - Tests have 15-second timeout
   - Check for hanging promises
   - Ensure proper cleanup in tests

### Getting Help

If tests fail in CI but pass locally:
1. Check the GitHub Actions logs for specific error messages
2. Verify environment differences between local and CI
3. Check if external dependencies are available in CI
4. Review test isolation and cleanup

## Best Practices

### Commit Messages

- Use descriptive commit messages
- Only use `[NOTEST]` when appropriate
- Consider the impact of your changes

### Test Development

- Write tests for new features
- Update tests when modifying existing code
- Ensure tests are isolated and don't depend on external state
- Use proper cleanup in test teardown

### Performance

- Keep tests fast and focused
- Mock external dependencies
- Use test databases, not production data
- Clean up resources after tests
