# Server Provisioning Guide

> **Critical Insight**: Server provisioning should be **manual documentation**, not automated scripts. App deployment should remain automated via GitHub Actions.

## Overview

This guide covers the **one-time manual setup** required for a fresh server before the automated deployment can work. This is separate from the automated app deployment handled by GitHub Actions.

## Prerequisites

- Fresh Ubuntu/Debian server
- Root or sudo access
- Domain name pointing to server IP
- GitHub Container Registry access configured

## Phase 1: System Dependencies

### Install Core Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y \
    postfix \
    postfix-sqlite \
    nginx \
    certbot \
    python3-certbot-nginx \
    sqlite3 \
    ufw \
    docker.io \
    docker-compose-v2 \
    curl \
    jq \
    git
```

### Configure Postfix During Installation
**IMPORTANT**: During postfix installation, select:
- **Configuration type**: Internet Site
- **System mail name**: your-domain.com (e.g., emailconnect.eu)

## Phase 2: Directory Structure Setup

### Create Application Directory
```bash
# Create the standard directory structure
sudo mkdir -p /opt/eu-email-webhook/data
sudo mkdir -p /opt/eu-email-webhook/scripts
sudo mkdir -p /opt/eu-email-webhook/backups

# Set proper ownership (replace 'ploi' with your deployment user)
sudo chown -R ploi:ploi /opt/eu-email-webhook

# Verify permissions
ls -la /opt/eu-email-webhook/
```

### Clone Repository
```bash
# Clone to the standard location
cd /opt
sudo git clone https://github.com/xadi-hq/eu-email-webhook.git
sudo chown -R ploi:ploi eu-email-webhook
```

## Phase 3: Environment Configuration

### Create Production Environment File
```bash
cd /opt/eu-email-webhook

# Create .env.prod with your specific values
cat > .env.prod << 'EOF'
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
DB_USER=your_db_user
DB_PASSWORD=your_secure_db_password
DB_NAME=your_db_name
DATABASE_URL=***************************************************************/your_db_name
REDIS_URL=redis://redis:6379
JWT_SECRET=your_secure_jwt_secret_64_chars_minimum
MAX_EMAIL_SIZE_MB=25
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3
DNS_VERIFICATION_TIMEOUT_MS=5000
DNS_VERIFICATION_CACHE_TTL_MS=300000
DNS_VERIFICATION_RETRY_ATTEMPTS=3
EMAIL_RETENTION_DAYS=30
LOG_RETENTION_DAYS=90
PROCESS_EMAIL_SCRIPT=/opt/eu-email-webhook/scripts/production/process-email.js
ADMIN_USERNAME=admin
ADMIN_PASSWORD=changeme
ADMIN_JWT_SECRET=your-very-strong-and-secret-key-for-admin-jwt
ADMIN_JWT_EXPIRES_IN=1h 
COMPOSE_PROJECT_NAME=your-project-name
COMPOSE_FILE=docker-compose.prod.yml
EOF

# Secure the environment file
chmod 600 .env.prod
```

### Generate Secure Credentials
```bash
# Generate secure database password
DB_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
echo "DB_PASSWORD: $DB_PASSWORD"

# Generate secure JWT secret
JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)
echo "JWT_SECRET: $JWT_SECRET"

# Update .env.prod with generated values
sed -i "s/your_secure_db_password/$DB_PASSWORD/g" .env.prod
sed -i "s/your_secure_jwt_secret_64_chars_minimum/$JWT_SECRET/g" .env.prod
```

## Phase 4: Firewall Configuration

### Configure UFW
```bash
# Reset firewall to defaults
sudo ufw --force reset

# Allow essential ports
sudo ufw allow 22    # SSH
sudo ufw allow 25    # SMTP (email reception)
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS

# Enable firewall
sudo ufw --force enable

# Verify rules
sudo ufw status verbose
```

## Phase 5: Docker Setup

### Configure Docker Permissions
```bash
# Add deployment user to docker group
sudo usermod -aG docker ploi

# Enable Docker service
sudo systemctl enable docker
sudo systemctl start docker

# Test Docker access (may require logout/login)
docker ps
```

## Phase 6: Initial Deployment Test

### Pull and Start Containers
```bash
cd /opt/eu-email-webhook

# Login to GitHub Container Registry (use your token)
echo "your_github_token" | docker login ghcr.io -u your_username --password-stdin

# Pull latest images
docker compose -f docker-compose.prod.yml --env-file .env.prod pull

# Start containers
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Verify all containers are healthy
docker ps

# Check logs for any issues
docker compose -f docker-compose.prod.yml logs --tail=20
```

### Verify Core Services
```bash
# Test main app health
curl -f http://localhost:3000/health

# Test postfix-manager health
curl -f http://localhost:3001/health

# Test domain addition (this creates SQLite database)
curl -X POST http://localhost:3001/domains \
  -H "Content-Type: application/json" \
  -d '{"domain": "test.example.com"}'
```

## Phase 7: Nginx Reverse Proxy

### Create Nginx Configuration
```bash
sudo tee /etc/nginx/sites-available/eu-email-webhook > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com ***************;
    
    # API endpoints
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 30s;
        proxy_connect_timeout 10s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        access_log off;
    }
    
    # Default response
    location / {
        return 200 '{"service": "eu-email-webhook", "status": "running"}';
        add_header Content-Type application/json;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/eu-email-webhook /etc/nginx/sites-enabled/

# Remove default site
sudo rm -f /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

### Verify External Access
```bash
# Test from external (replace with your server IP)
curl -f http://***************/health
curl -f http://***************/api/config/domains
```

## Phase 8: SSL Certificate

### Install SSL Certificate
```bash
# Install certificate for your domain
sudo certbot --nginx -d your-domain.com

# Verify auto-renewal is set up
sudo certbot renew --dry-run
```

## Phase 9: Host Postfix Configuration

### Configure Postfix for SQLite
```bash
# Configure Postfix to use SQLite databases
sudo postconf -e "virtual_alias_domains = sqlite:/opt/eu-email-webhook/data/virtual_domains.cf"
sudo postconf -e "virtual_alias_maps = sqlite:/opt/eu-email-webhook/data/virtual_aliases.cf"

# Add email processing alias
echo 'process-email: "|/opt/eu-email-webhook/scripts/production/process-email.js"' | sudo tee -a /etc/aliases
sudo newaliases

# Reload Postfix
sudo systemctl reload postfix

# Verify Postfix configuration
sudo postconf | grep virtual_alias
```

## Phase 10: Final Validation

### Run Comprehensive Validation
```bash
cd /opt/eu-email-webhook/deploy
chmod +x validate-deployment.sh
DOMAIN=your-domain.com ./validate-deployment.sh
```

## Troubleshooting

### Common Issues

#### Docker Compose Warnings
**Symptom**: `WARN[0000] The "DATABASE_URL" variable is not set`
**Solution**: These are parse-time warnings, not runtime errors. Verify `.env.prod` format and ensure no line breaks in values.

#### SQLite Database Missing
**Symptom**: `unable to open database "/opt/eu-email-webhook/data/postfix.db"`
**Solution**: 
1. Ensure data directory exists: `ls -la /opt/eu-email-webhook/data/`
2. Test domain addition: `curl -X POST http://localhost:3001/domains ...`
3. Check postfix-manager logs: `docker logs container-name`

#### Containers Not Starting
**Symptom**: Containers exit immediately or health checks fail
**Solution**:
1. Check environment file format: `cat -A .env.prod`
2. Verify all required variables are set
3. Check container logs: `docker compose logs`

#### External Access Failing
**Symptom**: API calls from outside server fail
**Solution**:
1. Verify Nginx configuration: `sudo nginx -t`
2. Check firewall rules: `sudo ufw status`
3. Test internal connectivity first: `curl localhost:3000/health`

## Security Considerations

### File Permissions
```bash
# Secure environment file
chmod 600 /opt/eu-email-webhook/.env.prod

# Verify data directory permissions
ls -la /opt/eu-email-webhook/data/
```

### Firewall Rules
- Only expose necessary ports (22, 25, 80, 443)
- Keep internal API ports (3000, 3001) internal
- Regular security updates

### SSL Configuration
- Use strong SSL certificates
- Enable HSTS headers
- Regular certificate renewal

## Next Steps After Provisioning

1. **Configure DNS**: Set up MX records pointing to your server
2. **Test Email Flow**: Send test emails and verify webhook delivery
3. **Set up Monitoring**: Configure log aggregation and alerting
4. **Backup Strategy**: Implement regular backups of configuration and data
5. **Documentation**: Update any domain-specific configuration

---

## Key Insights for Future Deployments

### What Works Well
- **Automated app deployment** via GitHub Actions
- **Docker containerization** for application services
- **Hybrid architecture**: Docker for apps, host for email/web infrastructure
- **SQLite for Postfix** dramatically simplifies configuration

### What Should Be Manual
- **Server provisioning** (this document)
- **Initial environment setup**
- **SSL certificate installation**
- **DNS configuration**

### Critical Success Factors
1. **Proper directory structure** with correct permissions
2. **Clean .env.prod file** with no line breaks in values
3. **Data directory must exist** before container startup
4. **Firewall rules** must allow necessary ports
5. **Nginx reverse proxy** for external access

This separation of concerns makes deployments reliable and maintainable.
