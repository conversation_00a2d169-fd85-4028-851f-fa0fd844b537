#!/bin/bash

# Development workflow script
# This script simplifies common development tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${GREEN}📋 $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to start development infrastructure
start_dev() {
    print_step "Starting development infrastructure..."

    # Stop any existing containers
    docker compose down >/dev/null 2>&1 || true

    # Start infrastructure services only (PostgreSQL, Redis, Postfix Manager)
    docker compose up postgres redis postfix-manager -d

    print_step "Infrastructure starting... Waiting for health checks..."
    sleep 10

    # Check if services are healthy
    if docker compose ps postgres | grep -q "healthy" && docker compose ps redis | grep -q "healthy"; then
        print_step "✅ Development infrastructure is ready!"
        print_step "🗄️  PostgreSQL: localhost:5432"
        print_step "🔴 Redis: localhost:6379"
        print_step "📮 Postfix Manager: localhost:3001"
        echo ""
        print_step "🚀 Next steps:"
        echo "   1. Run database migrations: npx prisma migrate deploy"
        echo "   2. Start the app server: npm run dev"
        echo "   3. Visit: http://localhost:3000"
        echo ""
        print_step "💡 Tips:"
        echo "   - View infrastructure logs: ./dev.sh logs"
        echo "   - Stop infrastructure: ./dev.sh stop"
        echo "   - Run migrations: ./dev.sh migrate"
    else
        print_error "Failed to start development infrastructure"
        docker compose logs
        exit 1
    fi
}

# Function to stop development environment
stop_dev() {
    print_step "Stopping development environment..."
    docker compose down
    print_step "✅ Development environment stopped"
}

# Function to restart infrastructure services
restart() {
    print_step "Restarting infrastructure..."
    docker compose restart postgres redis postfix-manager
    print_step "✅ Infrastructure restarted"
}

# Function to run database migrations
migrate() {
    print_step "Running database migrations..."
    npx prisma migrate deploy
    print_step "✅ Database migrations completed"
}

# Function to start the Node.js development server
dev() {
    print_step "Starting full development environment..."
    start_dev
    migrate
    print_step "Starting Node.js development server..."
    npm run dev
}

# Function to view logs
logs() {
    if [ "$2" = "all" ]; then
        docker compose logs -f
    else
        docker compose logs -f postgres redis postfix-manager
    fi
}

# Function to run tests
test() {
    print_step "Running tests..."
    npm test
}

# Function to show status
status() {
    print_step "Development environment status:"
    docker compose ps
}

# Function to clean up everything
clean() {
    print_warning "This will remove all containers, volumes, and images!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_step "Cleaning up..."
        docker compose down -v --remove-orphans
        docker system prune -f
        print_step "✅ Cleanup complete"
    else
        print_step "Cleanup cancelled"
    fi
}

# Function to show help
show_help() {
    echo "🚀 EU Email Webhook Development Helper"
    echo ""
    echo "Usage: ./dev.sh <command>"
    echo ""
    echo "Commands:"
    echo "  start     Start infrastructure services (PostgreSQL, Redis, Postfix Manager)"
    echo "  stop      Stop all infrastructure services"
    echo "  restart   Restart infrastructure services"
    echo "  dev       Start infrastructure + run Node.js development server"
    echo "  migrate   Run database migrations"
    echo "  logs      View infrastructure logs (add 'all' for all services)"
    echo "  test      Run tests"
    echo "  status    Show container status"
    echo "  clean     Clean up all containers and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Development workflow:"
    echo "  ./dev.sh start    # Start infrastructure"
    echo "  npm run dev       # Start app server (in another terminal)"
    echo ""
    echo "Or simply:"
    echo "  ./dev.sh dev      # Start everything"
    echo ""
    echo "Examples:"
    echo "  ./dev.sh start"
    echo "  ./dev.sh logs"
    echo "  ./dev.sh migrate"
    echo "  ./dev.sh test"
}

# Main command handling
case "${1:-help}" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart
        ;;
    dev)
        dev
        ;;
    migrate)
        migrate
        ;;
    logs)
        logs "$@"
        ;;
    test)
        test
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    help|*)
        show_help
        ;;
esac
