name: Deploy Content Only

on:
  push:
    paths:
      - 'content/**'
    branches: [ main ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force content deployment'
        required: false
        default: 'false'

jobs:
  deploy-content:
    name: Deploy Content Changes
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' || (github.event_name == 'push' && contains(github.event.head_commit.message, '[content-only]'))
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy content to production server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            #!/bin/bash
            set -e

            echo "📝 Deploying content-only changes..."

            # Detect Docker Compose version
            if docker compose version &>/dev/null; then
              DOCKER_COMPOSE="docker compose"
            elif command -v docker-compose &>/dev/null; then
              DOCKER_COMPOSE="docker-compose"
            else
              echo "❌ No supported Docker Compose found" >&2
              exit 1
            fi

            DEPLOY_DIR=/opt/eu-email-webhook

            # Update only content directory
            cd "$DEPLOY_DIR"
            echo "📁 Pulling latest content changes..."
            git fetch origin
            git checkout origin/main -- content/

            # Clear content cache via API
            echo "🔄 Clearing content cache..."
            if curl -f --max-time 10 -X POST http://localhost:3000/api/admin/cache/clear; then
              echo "✅ Content cache cleared successfully"
            else
              echo "⚠️ Cache clear failed, restarting app container..."
              # Restart only the app container to clear cache
              $DOCKER_COMPOSE -f docker-compose.prod.yml --env-file .env.prod restart app
              sleep 10
            fi

            # Verify content is accessible
            echo "🧪 Verifying content deployment..."
            if curl -f --max-time 10 http://localhost:3000/api/public/changelog | grep -q '"success":true'; then
              echo "✅ Changelog content is accessible"
            else
              echo "❌ Changelog content verification failed"
              exit 1
            fi

            if curl -f --max-time 10 http://localhost:3000/api/public/help | grep -q '"success":true'; then
              echo "✅ Help content is accessible"
            else
              echo "❌ Help content verification failed"
              exit 1
            fi

            if curl -f --max-time 10 http://localhost:3000/api/public/static | grep -q '"success":true'; then
              echo "✅ Static pages content is accessible"
            else
              echo "❌ Static pages content verification failed"
              exit 1
            fi

            echo "✅ Content deployment completed successfully"
            echo "📝 Content updated without full application rebuild"

      - name: Notify content deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Content deployment completed successfully"
            echo "📝 New content is live without full rebuild"
          else
            echo "❌ Content deployment failed"
            echo "🔍 Check deployment logs above"
          fi
