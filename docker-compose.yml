# Development Docker Compose - Infrastructure Services Only
# Run the main app locally with: npm run dev
# This provides: PostgreSQL, Redis, and Postfix Manager

services:
  # 🎯 SIMPLIFIED POSTFIX MANAGER WITH SQLITE
  postfix-manager:
    build:
      context: .
      dockerfile: ./postfix-manager-service/Dockerfile
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - LOG_LEVEL=info
      - PROCESS_EMAIL_SCRIPT=/opt/eu-email-webhook/scripts/production/process-email.js
      - ALLOWED_ORIGINS=http://localhost:3000
      - SQLITE_DB_PATH=/opt/eu-email-webhook/data/postfix.db
      - VIRTUAL_DOMAINS_CF=/opt/eu-email-webhook/data/virtual_domains.cf
      - VIRTUAL_ALIASES_CF=/opt/eu-email-webhook/data/virtual_aliases.cf
    volumes:
      # 🎯 ONLY 4 ESSENTIAL MOUNTS (vs 20+ before)
      - /etc/postfix/main.cf:/etc/postfix/main.cf:rw
      - /etc/aliases:/etc/aliases:rw
      - eu_email_data:/opt/eu-email-webhook/data:rw
      - eu_email_scripts:/opt/eu-email-webhook/scripts:ro
    restart: unless-stopped
    privileged: true  # Required for systemctl and postfix management
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      postgres:
        condition: service_healthy

  postgres:
    image: postgres:17-alpine
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
      - POSTGRES_DB=${DB_NAME:-eu_email_webhook}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/utilities/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d eu_email_webhook"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    shm_size: 128mb

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 5s
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # 🎯 SCRIPT INITIALIZATION SERVICE
  script-init:
    build: 
      context: .
      target: production
    volumes:
      - eu_email_scripts:/opt/eu-email-webhook/scripts:rw
    command: sh -c "cp -r /app/scripts/* /opt/eu-email-webhook/scripts/ && chmod +x /opt/eu-email-webhook/scripts/*.js"
    restart: "no"
    profiles:
      - init

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  eu_email_data:
    driver: local
  eu_email_scripts:
    driver: local
