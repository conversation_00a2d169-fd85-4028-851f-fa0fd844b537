import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { PrismaClient } from '@prisma/client'
import { AliasService } from '../../../src/backend/services/user/alias.service'

// Mock environment variables
jest.mock('../../../src/backend/config/env', () => ({
  env: {
    USER_JWT_SECRET: 'test-secret',
    ADMIN_PASSWORD: 'test-admin-password',
    ADMIN_JWT_SECRET: 'test-admin-secret',
    DATABASE_URL: process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/test'
  }
}))

const prisma = new PrismaClient()
const aliasService = new AliasService()

describe('Alias Deletion Constraints', () => {
  let testUser: any
  let testDomain: any
  let testWebhook: any

  beforeEach(async () => {
    // Create test user
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Test User'
      }
    })

    // Create test webhook
    testWebhook = await prisma.webhook.create({
      data: {
        name: 'Test Webhook',
        url: 'https://example.com/webhook',
        userId: testUser.id,
        verified: true
      }
    })

    // Create test domain
    testDomain = await prisma.domain.create({
      data: {
        domain: 'test-constraints.com',
        userId: testUser.id,
        verified: true
      }
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.alias.deleteMany({ where: { domainId: testDomain.id } })
    await prisma.domain.delete({ where: { id: testDomain.id } })
    await prisma.webhook.delete({ where: { id: testWebhook.id } })
    await prisma.user.delete({ where: { id: testUser.id } })
  })

  it('should prevent deletion of the only alias for a domain', async () => {
    // Create a single alias for the domain
    const alias = await prisma.alias.create({
      data: {
        email: '<EMAIL>',
        domainId: testDomain.id,
        webhookId: testWebhook.id
      }
    })

    // Attempt to delete the only alias should fail
    await expect(
      aliasService.deleteAlias(alias.id, testUser.id)
    ).rejects.toThrow('Cannot delete the last alias for a domain')
  })

  it('should allow deletion of alias when multiple aliases exist', async () => {
    // Create multiple aliases for the domain
    const alias1 = await prisma.alias.create({
      data: {
        email: '<EMAIL>',
        domainId: testDomain.id,
        webhookId: testWebhook.id
      }
    })

    const alias2 = await prisma.alias.create({
      data: {
        email: '<EMAIL>',
        domainId: testDomain.id,
        webhookId: testWebhook.id
      }
    })

    // Should be able to delete one alias when multiple exist
    const result = await aliasService.deleteAlias(alias1.id, testUser.id)
    expect(result.success).toBe(true)
    expect(result.message).toBe('Alias deleted successfully')

    // Verify the alias was actually deleted
    const deletedAlias = await prisma.alias.findUnique({
      where: { id: alias1.id }
    })
    expect(deletedAlias).toBeNull()

    // Verify the other alias still exists
    const remainingAlias = await prisma.alias.findUnique({
      where: { id: alias2.id }
    })
    expect(remainingAlias).not.toBeNull()
  })
})
