import { describe, it, expect } from '@jest/globals';
import { PlanConfigService, PLAN_PERMISSIONS } from '../../../src/backend/services/billing/plan-config.service.js';

describe('PlanConfigService - Permission System', () => {
  describe('PLAN_PERMISSIONS configuration', () => {
    it('should have correct permissions for free plan', () => {
      expect(PLAN_PERMISSIONS.free).toEqual([]);
    });

    it('should have correct permissions for pro plan', () => {
      expect(PLAN_PERMISSIONS.pro).toContain('custom_headers');
      expect(PLAN_PERMISSIONS.pro).toContain('priority_support');
      expect(PLAN_PERMISSIONS.pro).toContain('premium_delivery');
    });

    it('should have correct permissions for enterprise plan', () => {
      expect(PLAN_PERMISSIONS.enterprise).toContain('custom_headers');
      expect(PLAN_PERMISSIONS.enterprise).toContain('priority_support');
      expect(PLAN_PERMISSIONS.enterprise).toContain('email_analytics');
      expect(PLAN_PERMISSIONS.enterprise).toContain('custom_integrations');
      expect(PLAN_PERMISSIONS.enterprise).toContain('sla_guarantee');
      expect(PLAN_PERMISSIONS.enterprise).toContain('premium_delivery');
    });
  });

  describe('userHasPermission', () => {
    it('should return false for free plan with any permission', () => {
      expect(PlanConfigService.userHasPermission('free', 'custom_headers')).toBe(false);
      expect(PlanConfigService.userHasPermission('free', 'priority_support')).toBe(false);
    });

    it('should return true for pro plan with allowed permissions', () => {
      expect(PlanConfigService.userHasPermission('pro', 'custom_headers')).toBe(true);
      expect(PlanConfigService.userHasPermission('pro', 'priority_support')).toBe(true);
      expect(PlanConfigService.userHasPermission('pro', 'premium_delivery')).toBe(true);
    });

    it('should return false for pro plan with enterprise-only permissions', () => {
      expect(PlanConfigService.userHasPermission('pro', 'email_analytics')).toBe(false);
      expect(PlanConfigService.userHasPermission('pro', 'sla_guarantee')).toBe(false);
    });

    it('should return true for enterprise plan with all permissions', () => {
      expect(PlanConfigService.userHasPermission('enterprise', 'custom_headers')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'priority_support')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'email_analytics')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'custom_integrations')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'sla_guarantee')).toBe(true);
      expect(PlanConfigService.userHasPermission('enterprise', 'premium_delivery')).toBe(true);
    });

    it('should return false for unknown plan types', () => {
      expect(PlanConfigService.userHasPermission('unknown', 'custom_headers')).toBe(false);
      expect(PlanConfigService.userHasPermission('', 'custom_headers')).toBe(false);
    });
  });

  describe('getPlanPermissions', () => {
    it('should return empty array for free plan', () => {
      const permissions = PlanConfigService.getPlanPermissions('free');
      expect(permissions).toEqual([]);
    });

    it('should return correct permissions for pro plan', () => {
      const permissions = PlanConfigService.getPlanPermissions('pro');
      expect(permissions).toContain('custom_headers');
      expect(permissions).toContain('priority_support');
      expect(permissions).toContain('premium_delivery');
    });

    it('should return all permissions for enterprise plan', () => {
      const permissions = PlanConfigService.getPlanPermissions('enterprise');
      expect(permissions.length).toBeGreaterThan(5);
      expect(permissions).toContain('custom_headers');
      expect(permissions).toContain('priority_support');
      expect(permissions).toContain('email_analytics');
      expect(permissions).toContain('custom_integrations');
      expect(permissions).toContain('sla_guarantee');
    });

    it('should return empty array for unknown plan types', () => {
      expect(PlanConfigService.getPlanPermissions('unknown')).toEqual([]);
    });
  });

  describe('userHasAnyPermission', () => {
    it('should return false for free plan with any permissions', () => {
      expect(PlanConfigService.userHasAnyPermission('free', ['custom_headers', 'priority_support'])).toBe(false);
    });

    it('should return true for pro plan with at least one allowed permission', () => {
      expect(PlanConfigService.userHasAnyPermission('pro', ['custom_headers'])).toBe(true);
      expect(PlanConfigService.userHasAnyPermission('pro', ['sla_guarantee'])).toBe(false);
    });

    it('should return true for enterprise plan with any permissions', () => {
      expect(PlanConfigService.userHasAnyPermission('enterprise', ['custom_headers'])).toBe(true);
      expect(PlanConfigService.userHasAnyPermission('enterprise', ['sla_guarantee'])).toBe(true);
    });
  });

  describe('getMissingPermissions', () => {
    it('should return all permissions for free plan', () => {
      const missing = PlanConfigService.getMissingPermissions('free', ['custom_headers', 'priority_support']);
      expect(missing).toEqual(['custom_headers', 'priority_support']);
    });

    it('should return only missing permissions for pro plan', () => {
      const missing = PlanConfigService.getMissingPermissions('pro', ['custom_headers']);
      expect(missing).toEqual([]);
    });

    it('should return empty array for enterprise plan with any permissions', () => {
      const missing = PlanConfigService.getMissingPermissions('enterprise', ['custom_headers']);
      expect(missing).toEqual([]);
    });
  });

  describe('getCreditPricing with permissions', () => {
    it('should return standard pricing for free plan', () => {
      const pricing = PlanConfigService.getCreditPricing('free');
      expect(pricing.pricePerHundred).toBe(1.00);
      expect(pricing.currency).toBe('EUR');
    });

    it('should return discounted pricing for pro plan (has priority_support)', () => {
      const pricing = PlanConfigService.getCreditPricing('pro');
      expect(pricing.pricePerHundred).toBe(0.80);
      expect(pricing.currency).toBe('EUR');
    });

    it('should return discounted pricing for enterprise plan (has priority_support)', () => {
      const pricing = PlanConfigService.getCreditPricing('enterprise');
      expect(pricing.pricePerHundred).toBe(0.80);
      expect(pricing.currency).toBe('EUR');
    });
  });
});
