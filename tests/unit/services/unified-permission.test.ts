/**
 * Comprehensive Unit Tests for UnifiedPermissionService
 * 
 * This test suite covers all aspects of the unified permission system:
 * - Feature access validation
 * - API scope checking
 * - Plan permission validation
 * - Resource limit enforcement
 * - Error scenarios and edge cases
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock dependencies first, before importing the service
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockPrisma: any = {
  user: {
    findUnique: jest.fn(),
  },
  alias: {
    count: jest.fn(),
  },
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockApiKeyService: any = {
  verifyApiKey: jest.fn(),
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockPlanConfigService: any = {
  userHasPermission: jest.fn(),
  getPlanPermissions: jest.fn(),
  getPlanLimits: jest.fn(),
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockLogger: any = {
  debug: jest.fn(),
  error: jest.fn(),
};

// Mock the modules
jest.mock('../../../src/backend/lib/prisma.js', () => ({
  prisma: mockPrisma,
}));

jest.mock('../../../src/backend/services/auth/api-key.service.js', () => ({
  ApiKeyService: jest.fn().mockImplementation(() => mockApiKeyService),
}));

jest.mock('../../../src/backend/services/billing/plan-config.service.js', () => ({
  PlanConfigService: mockPlanConfigService,
}));

jest.mock('../../../src/backend/utils/logger.js', () => ({
  logger: mockLogger,
}));

// Now import the service after mocking
import { UnifiedPermissionService } from '../../../src/backend/services/auth/unified/unified-permission.service.js';
import { FEATURE_CONFIGS } from '../../../src/backend/services/auth/unified/feature-configs.js';
import type { 
  FeaturePermissionRequest, 
  ResourceUsage,
  ResourceLimits,
} from '../../../src/backend/types/permissions.types.js';

describe('UnifiedPermissionService', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    planType: 'free',
    currentMonthEmails: 100,
    monthlyEmailLimit: 1000,
    _count: {
      domains: 2,
      webhooks: 3,
    },
  };

  const mockUsage: ResourceUsage = {
    domains: 2,
    aliases: 5,
    webhooks: 3,
    emails: 100,
    monthlyEmails: 100,
  };

  const mockLimits: ResourceLimits = {
    domains: 3,
    aliases: 10,
    webhooks: 5,
    emails: 1000,
    monthlyEmails: 1000,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockPrisma.user.findUnique.mockResolvedValue(mockUser);
    mockPrisma.alias.count.mockResolvedValue(5);
    
    mockPlanConfigService.userHasPermission.mockReturnValue(true);
    mockPlanConfigService.getPlanPermissions.mockReturnValue([]);
    mockPlanConfigService.getPlanLimits.mockReturnValue(mockLimits);
    
    mockApiKeyService.verifyApiKey.mockResolvedValue({
      success: true,
      scopes: ['webhooks:read', 'webhooks:write'],
    } as any);
  });

  describe('validateFeatureAccess', () => {
    describe('Basic feature validation', () => {
      it('should allow free users to access basic auth', async () => {
        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'auth:basic',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(true);
        expect(result.details.hasApiScope).toBe(true);
        expect(result.details.hasPlanPermission).toBe(true);
        expect(result.details.withinLimits).toBe(true);
      });

      it('should allow free users to create basic webhooks within limits', async () => {
        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'valid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(true);
        expect(result.details.hasApiScope).toBe(true);
        expect(result.details.hasPlanPermission).toBe(true);
        expect(result.details.withinLimits).toBe(true);
        expect(result.details.currentPlan).toBe('free');
      });

      it('should block free users from creating advanced webhooks', async () => {
        // Mock plan permission check to return false for custom_headers
        mockPlanConfigService.userHasPermission.mockReturnValue(false);

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create_advanced',
          apiKey: 'valid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.details.hasApiScope).toBe(true);
        expect(result.details.hasPlanPermission).toBe(false);
        expect(result.details.upgradeRequired).toBe(true);
        expect(result.details.requiredPlan).toBe('pro');
        expect(result.reason).toContain('custom_headers');
      });

      it('should return error for unknown feature', async () => {
        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'unknown:feature',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.reason).toBe('Unknown feature: unknown:feature');
      });
    });

    describe('API Scope validation', () => {
      it('should reject request with invalid API key', async () => {
        mockApiKeyService.verifyApiKey.mockResolvedValue({
          success: false,
          reason: 'Invalid API key',
        });

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'invalid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.details.hasApiScope).toBe(false);
        expect(result.reason).toBe('Invalid API key');
      });

      it('should reject request with insufficient scope', async () => {
        mockApiKeyService.verifyApiKey.mockResolvedValue({
          success: true,
          scopes: ['webhooks:read'], // Missing webhooks:write
        });

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'limited-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.details.hasApiScope).toBe(false);
        expect(result.reason).toBe('Missing required scope: webhooks:write');
      });

      it('should allow wildcard scopes', async () => {
        mockApiKeyService.verifyApiKey.mockResolvedValue({
          success: true,
          scopes: ['webhooks:*'], // Wildcard scope
        });

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'wildcard-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(true);
        expect(result.details.hasApiScope).toBe(true);
      });

      it('should allow full access wildcard scope', async () => {
        mockApiKeyService.verifyApiKey.mockResolvedValue({
          success: true,
          scopes: ['*'], // Full access
        });

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'admin-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(true);
        expect(result.details.hasApiScope).toBe(true);
      });
    });
    describe('Resource limit enforcement', () => {
      it('should enforce webhook creation limits', async () => {
        // Mock user with max webhooks already created
        const userAtLimit = {
          ...mockUser,
          _count: { ...mockUser._count, webhooks: 5 }, // At limit
        };
        mockPrisma.user.findUnique.mockResolvedValue(userAtLimit);

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'valid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.details.withinLimits).toBe(false);
        expect(result.details.upgradeRequired).toBe(true);
        expect(result.reason).toContain('Cannot create webhooks');
        expect(result.reason).toContain('Plan limit of 5 reached');
      });

      it('should enforce domain creation limits', async () => {
        // Mock user with max domains already created  
        const userAtLimit = {
          ...mockUser,
          _count: { ...mockUser._count, domains: 3 }, // At limit
        };
        mockPrisma.user.findUnique.mockResolvedValue(userAtLimit);

        // Ensure API key has proper scope for domain creation
        mockApiKeyService.verifyApiKey.mockResolvedValue({
          success: true,
          scopes: ['domains:write'],
        });

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'domain:create',
          apiKey: 'valid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.details.withinLimits).toBe(false);
        expect(result.reason).toContain('Cannot create domains');
      });

      it('should enforce alias creation limits', async () => {
        // Mock alias count at limit
        mockPrisma.alias.count.mockResolvedValue(10); // At limit

        // Ensure API key has proper scope for alias creation
        mockApiKeyService.verifyApiKey.mockResolvedValue({
          success: true,
          scopes: ['aliases:write'],
        });

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'alias:create',
          apiKey: 'valid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.details.withinLimits).toBe(false);
        expect(result.reason).toContain('Cannot create aliases');
      });

      it('should include current usage and limits in response', async () => {
        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
          apiKey: 'valid-api-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.details.currentUsage).toBeDefined();
        expect(result.details.limits).toBeDefined();
        expect(result.details.currentUsage?.webhooks).toBe(3);
        expect(result.details.limits?.webhooks).toBe(5);
      });
    });

    describe('Error handling', () => {
      it('should handle user not found', async () => {
        mockPrisma.user.findUnique.mockResolvedValue(null);

        const request: FeaturePermissionRequest = {
          userId: 'nonexistent-user',
          featureName: 'webhook:create',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.reason).toBe('User not found');
      });

      it('should handle database errors gracefully', async () => {
        mockPrisma.user.findUnique.mockRejectedValue(new Error('Database error'));

        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:create',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(false);
        expect(result.reason).toBe('Permission validation failed');
      });
    });

    describe('Browser requests (no API key)', () => {
      it('should allow browser requests without API key validation', async () => {
        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName: 'webhook:read', // Has required scope but no API key provided
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);

        expect(result.allowed).toBe(true);
        expect(result.details.hasApiScope).toBe(true); // Should be true for browser requests
      });
    });
  });

  describe('getEffectivePermissions', () => {
    beforeEach(() => {
      mockPlanConfigService.getPlanPermissions.mockReturnValue(['custom_headers']);
    });

    it('should return complete effective permissions for API key user', async () => {
      const result = await UnifiedPermissionService.getEffectivePermissions(
        'user-123',
        'valid-api-key'
      );

      expect(result).toMatchObject({
        scopes: ['webhooks:read', 'webhooks:write'],
        planPermissions: ['custom_headers'],
        planType: 'free',
        resourceLimits: mockLimits,
        currentUsage: expect.objectContaining({
          domains: 2,
          webhooks: 3,
          aliases: 5,
        }),
        availableFeatures: expect.any(Array),
        restrictions: expect.any(Array),
      });
    });

    it('should handle user not found', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      await expect(
        UnifiedPermissionService.getEffectivePermissions('nonexistent-user')
      ).rejects.toThrow('User not found');
    });
  });

  describe('Complex permission scenarios', () => {
    it('should handle feature with all requirements', async () => {
      // Test a feature that requires scope + plan permission + resource limit
      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create_advanced',
        apiKey: 'valid-key',
      };

      // Mock pro user with proper permissions
      const proUser = { ...mockUser, planType: 'pro' };
      mockPrisma.user.findUnique.mockResolvedValue(proUser);
      mockPlanConfigService.userHasPermission.mockReturnValue(true);

      const result = await UnifiedPermissionService.validateFeatureAccess(request);

      expect(result.allowed).toBe(true);
      expect(result.details.hasApiScope).toBe(true);
      expect(result.details.hasPlanPermission).toBe(true);
      expect(result.details.withinLimits).toBe(true);
    });

    it('should provide upgrade information for blocked features', async () => {
      // Test free user trying to access enterprise feature
      mockPlanConfigService.userHasPermission.mockReturnValue(false);
      // Ensure the API key has the correct scope for this feature
      mockApiKeyService.verifyApiKey.mockResolvedValue({
        success: true,
        scopes: ['analytics:read'],
      });

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'analytics:read',
        apiKey: 'valid-key',
      };

      const result = await UnifiedPermissionService.validateFeatureAccess(request);

      expect(result.allowed).toBe(false);
      expect(result.details.upgradeRequired).toBe(true);
      expect(result.details.requiredPlan).toBe('enterprise');
      expect(result.details.upgradeUrl).toBe('/settings#billing');
    });

    it('should fail fast on first validation failure', async () => {
      // Test that validation stops at first failure (API scope in this case)
      mockApiKeyService.verifyApiKey.mockResolvedValue({
        success: false,
        reason: 'Invalid key',
      });

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
        apiKey: 'invalid-key',
      };

      const result = await UnifiedPermissionService.validateFeatureAccess(request);

      expect(result.allowed).toBe(false);
      expect(result.details.hasApiScope).toBe(false);
      expect(result.reason).toBe('Invalid API key');
    });

    it('should handle features with quantity-based limits', async () => {
      // Mock attempting to create multiple resources at once
      const userNearLimit = {
        ...mockUser,
        _count: { domains: 2, webhooks: 4 }, // 1 away from webhook limit
      };
      mockPrisma.user.findUnique.mockResolvedValue(userNearLimit);

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
        apiKey: 'valid-key',
      };

      // Should still allow creation since under limit
      const result = await UnifiedPermissionService.validateFeatureAccess(request);
      expect(result.allowed).toBe(true);

      // But if we were at the exact limit
      const userAtLimit = {
        ...mockUser,
        _count: { domains: 2, webhooks: 5 }, // At webhook limit
      };
      mockPrisma.user.findUnique.mockResolvedValue(userAtLimit);

      const resultAtLimit = await UnifiedPermissionService.validateFeatureAccess(request);
      expect(resultAtLimit.allowed).toBe(false);
      expect(resultAtLimit.details.withinLimits).toBe(false);
    });
  });

  describe('Feature configuration integration', () => {
    it('should respect all defined feature configurations', async () => {
      // Test that our service properly integrates with feature configs
      const features = Object.keys(FEATURE_CONFIGS);
      expect(features.length).toBeGreaterThan(10);

      // Test a sample of features to ensure they work
      const sampleFeatures = [
        'webhook:read',
        'domain:create',
        'alias:update',
        'profile:read',
        'apikey:create',
        'billing:read',
      ];

      for (const featureName of sampleFeatures) {
        const request: FeaturePermissionRequest = {
          userId: 'user-123',
          featureName,
          apiKey: 'valid-key',
        };

        const result = await UnifiedPermissionService.validateFeatureAccess(request);
        
        // Should not fail due to unknown feature
        expect(result.reason).not.toBe(`Unknown feature: ${featureName}`);
        
        // Should have proper permission structure
        expect(result.details).toHaveProperty('hasApiScope');
        expect(result.details).toHaveProperty('hasPlanPermission');
        expect(result.details).toHaveProperty('withinLimits');
      }
    });

    it('should handle features without resource limits', async () => {
      // Ensure the API key has the correct scope for this feature
      mockApiKeyService.verifyApiKey.mockResolvedValue({
        success: true,
        scopes: ['profile:read'],
      });
      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'profile:read', // No resource limits
        apiKey: 'valid-key',
      };

      const result = await UnifiedPermissionService.validateFeatureAccess(request);

      expect(result.details.withinLimits).toBe(true);
      expect(result.details.currentUsage).toBeUndefined();
      expect(result.details.limits).toBeUndefined();
    });
  });

  describe('Edge cases and boundary conditions', () => {
    it('should handle empty API key scopes', async () => {
      mockApiKeyService.verifyApiKey.mockResolvedValue({
        success: true,
        scopes: [], // Empty scopes
      });

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
        apiKey: 'limited-key',
      };

      const result = await UnifiedPermissionService.validateFeatureAccess(request);

      expect(result.allowed).toBe(false);
      expect(result.details.hasApiScope).toBe(false);
    });

    it('should handle null/undefined API key scopes', async () => {
      mockApiKeyService.verifyApiKey.mockResolvedValue({
        success: true,
        scopes: null, // Null scopes
      });

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
        apiKey: 'null-scopes-key',
      };

      const result = await UnifiedPermissionService.validateFeatureAccess(request);

      expect(result.allowed).toBe(false);
      expect(result.details.hasApiScope).toBe(false);
    });

    it('should handle user with null plan type', async () => {
      const userWithNullPlan = { ...mockUser, planType: null };
      mockPrisma.user.findUnique.mockResolvedValue(userWithNullPlan);

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
      };

      // Should still work, treating null as default plan
      const result = await UnifiedPermissionService.validateFeatureAccess(request);
      expect(result.details.currentPlan).toBeDefined();
    });
  });

  describe('Logging and debugging', () => {
    it('should log permission validation details', async () => {
      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
        apiKey: 'valid-key',
      };

      await UnifiedPermissionService.validateFeatureAccess(request);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          featureName: 'webhook:create',
          hasApiKey: true,
        }),
        'Starting feature permission validation'
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          featureName: 'webhook:create',
          allowed: true,
        }),
        'Feature permission validation completed'
      );
    });

    it('should log errors appropriately', async () => {
      mockPrisma.user.findUnique.mockRejectedValue(new Error('DB Error'));

      const request: FeaturePermissionRequest = {
        userId: 'user-123',
        featureName: 'webhook:create',
      };

      await UnifiedPermissionService.validateFeatureAccess(request);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          featureName: 'webhook:create',
          error: 'DB Error',
        }),
        'Feature permission validation failed'
      );
    });
  });
});
