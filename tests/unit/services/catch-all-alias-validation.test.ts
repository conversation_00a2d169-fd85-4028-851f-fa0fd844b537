import { AliasService, isValidEmailOrCatchAll } from '../../../src/backend/services/user/alias.service.js';
import { prisma } from '../../../src/backend/lib/prisma.js';

// Mock the prisma client
jest.mock('../../../src/backend/lib/prisma.js', () => ({
  prisma: {
    domain: {
      findFirst: jest.fn(),
    },
    webhook: {
      findFirst: jest.fn(),
    },
    alias: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
  },
}));

describe('Catch-All Alias Validation', () => {
  let aliasService: AliasService;
  const mockUserId = 'test-user-id';
  const mockDomainId = 'test-domain-id';
  const mockWebhookId = 'test-webhook-id';

  beforeEach(() => {
    aliasService = new AliasService();
    jest.clearAllMocks();

    // Setup default mocks
    (prisma.domain.findFirst as jest.Mock).mockResolvedValue({
      id: mockDomainId,
      domain: 'example.com',
      verified: true,
      userId: mockUserId,
    });

    (prisma.webhook.findFirst as jest.Mock).mockResolvedValue({
      id: mockWebhookId,
      name: 'Test Webhook',
      url: 'https://example.com/webhook',
      verified: true,
      userId: mockUserId,
    });

    (prisma.alias.findFirst as jest.Mock).mockResolvedValue(null); // No existing alias

    (prisma.alias.create as jest.Mock).mockResolvedValue({
      id: 'test-alias-id',
      email: '*@example.com',
      active: true,
      domainId: mockDomainId,
      webhookId: mockWebhookId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  });

  describe('Catch-All Email Validation', () => {
    it('should accept catch-all email format (*@domain.com)', async () => {
      const aliasData = {
        email: '*@example.com',
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        userId: mockUserId,
      };

      const result = await aliasService.createAlias(aliasData);

      expect(result.success).toBe(true);
      expect(result.alias.email).toBe('*@example.com');
      expect(prisma.alias.create).toHaveBeenCalledWith({
        data: {
          email: '*@example.com',
          domainId: mockDomainId,
          webhookId: mockWebhookId,
          active: true,
        },
      });
    });

    it('should accept regular email format (<EMAIL>)', async () => {
      const aliasData = {
        email: '<EMAIL>',
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        userId: mockUserId,
      };

      (prisma.alias.create as jest.Mock).mockResolvedValue({
        id: 'test-alias-id',
        email: '<EMAIL>',
        active: true,
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const result = await aliasService.createAlias(aliasData);

      expect(result.success).toBe(true);
      expect(result.alias.email).toBe('<EMAIL>');
    });

    it('should reject invalid email formats', async () => {
      const aliasData = {
        email: 'invalid-email',
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        userId: mockUserId,
      };

      await expect(aliasService.createAlias(aliasData)).rejects.toThrow('Invalid email format');
    });

    it('should accept valid catch-all patterns', async () => {
      // Test the validation function directly
      const validCatchAllPatterns = [
        '*@example.com',
        '*@test.domain.com',
        '*@sub.example.org',
      ];

      for (const email of validCatchAllPatterns) {
        expect(isValidEmailOrCatchAll(email)).toBe(true);
      }
    });

    it('should reject malformed catch-all patterns', async () => {
      // Test each pattern individually to see which one is failing
      expect(isValidEmailOrCatchAll('**@example.com')).toBe(false);
      expect(isValidEmailOrCatchAll('*@')).toBe(false);
      expect(isValidEmailOrCatchAll('@example.com')).toBe(false);
      expect(isValidEmailOrCatchAll('*example.com')).toBe(false);
      expect(isValidEmailOrCatchAll('*@.com')).toBe(false);
    });

    it('should not apply reserved local part restrictions to catch-all aliases', async () => {
      // Catch-all aliases use "*" as local part, which should not be restricted
      const aliasData = {
        email: '*@example.com',
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        userId: mockUserId,
      };

      const result = await aliasService.createAlias(aliasData);

      expect(result.success).toBe(true);
      expect(result.alias.email).toBe('*@example.com');
    });

    it('should still apply reserved local part restrictions to regular aliases', async () => {
      const aliasData = {
        email: '<EMAIL>', // 'admin' is a reserved local part
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        userId: mockUserId,
      };

      await expect(aliasService.createAlias(aliasData)).rejects.toThrow(
        "The local part 'admin' is reserved and cannot be used for aliases"
      );
    });
  });

  describe('Update Alias Email Validation', () => {
    it('should accept catch-all email format when updating', async () => {
      const aliasId = 'test-alias-id';
      const updates = {
        email: '*@example.com',
      };

      (prisma.alias.findFirst as jest.Mock).mockResolvedValue({
        id: aliasId,
        email: '<EMAIL>',
        domainId: mockDomainId,
        webhookId: mockWebhookId,
      });

      (prisma.alias.update as jest.Mock) = jest.fn().mockResolvedValue({
        id: aliasId,
        email: '*@example.com',
        active: true,
        domainId: mockDomainId,
        webhookId: mockWebhookId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const result = await aliasService.updateAlias(aliasId, mockUserId, updates);

      expect(result.success).toBe(true);
    });

    it('should reject invalid email format when updating', async () => {
      const aliasId = 'test-alias-id';
      const updates = {
        email: 'invalid-email',
      };

      (prisma.alias.findFirst as jest.Mock).mockResolvedValue({
        id: aliasId,
        email: '<EMAIL>',
        domainId: mockDomainId,
        webhookId: mockWebhookId,
      });

      await expect(aliasService.updateAlias(aliasId, mockUserId, updates)).rejects.toThrow(
        'Invalid email format'
      );
    });
  });
});
