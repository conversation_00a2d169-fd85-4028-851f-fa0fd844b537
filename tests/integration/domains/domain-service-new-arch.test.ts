import { describe, test, expect } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
  createTestWebhook,
  createTestDomainWithCatchAll,
} from '../../setup/test-db-setup';
import { DomainService } from '../../../src/backend/services/user/domain.service';

setupTestDatabase();

describe('Domain Service - New Architecture (Catch-All Only)', () => {
  let domainService: DomainService;

  beforeEach(() => {
    domainService = new DomainService();
  });

  test('should create domain with catch-all alias automatically', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id, {
      name: 'Test Webhook',
      url: 'https://test.example.com/webhook',
    });

    const result = await domainService.createDomain({
      domain: 'test-new-arch.com',
      webhookId: testWebhook.id,
      userId: testUser.id,
      active: true,
      createCatchAll: true
    });

    expect(result.success).toBe(true);
    expect(result.domain.domain).toBe('test-new-arch.com');

    // Verify catch-all alias was created
    const catchAllAlias = await prisma.alias.findFirst({
      where: {
        domainId: result.domain.id,
        email: '*@test-new-arch.com'
      },
      
    });

    expect(catchAllAlias).toBeTruthy();
    // // expect(catchAllAlias!.webhookId).toBe(testWebhook.id); // TODO: Update for new architecture // TODO: Update for new architecture
    // expect(catchAllAlias!.webhook.name).toBe('Test Webhook'); // TODO: Update for new architecture
  });

  test('should get domain webhook info from catch-all alias', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id, {
      name: 'Production Webhook',
      url: 'https://prod.example.com/webhook',
    });

    const { domain } = await createTestDomainWithCatchAll(testUser.id, testWebhook.id, {
      domain: 'webhook-info-test.com'
    });

    const domainInfo = await domainService.getDomain(domain.id, testUser.id);

    expect(domainInfo).toBeTruthy();
    expect(domainInfo!.domain).toBe('webhook-info-test.com');
    // expect(domainInfo!.webhookUrl).toBe('https://prod.example.com/webhook'); // TODO: Update for new architecture
    // expect(domainInfo!.webhookName).toBe('Production Webhook'); // TODO: Update for new architecture
  });

  test('should update domain webhook via catch-all alias', async () => {
    const testUser = await createTestUser();
    const webhook1 = await createTestWebhook(testUser.id, {
      name: 'Original Webhook',
      url: 'https://original.example.com/webhook',
    });
    const webhook2 = await createTestWebhook(testUser.id, {
      name: 'Updated Webhook',
      url: 'https://updated.example.com/webhook',
    });

    const { domain } = await createTestDomainWithCatchAll(testUser.id, webhook1.id, {
      domain: 'webhook-update-test.com'
    });

    // Update domain webhook (should update catch-all alias)
    const result = await domainService.updateDomainWebhook(domain.id, testUser.id, webhook2.id);

    expect(result.success).toBe(true);
    // expect(result.webhook.name).toBe('Updated Webhook'); // TODO: Update for new architecture

    // Verify catch-all alias was updated
    const catchAllAlias = await prisma.alias.findFirst({
      where: {
        domainId: domain.id,
        email: '*@webhook-update-test.com'
      },
      
    });

    // // expect(catchAllAlias!.webhookId).toBe(webhook2.id); // TODO: Update for new architecture // TODO: Update for new architecture
    // expect(catchAllAlias!.webhook.name).toBe('Updated Webhook'); // TODO: Update for new architecture
  });

  test('should list domains with webhook info from catch-all aliases', async () => {
    const testUser = await createTestUser();
    const webhook1 = await createTestWebhook(testUser.id, {
      name: 'Webhook 1',
      url: 'https://webhook1.example.com/webhook',
    });
    const webhook2 = await createTestWebhook(testUser.id, {
      name: 'Webhook 2',
      url: 'https://webhook2.example.com/webhook',
    });

    // Create two domains with different webhooks
    await createTestDomainWithCatchAll(testUser.id, webhook1.id, {
      domain: 'domain1.com'
    });
    await createTestDomainWithCatchAll(testUser.id, webhook2.id, {
      domain: 'domain2.com'
    });

    const result = await domainService.getUserDomains(testUser.id);

    expect(result.domains).toHaveLength(2);
    expect(result.total).toBe(2);

    const domain1 = result.domains.find(d => d.domain === 'domain1.com');
    const domain2 = result.domains.find(d => d.domain === 'domain2.com');

    // expect(domain1!.webhook!.name).toBe('Webhook 1'); // TODO: Update for new architecture
    // expect(domain2!.webhook!.name).toBe('Webhook 2'); // TODO: Update for new architecture
  });

  test('should handle domain without catch-all alias gracefully', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id);

    // Create domain without catch-all alias (edge case)
    const domain = await prisma.domain.create({
      data: {
        domain: 'no-catchall.com',
        userId: testUser.id,
        verified: true,
        verificationStatus: 'VERIFIED'
      }
    });

    const domainInfo = await domainService.getDomain(domain.id, testUser.id);

    expect(domainInfo).toBeTruthy();
    expect(domainInfo!.domain).toBe('no-catchall.com');
    // expect(domainInfo!.webhookUrl).toBeUndefined(); // TODO: Update for new architecture
    // expect(domainInfo!.webhookName).toBeUndefined(); // TODO: Update for new architecture
  });

  test('should fail to update webhook if no catch-all alias exists', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id);

    // Create domain without catch-all alias
    const domain = await prisma.domain.create({
      data: {
        domain: 'no-catchall-update.com',
        userId: testUser.id,
        verified: true,
        verificationStatus: 'VERIFIED'
      }
    });

    await expect(
      domainService.updateDomainWebhook(domain.id, testUser.id, testWebhook.id)
    ).rejects.toThrow('No catch-all alias found for domain');
  });

  test('should prevent deletion of catch-all aliases', async () => {
    const testUser = await createTestUser();
    const testWebhook = await createTestWebhook(testUser.id);
    const { domain, catchAllAlias } = await createTestDomainWithCatchAll(testUser.id, testWebhook.id);

    // Import AliasService
    const { AliasService } = await import('../../../src/backend/services/user/alias.service');
    const aliasService = new AliasService();

    // Attempt to delete catch-all alias should fail
    await expect(
      aliasService.deleteAlias(catchAllAlias.id, testUser.id)
    ).rejects.toThrow('Cannot delete catch-all aliases');
  });
});
