#!/usr/bin/env node

/**
 * Email test simulator
 * This script generates test emails and sends them to our processing endpoint
 * [TODO] Verify necessity and accuracy of this test component.
 */

import axios from 'axios';
// import fs from 'fs';
// import path from 'path';

// Sample raw email for testing
const sampleEmail = `Return-Path: <<EMAIL>>
Delivered-To: <EMAIL>
Received: from mail.test.com (mail.test.com [*************])
    by mail.yourdomain.com (Postfix) with ESMTP id 12345
    for <<EMAIL>>; Sat, 25 May 2025 12:30:00 +0200 (CEST)
From: Test Sender <<EMAIL>>
To: <EMAIL>
Subject: Test Email for Webhook Processing
Date: Sat, 25 May 2025 12:30:00 +0200
Message-ID: <<EMAIL>>
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 7bit

This is a test email to verify that our email-to-webhook processing system works correctly.

The email should be:
1. Parsed correctly
2. Converted to JSON
3. Queued for webhook delivery
4. Successfully delivered to the configured endpoint

Best regards,
Test System
`;

async function testEmailProcessing() {
  try {
    console.log('🧪 Testing email processing...');
    
    // Send the raw email to our processing endpoint
    const response = await axios.post('http://localhost:3000/api/email/process', sampleEmail, {
      headers: {
        'Content-Type': 'text/plain',
        'X-Test-Email': 'true',
      },
    });

    console.log('✅ Email processing response:', response.data);
    
    // Check webhook stats
    const statsResponse = await axios.get('http://localhost:3000/api/webhook/stats');
    console.log('📊 Webhook stats:', statsResponse.data);
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

async function testWithDifferentDomains() {
  const testEmails = [
    {
      domain: 'test.example.com',
      email: sampleEmail.replace('yourdomain.com', 'test.example.com'),
    },
    {
      domain: 'another.test.com', 
      email: sampleEmail.replace('yourdomain.com', 'another.test.com'),
    },
  ];

  for (const test of testEmails) {
    console.log(`\\n🧪 Testing domain: ${test.domain}`);
    
    try {
      const response = await axios.post('http://localhost:3000/api/email/process', test.email, {
        headers: {
          'Content-Type': 'text/plain',
          'X-Test-Domain': test.domain,
        },
      });
      
      console.log(`✅ ${test.domain}:`, response.data);
    } catch (error) {
      console.log(`❌ ${test.domain}:`, error.response?.data || error.message);
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 EU Email Webhook Test Suite\\n');
  
  // Test 1: Basic email processing
  await testEmailProcessing();
  
  // Test 2: Multiple domains
  await testWithDifferentDomains();
  
  // Test 3: Check final stats
  console.log('\\n📈 Final webhook statistics:');
  try {
    const finalStats = await axios.get('http://localhost:3000/api/webhook/stats');
    console.log(finalStats.data);
  } catch (error) {
    console.error('Failed to get final stats:', error.message);
  }
}

main().catch(console.error);
