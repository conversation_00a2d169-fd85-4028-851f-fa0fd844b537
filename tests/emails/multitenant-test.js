#!/usr/bin/env node

/**
 * Multi-tenant domain management test
 * Demonstrates programmatic Postfix configuration
 * [TODO] Verify necessity and accuracy of this test.
 */

import axios from 'axios';

const API_BASE = 'http://localhost:3000/api/config';

async function testDomainManagement() {
  console.log('🏢 Testing Multi-tenant Domain Management\\n');

  try {
    // 1. Check initial Postfix status
    console.log('📊 Checking Postfix status...');
    const statusResponse = await axios.get(`${API_BASE}/postfix/status`);
    console.log('✅ Postfix Status:', statusResponse.data);
    console.log();

    // 2. Add multiple customer domains
    const customerDomains = [
      {
        domain: 'customer1.com',
        webhookUrl: 'https://customer1.example.com/webhook',
        active: true,
      },
      {
        domain: 'customer2.com', 
        webhookUrl: 'https://customer2.example.com/webhook',
        active: true,
      },
      {
        domain: 'customer3.com',
        webhookUrl: 'https://customer3.example.com/webhook',
        active: true,
      },
    ];

    console.log('➕ Adding customer domains programmatically...');
    
    for (const config of customerDomains) {
      try {
        const response = await axios.post(`${API_BASE}/domains`, config);
        console.log(`✅ Added ${config.domain}:`, {
          success: response.data.success,
          postfix_status: response.data.postfix_status,
        });
      } catch (error) {
        console.log(`❌ Failed to add ${config.domain}:`, error.response?.data || error.message);
      }
    }
    console.log();

    // 3. List all configured domains
    console.log('📋 Listing all configured domains...');
    const domainsResponse = await axios.get(`${API_BASE}/domains`);
    console.log('✅ Configured domains:', domainsResponse.data);
    console.log();

    // 4. Test removing a domain
    console.log('🗑️  Testing domain removal...');
    try {
      const removeResponse = await axios.delete(`${API_BASE}/domains/customer2.com`);
      console.log('✅ Removed customer2.com:', removeResponse.data);
    } catch (error) {
      console.log('❌ Failed to remove domain:', error.response?.data || error.message);
    }
    console.log();

    // 5. Final domain list
    console.log('📋 Final domain configuration...');
    const finalDomainsResponse = await axios.get(`${API_BASE}/domains`);
    console.log('✅ Final domains:', finalDomainsResponse.data);
    console.log();

    // 6. Final Postfix status
    console.log('📊 Final Postfix status...');
    const finalStatusResponse = await axios.get(`${API_BASE}/postfix/status`);
    console.log('✅ Final Status:', finalStatusResponse.data);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

async function simulateCustomerOnboarding() {
  console.log('\\n🎯 Simulating Customer Onboarding Flow\\n');

  const newCustomer = {
    domain: 'newcustomer.io',
    webhookUrl: 'https://newcustomer.io/api/email-webhook',
    active: true,
  };

  try {
    console.log(`🔧 Onboarding customer: ${newCustomer.domain}`);
    
    // Add domain (this automatically configures Postfix)
    const response = await axios.post(`${API_BASE}/domains`, newCustomer);
    
    console.log('✅ Customer onboarded successfully!');
    console.log('📋 DNS Instructions for customer:');
    console.log('   MX Record:', response.data.instructions.mx_record);
    console.log('   TXT Record:', response.data.instructions.txt_record);
    console.log('   Postfix Status:', response.data.postfix_status);
    
  } catch (error) {
    console.error('❌ Customer onboarding failed:', error.response?.data || error.message);
  }
}

// Main execution
async function main() {
  console.log('🚀 EU Email Webhook - Multi-tenant Domain Management Test\\n');
  
  await testDomainManagement();
  await simulateCustomerOnboarding();
  
  console.log('\\n✅ Multi-tenant testing completed!');
}

main().catch(console.error);
