# 🧪 End-to-End Email Testing

This directory contains comprehensive end-to-end tests for the EU Email Webhook system.

## 🎯 Remote E2E Test (`remote-e2e-test.js`)

**Complete end-to-end test using real APIs:**
- ✅ **Real email sending** via ForwardEmail API
- ✅ **Real webhook endpoint** via webhook-test.com
- ✅ **Production system testing** against your live server
- ✅ **Full pipeline validation** from email to webhook delivery

### 🚀 Quick Start

```bash
# Install dependencies (if not already done)
npm install

# Set API key for authentication (required)
export API_KEY="your-jwt-secret-here"

# Run against production server
npm run test:e2e

# Run against local development server
npm run test:e2e:local
```

### 🔧 Configuration

The test uses these APIs with your provided credentials:

**Email Sending (ForwardEmail):**
- Domain: `in.xadi.nl` (pre-configured with MX/TXT records)
- From: `<EMAIL>`
- API: `https://api.forwardemail.net/v1/emails`

**Webhook Testing (webhook-test.com):**
- Creates temporary webhook endpoints
- Monitors webhook delivery
- API: `https://webhook-test.com/api/webhooks`

**Target System:**
- Production: `https://emailconnect.eu` (default)
- Local: `http://localhost:3000` (with `test:e2e:local`)

### 📋 Test Flow

1. **🌐 Create Webhook Endpoint** - Creates temporary webhook via webhook-test.com
2. **📝 Register Domain** - Registers `in.xadi.nl` in your system with webhook URL
3. **📧 Send Real Email** - Sends email via ForwardEmail API to test address
4. **⏳ Monitor Processing** - Waits for email processing through your system
5. **🔍 Verify Webhook** - Confirms webhook delivery and validates payload
6. **🧹 Cleanup** - Removes temporary webhook endpoint

### 📊 Test Results

The test validates:
- ✅ **Webhook Creation** - Temporary endpoint created successfully
- ✅ **Domain Registration** - Domain added to your system
- ✅ **Email Sending** - Real email sent via API
- ✅ **Webhook Delivery** - Webhook received at endpoint
- ✅ **Payload Validation** - All required fields present and correct

### 🔍 Payload Validation

Checks for:
- Message ID matching
- Sender information
- Recipient information
- Subject line
- Email content (text/HTML)
- Timestamp
- Domain information

### 🛠️ Troubleshooting

**Common Issues:**

1. **Domain Already Exists**
   ```
   Error: Domain already exists
   ```
   - The test domain is already registered
   - Either delete it manually or the test will handle conflicts

2. **Webhook Timeout**
   ```
   Timeout waiting for webhook delivery
   ```
   - Check if your system is processing emails
   - Verify MX records for `in.xadi.nl`
   - Check system health endpoints

3. **Email Sending Failed**
   ```
   Failed to send email via ForwardEmail API
   ```
   - Verify ForwardEmail API credentials
   - Check rate limits
   - Ensure `<EMAIL>` is authorized

### 📈 Advanced Usage

**Custom Configuration:**
```bash
# Test against different server
API_BASE=https://staging.emailconnect.eu npm run test:e2e

# Increase webhook timeout
WEBHOOK_DELIVERY_TIMEOUT=180000 npm run test:e2e

# Use different test domain (must have MX records)
TEST_DOMAIN=your-test.domain.com npm run test:e2e
```

**Environment Variables:**
- `API_BASE` - Target system URL
- `TEST_DOMAIN` - Domain to test (default: `in.xadi.nl`)
- `WEBHOOK_DELIVERY_TIMEOUT` - Max wait time for webhook (default: 120s)
- `POLLING_INTERVAL` - How often to check for webhook (default: 3s)

## 🧪 Other Test Files

- `test-runner.js` - Basic email processing simulation
- `postfix-simulation.js` - Simulates Postfix email delivery
- `multitenant-test.js` - Multi-domain testing

## 🎯 Success Criteria

**Test passes when:**
1. Webhook endpoint created successfully
2. Domain registered in your system
3. Real email sent via ForwardEmail API
4. Webhook received with correct payload
5. All payload fields validated successfully

**This confirms your entire email-to-webhook pipeline is working correctly!** 🎉
