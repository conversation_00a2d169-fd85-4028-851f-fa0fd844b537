#!/usr/bin/env node

/**
 * Postfix simulation script
 * This simulates how Postfix would pipe an email to our processing service
 * [TODO] Verify necessity and accuracy of this test.
 */

import http from 'http';

// Sample email that would come from Postfix
const incomingEmail = `Return-Path: <<EMAIL>>
Delivered-To: <EMAIL>  
Received: from mail.customer.com (mail.customer.com [************])
    by mail.yourdomain.com (Postfix) with ESMTP id AB123CD456
    for <<EMAIL>>; Sat, 25 May 2025 14:15:30 +0200 (CEST)
From: <PERSON> <<EMAIL>>
To: Support Team <<EMAIL>>
Subject: Help with account setup
Date: Sat, 25 May 2025 14:15:30 +0200
Message-ID: <<EMAIL>>
MIME-Version: 1.0
Content-Type: multipart/alternative; boundary="boundary123"

--boundary123
Content-Type: text/plain; charset=UTF-8

Hi Support Team,

I'm having trouble setting up my account. Could you please help me with the following:

1. Password reset process
2. Email verification issues
3. Account activation

My account ID is: ACC-12345

Thanks for your help!

Best regards,
<PERSON>er

--boundary123
Content-Type: text/html; charset=UTF-8

<html>
<body>
<p>Hi Support Team,</p>

<p>I'm having trouble setting up my account. Could you please help me with the following:</p>

<ol>
<li>Password reset process</li>
<li>Email verification issues</li>
<li>Account activation</li>
</ol>

<p>My account ID is: <strong>ACC-12345</strong></p>

<p>Thanks for your help!</p>

<p>Best regards,<br>
John Customer</p>
</body>
</html>

--boundary123--
`;

function processEmailViaHTTP(email) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/email/process',
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Content-Length': Buffer.byteLength(email),
        'X-Email-Source': 'postfix-simulation',
      },
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log('✅ Email processed successfully:', JSON.parse(responseData));
          resolve(responseData);
        } else {
          console.error('❌ Email processing failed:', res.statusCode, responseData);
          reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Request failed:', error.message);
      reject(error);
    });

    req.write(email);
    req.end();
  });
}

async function main() {
  console.log('📧 Simulating Postfix email delivery...');
  console.log('📨 Processing incoming support email...');
  
  try {
    await processEmailViaHTTP(incomingEmail);
    
    // Check final webhook stats
    console.log('\\n📊 Checking webhook delivery stats...');
    
    const statsReq = http.request({
      hostname: 'localhost',
      port: 3000,
      path: '/api/webhook/stats',
      method: 'GET',
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('📈 Webhook Stats:', JSON.parse(data));
      });
    });
    
    statsReq.end();
    
  } catch (error) {
    console.error('Process failed:', error.message);
    process.exit(1);
  }
}

main();
