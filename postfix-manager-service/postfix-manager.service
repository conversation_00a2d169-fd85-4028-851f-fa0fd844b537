[Unit]
Description=Postfix Manager Service for EU Email Webhook
After=network.target postfix.service
Requires=postfix.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/etc/postfix-manager
ExecStart=/usr/local/bin/postfix-manager
Restart=always
RestartSec=5

# Security settings (even though running as root)
NoNewPrivileges=true
ProtectHome=true
ProtectKernelTunables=true
ProtectControlGroups=true
RestrictRealtime=true

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=postfix-manager

[Install]
WantedBy=multi-user.target
