#!/bin/bash

# Build script for Postfix Manager Service
set -e

echo "🔨 Building Postfix Manager Service..."

# Initialize Go module if needed
if [ ! -f "go.sum" ]; then
    echo "📦 Initializing Go modules..."
    go mod tidy
fi

# Build the binary
echo "⚙️  Building binary..."
go build -o postfix-manager main.go

# Make executable
chmod +x postfix-manager

echo "✅ Build completed: $(pwd)/postfix-manager"
echo ""
echo "📋 Next steps:"
echo "1. sudo ./postfix-manager                    # Test run"
echo "2. sudo ./install.sh                        # Install as systemd service"
echo "3. curl http://localhost:3001/health         # Test API"
