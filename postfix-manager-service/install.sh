#!/bin/bash

# Installation script for Postfix Manager Service
# Updated for /opt/eu-email-webhook standardization

set -e

if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "🚀 Installing Postfix Manager Service..."

# Build if binary doesn't exist
if [ ! -f "postfix-manager" ]; then
    echo "📦 Building binary first..."
    ./build.sh
fi

# Copy binary to system location
echo "📦 Installing binary..."
cp postfix-manager /usr/local/bin/postfix-manager
chmod +x /usr/local/bin/postfix-manager

# Create config directory and copy config
echo "⚙️  Setting up configuration..."
mkdir -p /etc/postfix-manager
if [ ! -f "/etc/postfix-manager/config.env" ]; then
    cp config.env.example /etc/postfix-manager/config.env
    echo "📝 Config created at /etc/postfix-manager/config.env"
fi

# Create application data directory
echo "📁 Creating application directories..."
mkdir -p /opt/eu-email-webhook/data
mkdir -p /opt/eu-email-webhook/scripts
chmod 755 /opt/eu-email-webhook/data

# Update systemd service file with correct paths
echo "📋 Installing systemd service..."
sed -e 's|/home/<USER>/webapps/eu-email-webhook/postfix-manager-service/postfix-manager|/usr/local/bin/postfix-manager|g' \
    -e 's|WorkingDirectory=.*|WorkingDirectory=/opt/eu-email-webhook|g' \
    postfix-manager.service > /etc/systemd/system/postfix-manager.service

# Reload systemd
systemctl daemon-reload

# Enable and start service
systemctl enable postfix-manager.service
systemctl start postfix-manager.service

# Check status
echo ""
echo "✅ Installation completed!"
echo ""
echo "📊 Service Status:"
systemctl status postfix-manager.service --no-pager -l

echo ""
echo "🔗 API Endpoints:"
echo "  Health:  http://localhost:3001/health"
echo "  Status:  http://localhost:3001/status"
echo "  Setup:   curl -X POST http://localhost:3001/setup"
echo ""
echo "📝 Management:"
echo "  Logs: journalctl -u postfix-manager.service -f"
echo "  Config: /etc/postfix-manager/config.env"
echo "  Data: /opt/eu-email-webhook/data/"
echo "  SQLite DB: /opt/eu-email-webhook/data/postfix.db"
echo ""
echo "🧪 Test the service:"
echo "  curl http://localhost:3001/health"
echo "  curl http://localhost:3001/status"
