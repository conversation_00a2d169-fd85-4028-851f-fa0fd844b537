#!/bin/bash

# Uninstall script for Postfix Manager Service
set -e

if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (use sudo)"
    exit 1
fi

echo "🗑️  Uninstalling Postfix Manager Service..."

# Stop and disable service
systemctl stop postfix-manager.service || true
systemctl disable postfix-manager.service || true

# Remove systemd service file
rm -f /etc/systemd/system/postfix-manager.service

# Remove binary
rm -f /usr/local/bin/postfix-manager

# Reload systemd
systemctl daemon-reload

echo "✅ Postfix Manager Service uninstalled successfully!"
echo "📝 Note: Configuration files in /etc/postfix-manager/ were kept"
