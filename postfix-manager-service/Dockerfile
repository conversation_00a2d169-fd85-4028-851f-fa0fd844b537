# Multi-stage Dockerfile for Postfix Manager Go service with SQLite support
FROM golang:1.23-alpine AS builder

# Install build dependencies including SQLite
RUN apk add --no-cache git ca-certificates build-base sqlite-dev

# Set working directory
WORKDIR /app

# Copy go module files
COPY postfix-manager-service/go.mod postfix-manager-service/go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code - SQLite version (now renamed to main.go)
COPY postfix-manager-service/main.go ./main.go

# Build the binary with CGO enabled for SQLite
RUN CGO_ENABLED=1 GOOS=linux go build -a -ldflags '-linkmode external -extldflags "-static"' -o postfix-manager .

# Final stage - minimal image with SQLite support
FROM debian:stable-slim AS final

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
RUN mkdir -p /opt/eu-email-webhook/data

# Set working directory
WORKDIR /root/

# Copy binary from builder stage
COPY --from=builder /app/postfix-manager .

# Copy SQLite schema and configuration files from project root
COPY postfix-sqlite/ /opt/eu-email-webhook/postfix-sqlite/

# Make binary executable
RUN chmod +x ./postfix-manager

# Expose port
EXPOSE 3001

# Health check using curl instead of wget
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Run as root (required for postfix management)
USER root

# Start the service
CMD ["./postfix-manager"]
