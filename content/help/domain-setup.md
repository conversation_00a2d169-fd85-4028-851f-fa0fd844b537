---
title: Domain setup and verification
slug: domain-setup
excerpt: Complete guide to adding and verifying your domain for email processing
category: configuration
order: 2
---

# Domain Setup and Verification

Setting up your domain correctly is crucial for receiving emails through EmailConnect. This guide covers everything you need to know.

## Prerequisites

- Access to your domain's DNS settings
- Administrative rights to modify DNS records
- Your domain should be active and resolving

## Step-by-Step Setup

### 1. Add Your Domain

1. Navigate to **Domains** in your dashboard
2. Click **Add Domain**
3. Enter your domain name without any subdomain (e.g., `example.com`, not `www.example.com`)
4. Click **Add Domain**

### 2. Configure MX Records

MX (Mail Exchange) records tell email servers where to deliver emails for your domain.

**Required MX Record:**
```
Type: MX
Name: @ (or leave blank)
Value: mx.emailconnect.eu
Priority: 10
TTL: 3600 (or your provider's default)
```

**Important Notes:**
- Remove any existing MX records that point to other email services
- If you need to keep existing email service, contact support for advanced configuration
- Changes may take up to 24 hours to propagate globally

### 3. Add TXT Verification Record

A TXT record is used to verify domain ownership.

**Required TXT Record:**
```
Type: TXT
Name: @ (or leave blank)
Value: verify-ec=yourdomain.com
TTL: 3600
```

### 4. Verification Process

After adding the DNS records:

1. Wait 5-10 minutes for initial DNS propagation
2. Click **Verify Domain** in your dashboard
3. The system will check your DNS configuration
4. Verification typically completes within 1-2 minutes

**Verification Status:**
- **Pending**: DNS records not yet detected
- **Verified**: Domain is ready to receive emails
- **Failed**: DNS configuration issues detected

## Troubleshooting

### Common Issues

**"MX record not found"**
- Double-check the MX record value: `mx1.emailconnect.eu`
- Ensure priority is set to 10
- Wait longer for DNS propagation (up to 24 hours)

**"TXT verification record missing"**
- Verify the TXT record value exactly matches: `verify-ec=yourdomain.com`
- Check that the record is added to the domain level of your choice (@ vs e.g. in.)

**"Verification taking too long"**
- DNS changes can take up to 24 hours to propagate globally
- Use online DNS checker tools to verify your records
- Contact support if issues persist after 24 hours

### DNS Checker Tools

Use these tools to verify your DNS configuration:
- [MXToolbox](https://mxtoolbox.com/)
- [DNSChecker](https://dnschecker.org/)
- [Google Admin Toolbox](https://toolbox.googleapps.com/apps/dig/)

## Advanced Configuration

### Subdomain Setup

You can also configure subdomains for email processing:

1. Add the subdomain as a separate domain (e.g., `mail.example.com`)
2. Configure MX and TXT records for the subdomain
3. Follow the same verification process

### Multiple Email Services

If you need to use EmailConnect alongside another email service:

1. Contact our support team for custom MX record configuration
2. We can help set up priority-based routing
3. Additional configuration may be required

## Next Steps

Once your domain is verified:
- [Create aliases](./aliases) for specific email addresses
- [Set up webhooks](./webhooks) to process emails
- [Test your configuration](./aliases) with sample emails

## Need Help?

Domain setup issues? Our support team is here to help:
- Check our [help center](./help)
- Contact support with your domain name and DNS provider
- We can help verify your configuration remotely
