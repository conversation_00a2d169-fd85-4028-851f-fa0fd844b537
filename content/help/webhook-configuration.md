---
title: Webhook Configuration
slug: webhook-configuration
excerpt: Learn how to set up and configure webhooks to receive processed email data
category: configuration
order: 3
---

# Webhook Configuration

Webhooks are HTTP endpoints that receive processed email data from EmailConnect. This guide covers everything you need to know about setting up and managing webhooks.

## What are Webhooks?

When an email is received by EmailConnect, it's processed and sent as an HTTP POST request to your webhook URL. This allows your application to:

- Receive email data in real-time
- Process emails programmatically
- Integrate with existing systems
- Trigger automated workflows

## Webhook Data Format

EmailConnect sends email data in JSON format:

```json
{
  "id": "email_123456",
  "timestamp": "2024-06-24T10:30:00Z",
  "from": {
    "email": "<EMAIL>",
    "name": "<PERSON>"
  },
  "to": [
    {
      "email": "<EMAIL>",
      "name": "Support Team"
    }
  ],
  "subject": "Customer inquiry",
  "text": "Plain text version of the email",
  "html": "<p>HTML version of the email</p>",
  "attachments": [
    {
      "filename": "document.pdf",
      "contentType": "application/pdf",
      "size": 12345,
      "data": "base64-encoded-content"
    }
  ],
  "headers": {
    "message-id": "<unique-message-id>",
    "date": "Mon, 24 Jun 2024 10:30:00 +0000"
  }
}
```

## Setting Up Webhooks

### 1. Create a Webhook Endpoint

Your webhook endpoint should:
- Accept HTTP POST requests
- Return a 200 status code for successful processing
- Process requests within 30 seconds (timeout limit)
- Handle JSON content type

**Example webhook endpoint (Node.js/Express):**

```javascript
app.post('/webhook/emails', (req, res) => {
  const emailData = req.body;
  
  // Process the email data
  console.log('Received email:', emailData.subject);
  
  // Your processing logic here
  processEmail(emailData);
  
  // Return success response
  res.status(200).json({ success: true });
});
```

### 2. Add Webhook in Dashboard

1. Go to **Webhooks** in your dashboard
2. Click **Create Webhook**
3. Enter your webhook URL
4. Choose which events to receive
5. Test the webhook connection
6. Save the webhook

### 3. Configure Webhook Security

**Webhook Signatures:**
EmailConnect signs webhook requests with a secret key. Verify signatures to ensure requests are authentic:

```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');
  
  return signature === `sha256=${expectedSignature}`;
}
```

## Webhook Events

Configure which events trigger webhook calls:

- **Email Received**: New email processed
- **Delivery Failed**: Email processing failed
- **Bounce Received**: Bounce notification received
- **Spam Detected**: Email marked as spam

## Testing Webhooks

### Built-in Testing

Use the webhook testing feature in your dashboard:

1. Go to your webhook configuration
2. Click **Test Webhook**
3. Review the test response
4. Check your endpoint logs

### Manual Testing

Send a test email to your alias and monitor:
- Your webhook endpoint logs
- EmailConnect delivery logs
- Response times and status codes

## Best Practices

### Reliability

- **Idempotency**: Handle duplicate webhook calls gracefully
- **Timeouts**: Respond within 30 seconds to avoid retries
- **Error Handling**: Return appropriate HTTP status codes
- **Logging**: Log all webhook requests for debugging

### Security

- **HTTPS Only**: Always use HTTPS for webhook URLs
- **Signature Verification**: Verify webhook signatures
- **IP Whitelisting**: Restrict access to EmailConnect IPs
- **Rate Limiting**: Implement rate limiting on your endpoint

### Performance

- **Async Processing**: Process emails asynchronously when possible
- **Queue Systems**: Use queues for heavy processing
- **Database Optimization**: Optimize database operations
- **Monitoring**: Monitor webhook performance and errors

## Troubleshooting

### Common Issues

**"Webhook timeout"**
- Ensure your endpoint responds within 30 seconds
- Move heavy processing to background jobs
- Check server performance and resources

**"Connection refused"**
- Verify your webhook URL is accessible from the internet
- Check firewall settings
- Ensure your server is running

**"Invalid signature"**
- Verify you're using the correct webhook secret
- Check signature calculation implementation
- Ensure payload is not modified before verification

### Debugging Tools

- **Webhook logs**: Check delivery logs in your dashboard
- **Request inspection**: Use tools like ngrok for local testing
- **Network monitoring**: Monitor network connectivity and latency

## Advanced Configuration

### Multiple Webhooks

You can configure multiple webhooks for different purposes:
- Primary webhook for main processing
- Backup webhook for redundancy
- Specialized webhooks for specific email types

### Conditional Webhooks

Configure webhooks to trigger based on conditions:
- Sender email address
- Subject line patterns
- Attachment presence
- Email size

## Next Steps

- [Set up email aliases](./alias-management) to route emails to webhooks
- [Learn about email processing](./email-processing) options
- [Configure attachment handling](./attachment-processing)

## Need Help?

Having webhook issues?
- Check our [troubleshooting guide](./troubleshooting)
- Review webhook logs in your dashboard
- Contact support with webhook URL and error details
