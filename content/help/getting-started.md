---
title: Getting Started with EmailConnect
slug: getting-started
excerpt: Learn the basics of setting up your EmailConnect account and processing your first emails
category: getting-started
order: 1
---

# Getting Started with EmailConnect

Welcome to EmailConnect! This guide will help you get up and running with email processing in just a few minutes.

## What is EmailConnect?

EmailConnect is a service that converts incoming emails into webhook calls, allowing you to process emails programmatically in your applications. It's perfect for:

- Customer support systems
- Order processing workflows
- Newsletter management
- Automated email responses
- Integration with existing business systems

## Quick Setup Process

### 1. Create Your Account

If you haven't already, [sign up for a free account](https://emailconnect.eu/register). The free plan includes:
- 100 emails per month
- 1 domain
- 3 aliases
- 2 webhooks

### 2. Add Your Domain

1. Go to the **Domains** section in your dashboard
2. Click **Add Domain**
3. Enter your domain name (e.g., `yourdomain.com`)
4. Follow the DNS configuration instructions

### 3. Configure DNS Records

You'll need to add these DNS records to your domain:

```
Type: MX
Name: @
Value: mx.emailconnect.eu
Priority: 10

Type: TXT
Name: @
Value: v=spf1 include:emailconnect.eu ~all
```

### 4. Create an Alias

1. Go to the **Aliases** section
2. Click **Create Alias**
3. Choose your domain
4. Set up your email pattern (e.g., `<EMAIL>`)
5. Configure your webhook URL

### 5. Test Your Setup

Send a test email to your alias and check your webhook endpoint to see the processed email data.

## Next Steps

- [Set up webhooks](./webhook-configuration) to process incoming emails
- [Configure aliases](./alias-management) for different email addresses
- [Learn about email processing](./email-processing) options

## Need Help?

If you run into any issues, check our [troubleshooting guide](./troubleshooting) or contact our support team.
