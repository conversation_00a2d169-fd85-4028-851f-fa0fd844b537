#!/bin/bash
set -e

echo "🔧 Setting up EU Email Webhook development environment..."

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS)
echo "📦 Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Go 1.21+
echo "📦 Installing Go..."
wget -q https://go.dev/dl/go1.21.6.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.21.6.linux-amd64.tar.gz
rm go1.21.6.linux-amd64.tar.gz

# Add Go to PATH
echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
export PATH=$PATH:/usr/local/go/bin

# Install PostgreSQL client (for potential database tests)
echo "📦 Installing PostgreSQL client..."
sudo apt-get install -y postgresql-client

# Install Redis tools (for potential Redis tests)
echo "📦 Installing Redis tools..."
sudo apt-get install -y redis-tools

# Navigate to workspace
cd /mnt/persist/workspace

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm ci

# Install additional Jest dependencies for TypeScript support
echo "📦 Installing Jest TypeScript dependencies..."
npm install --save-dev ts-jest @jest/globals supertest

# Install Go dependencies for postfix-manager
echo "📦 Installing Go dependencies..."
cd postfix-manager-service
go mod download
go mod verify
cd ..

# Create Jest configuration file with proper timeout handling
echo "🔧 Creating Jest configuration..."
cat > jest.config.js << 'EOF'
export default {
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',
  testMatch: ['<rootDir>/tests/**/*.test.ts', '<rootDir>/tests/**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/index.ts',
    '!src/**/*.d.ts'
  ],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        module: 'ESNext',
        target: 'ES2022'
      }
    }]
  },
  setupFilesAfterEnv: [],
  testTimeout: 10000,
  forceExit: true,
  detectOpenHandles: true
};
EOF

# Fix the DNS verifier test file to match the actual implementation
echo "🔧 Fixing DNS verifier test file..."
cat > tests/services/dns-verifier.test.ts << 'EOF'
import { DNSVerifier, DNSVerificationResult } from '../../src/services/dns-verifier';
import * as dns from 'dns';
import { env } from '../../src/config/env';

// Mock the dns module
jest.mock('dns', () => ({
  promises: {
    resolveTxt: jest.fn(),
  },
}));

// Mock logger to prevent console output during tests
jest.mock('../../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock env variables used by DnsVerifier
jest.mock('../../src/config/env', () => ({
  env: {
    DNS_VERIFICATION_RETRY_ATTEMPTS: 3,
    DNS_VERIFICATION_TIMEOUT_MS: 2000,
    DNS_VERIFICATION_CACHE_TTL_MS: 3600000, // 1 hour
  },
}));

describe('DNSVerifier', () => {
  let dnsVerifier: DNSVerifier;
  const mockDnsPromises = dns.promises as jest.Mocked<typeof dns.promises>;

  beforeEach(() => {
    dnsVerifier = new DNSVerifier();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('verifyDomainOwnership', () => {
    const testDomain = 'example.com';
    const expectedTxtRecord = `verify-ec=${testDomain}`;

    it('should return true if the correct TXT record is found', async () => {
      mockDnsPromises.resolveTxt.mockResolvedValueOnce([[`${expectedTxtRecord}`]]);

      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(testDomain);

      expect(result.verified).toBe(true);
      expect(result.domain).toBe(testDomain);
      expect(result.expectedRecord).toBe(expectedTxtRecord);
      expect(result.foundRecords).toEqual([expectedTxtRecord]);
      expect(result.cached).toBe(false);
      expect(result.error).toBeUndefined();
      expect(mockDnsPromises.resolveTxt).toHaveBeenCalledWith(testDomain);
    });

    it('should return true if the correct TXT record is among others', async () => {
      mockDnsPromises.resolveTxt.mockResolvedValueOnce([
        ['some-other-record'],
        [`${expectedTxtRecord}`],
        ['another-record=test']
      ]);

      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(testDomain);

      expect(result.verified).toBe(true);
      expect(result.domain).toBe(testDomain);
      expect(result.foundRecords).toEqual(['some-other-record', expectedTxtRecord, 'another-record=test']);
      expect(result.cached).toBe(false);
    });

    it('should return false if the correct TXT record is not found', async () => {
      mockDnsPromises.resolveTxt.mockResolvedValueOnce([
        ['some-other-record'],
        ['another-record=test']
      ]);

      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(testDomain);

      expect(result.verified).toBe(false);
      expect(result.domain).toBe(testDomain);
      expect(result.foundRecords).toEqual(['some-other-record', 'another-record=test']);
      expect(result.cached).toBe(false);
    });

    it('should return false and include error if DNS lookup fails', async () => {
      const dnsError = new Error('DNS lookup failed');
      mockDnsPromises.resolveTxt.mockRejectedValue(dnsError);

      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(testDomain);

      expect(result.verified).toBe(false);
      expect(result.domain).toBe(testDomain);
      expect(result.error).toBe('DNS lookup failed');
      expect(result.cached).toBe(false);
      expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(env.DNS_VERIFICATION_RETRY_ATTEMPTS);
    });

    it('should handle empty domain by attempting DNS lookup', async () => {
      const dnsError = new Error('Invalid domain name');
      mockDnsPromises.resolveTxt.mockRejectedValue(dnsError);

      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership('');

      expect(result.verified).toBe(false);
      expect(result.domain).toBe('');
      expect(result.error).toBe('Invalid domain name');
      expect(result.cached).toBe(false);
    });

    it('should handle invalid domain format by attempting DNS lookup', async () => {
      const invalidDomain = 'invalid..domain';
      const dnsError = new Error('Invalid domain name');
      mockDnsPromises.resolveTxt.mockRejectedValue(dnsError);

      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(invalidDomain);

      expect(result.verified).toBe(false);
      expect(result.domain).toBe(invalidDomain);
      expect(result.error).toBe('Invalid domain name');
      expect(result.cached).toBe(false);
    });

    it('should use cached result if available and not expired', async () => {
      mockDnsPromises.resolveTxt.mockResolvedValueOnce([[`${expectedTxtRecord}`]]);
      
      // First call - should perform DNS lookup and cache
      await dnsVerifier.verifyDomainOwnership(testDomain);
      expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(1);

      // Second call - should use cache
      const result: DNSVerificationResult = await dnsVerifier.verifyDomainOwnership(testDomain);
      expect(result.verified).toBe(true);
      expect(result.cached).toBe(true);
      expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(1); // Should not be called again
    });

    it('should not use cached result if expired', async () => {
        jest.useFakeTimers();
        mockDnsPromises.resolveTxt.mockResolvedValue([[`${expectedTxtRecord}`]]);

        // First call
        await dnsVerifier.verifyDomainOwnership(testDomain);
        expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(1);

        // Advance time past cache TTL
        jest.advanceTimersByTime(env.DNS_VERIFICATION_CACHE_TTL_MS + 1000);

        // Second call - should re-fetch
        await dnsVerifier.verifyDomainOwnership(testDomain);
        expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(2);
        
        const result = await dnsVerifier.verifyDomainOwnership(testDomain);
        expect(result.cached).toBe(true); // The third call will be cached again

        jest.useRealTimers();
    });
  });

  describe('clearCache', () => {
    it('should clear the verification cache', async () => {
        const testDomain1 = 'example1.com';
        const testDomain2 = 'example2.com';
        const expectedTxtRecord1 = `verify-ec=${testDomain1}`;
        const expectedTxtRecord2 = `verify-ec=${testDomain2}`;
        
        mockDnsPromises.resolveTxt
            .mockResolvedValueOnce([[expectedTxtRecord1]])
            .mockResolvedValueOnce([[expectedTxtRecord2]]);

        // Verify both domains to populate cache
        await dnsVerifier.verifyDomainOwnership(testDomain1);
        await dnsVerifier.verifyDomainOwnership(testDomain2);
        expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(2);

        // Clear cache
        dnsVerifier.clearCache();

        // Verify again - should make new DNS calls
        mockDnsPromises.resolveTxt
            .mockResolvedValueOnce([[expectedTxtRecord1]])
            .mockResolvedValueOnce([[expectedTxtRecord2]]);
        
        await dnsVerifier.verifyDomainOwnership(testDomain1);
        await dnsVerifier.verifyDomainOwnership(testDomain2);
        
        expect(mockDnsPromises.resolveTxt).toHaveBeenCalledTimes(4); // One for each domain after cache clear
    });
  });

  describe('Static Methods', () => {
    it('getExpectedTXTRecord should return correct format', () => {
        const domain = "example.com";
        expect(DNSVerifier.getExpectedTXTRecord(domain)).toBe(`verify-ec=${domain}`);
    });

    describe('isValidDomain', () => {
        const validDomains = [
            'example.com',
            'sub.example.co.uk',
            'test-domain.info',
            'a.io',
            'xn--ls8h.la', // IDN
            'domain123.com',
            '123domain.com', // Should be valid
            // Note: The actual regex allows trailing hyphens and long labels
            'example-.com', // Actually valid according to the regex
            'example.' + 'a'.repeat(64), // Actually valid according to the regex
        ];
        const invalidDomains = [
            'localhost', // No TLD in regex
            'example', // no TLD
            '.com', // no domain part
            'example.com/', // trailing slash
            'example..com', // double dot
            '-example.com', // leading hyphen
            'example.c', // TLD too short
            'a'.repeat(250) + '.com' // domain too long overall
        ];

        validDomains.forEach(domain => {
            it(`should return true for valid domain: ${domain}`, () => {
                expect(DNSVerifier.isValidDomain(domain)).toBe(true);
            });
        });

        invalidDomains.forEach(domain => {
            it(`should return false for invalid domain: ${domain}`, () => {
                expect(DNSVerifier.isValidDomain(domain)).toBe(false);
            });
        });
    });
  });
});
EOF

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

# Generate Prisma client (needed for tests)
echo "🔨 Generating Prisma client..."
npx prisma generate

# Verify installations
echo "✅ Verifying installations..."
node --version
npm --version
go version

echo "🎉 Setup complete! Ready to run tests."