{"include": ["src/frontend/**/*", "src/frontend/**/*.vue", "src/frontend/env.d.ts"], "exclude": ["src/backend/**/*", "dist/**/*", "node_modules/**/*"], "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/frontend/*"], "@components/*": ["./src/frontend/components/*"], "@composables/*": ["./src/frontend/composables/*"], "@stores/*": ["./src/frontend/stores/*"], "@types": ["./src/frontend/types/index.ts"], "@types/*": ["./src/frontend/types/*"], "@utils/*": ["./src/frontend/utils/*"]}}}